import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  Request,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { AttendanceService } from './attendance.service';
import { CreateAttendanceDto } from './dto/create-attendance.dto';
import { UpdateAttendanceDto } from './dto/update-attendance.dto';
import {
  AttendanceIdResponseDto,
  AttendanceResponseDto,
  PaginatedAttendanceResponseDto,
  DeleteAttendanceResponseDto,
  BulkDeleteAttendanceDto,
  BulkDeleteAttendanceResponseDto,
} from './dto/attendance-response.dto';
import { AttendanceStatus } from '../shared/types';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { ActivityMetadata } from '../auth/decorators/activity-metadata.decorator';
import { Permission } from '../shared/types/permission.enum';
import type { ActivityMetadata as ActivityMetadataType } from '../shared/types/activity-metadata.type';

@ApiTags('attendance')
@Controller('attendance')
@UseGuards(PermissionsGuard)
export class AttendanceController {
  constructor(private readonly attendanceService: AttendanceService) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.ATTENDANCE_CREATE)
  @ApiOperation({ summary: 'Create a new attendance record' })
  @ApiResponse({
    status: 201,
    description: 'The attendance record has been successfully created',
    type: AttendanceIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or missing required fields',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not found - Staff member not found',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Attendance record already exists for this date',
  })
  create(
    @Request() req,
    @Body() createAttendanceDto: CreateAttendanceDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<AttendanceIdResponseDto> {
    return this.attendanceService.create(
      req.user.id,
      req.user.activeBusinessId,
      createAttendanceDto,
      metadata,
    );
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.ATTENDANCE_READ)
  @ApiOperation({
    summary: 'Get all attendance records for the active business',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'search',
    description: 'Search by staff name',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'sortBy',
    description: 'Sort by field',
    required: false,
    type: String,
    enum: ['attendanceDate', 'staffName', 'status', 'createdAt'],
  })
  @ApiQuery({
    name: 'sortOrder',
    description: 'Sort order',
    required: false,
    enum: ['asc', 'desc'],
  })
  @ApiQuery({
    name: 'status',
    description: 'Filter by attendance status',
    required: false,
    enum: AttendanceStatus,
  })
  @ApiQuery({
    name: 'staffId',
    description: 'Filter by staff member ID',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'startDate',
    description: 'Filter by start date (YYYY-MM-DD)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'endDate',
    description: 'Filter by end date (YYYY-MM-DD)',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Attendance records retrieved successfully',
    type: PaginatedAttendanceResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Request() req,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('search') search?: string,
    @Query('sortBy') sortBy?: string,
    @Query('sortOrder') sortOrder?: 'asc' | 'desc',
    @Query('status') status?: AttendanceStatus,
    @Query('staffId') staffId?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ): Promise<PaginatedAttendanceResponseDto> {
    return this.attendanceService.findAll(
      req.user.activeBusinessId,
      page,
      limit,
      search,
      sortBy,
      sortOrder,
      status,
      staffId,
      startDate,
      endDate,
    );
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ATTENDANCE_READ)
  @ApiOperation({ summary: 'Get a specific attendance record by ID' })
  @ApiResponse({
    status: 200,
    description: 'Attendance record retrieved successfully',
    type: AttendanceResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not found - Attendance record not found',
  })
  async findOne(
    @Request() req,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<AttendanceResponseDto> {
    const data = await this.attendanceService.findOne(
      req.user.activeBusinessId,
      id,
    );
    return { data };
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ATTENDANCE_UPDATE)
  @ApiOperation({ summary: 'Update an attendance record' })
  @ApiResponse({
    status: 200,
    description: 'The attendance record has been successfully updated',
    type: AttendanceIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not found - Attendance record not found',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Attendance record already exists for this date',
  })
  update(
    @Request() req,
    @Param('id', ParseIntPipe) id: number,
    @Body() updateAttendanceDto: UpdateAttendanceDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<AttendanceIdResponseDto> {
    return this.attendanceService.update(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateAttendanceDto,
      metadata,
    );
  }

  @Delete('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ATTENDANCE_DELETE)
  @ApiOperation({ summary: 'Bulk delete attendance records' })
  @ApiBody({
    description: 'Array of attendance IDs to delete',
    type: BulkDeleteAttendanceDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Attendance records deleted successfully',
    type: BulkDeleteAttendanceResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - No attendance IDs provided',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not found - One or more attendance records not found',
  })
  bulkRemove(
    @Request() req,
    @Body() bulkDeleteAttendanceDto: BulkDeleteAttendanceDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkDeleteAttendanceResponseDto> {
    return this.attendanceService.bulkRemove(
      req.user.id,
      req.user.activeBusinessId,
      bulkDeleteAttendanceDto.attendanceIds,
      metadata,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ATTENDANCE_DELETE)
  @ApiOperation({ summary: 'Delete an attendance record' })
  @ApiResponse({
    status: 200,
    description: 'Attendance record deleted successfully',
    type: DeleteAttendanceResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not found - Attendance record not found',
  })
  remove(
    @Request() req,
    @Param('id', ParseIntPipe) id: number,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<DeleteAttendanceResponseDto> {
    return this.attendanceService.remove(
      req.user.id,
      req.user.activeBusinessId,
      id,
      metadata,
    );
  }
}
