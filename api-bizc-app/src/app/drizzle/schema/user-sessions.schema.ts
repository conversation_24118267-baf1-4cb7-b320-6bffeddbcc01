import {
  boolean,
  index,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uuid,
  integer,
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { users } from './users.schema';
import { ActivitySource } from '../../shared/types/activity.enum';

// Session status enum
export const sessionStatusEnum = pgEnum('session_status', [
  'ACTIVE',
  'EXPIRED',
  'REVOKED',
  'LOGGED_OUT',
]);

// Session source enum (reusing ActivitySource values)
export const sessionSourceEnum = pgEnum('session_source', [
  ActivitySource.WEB,
  ActivitySource.MOBILE,
  ActivitySource.API,
]);

// Device type enum for better categorization
export const deviceTypeEnum = pgEnum('device_type', [
  'DESKTOP',
  'MOBILE',
  'TABLET',
  'API_CLIENT',
  'UNKNOWN',
]);

/**
 * User Sessions Table
 *
 * Tracks active user sessions across different devices/clients.
 * Supports session management, security monitoring, and activity logging.
 */
export const userSessions = pgTable(
  'user_sessions',
  {
    // Primary identification
    id: uuid('id').defaultRandom().primaryKey(),
    sessionId: text('session_id').notNull().unique(), // External session ID
    userId: uuid('user_id')
      .references(() => users.id)
      .notNull(),

    // Session metadata
    source: sessionSourceEnum('source').notNull(),
    deviceType: deviceTypeEnum('device_type').notNull(),
    status: sessionStatusEnum('status').default('ACTIVE').notNull(),

    // Device/Client information
    userAgent: text('user_agent'),
    ipAddress: text('ip_address'),
    deviceId: text('device_id'), // Unique device identifier
    deviceInfo: text('device_info'), // JSON string with device details

    // Location information (optional)
    country: text('country'),
    city: text('city'),
    timezone: text('timezone'),

    // Session tracking
    firstSeenAt: timestamp('first_seen_at').defaultNow().notNull(),
    lastSeenAt: timestamp('last_seen_at').defaultNow().notNull(),
    expiresAt: timestamp('expires_at'), // Optional session expiry

    // Activity counters
    requestCount: integer('request_count').default(0).notNull(),
    lastActivityType: text('last_activity_type'), // Last action performed

    // Session termination
    endedAt: timestamp('ended_at'),
    endReason: text('end_reason'), // LOGOUT, TIMEOUT, REVOKED, etc.

    // Timestamps
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
  },
  (t) => ({
    // Primary indexes
    sessionIdIndex: index('user_sessions_session_id_index').on(t.sessionId),
    userIdIndex: index('user_sessions_user_id_index').on(t.userId),

    // Status and activity indexes
    statusIndex: index('user_sessions_status_index').on(t.status),
    lastSeenIndex: index('user_sessions_last_seen_index').on(t.lastSeenAt),
    expiresIndex: index('user_sessions_expires_index').on(t.expiresAt),

    // Security monitoring indexes
    ipAddressIndex: index('user_sessions_ip_address_index').on(t.ipAddress),
    deviceIdIndex: index('user_sessions_device_id_index').on(t.deviceId),

    // Composite indexes for common queries
    userStatusIndex: index('user_sessions_user_status_index').on(
      t.userId,
      t.status,
    ),
    userSourceIndex: index('user_sessions_user_source_index').on(
      t.userId,
      t.source,
    ),
    activeSessionsIndex: index('user_sessions_active_index').on(
      t.userId,
      t.status,
      t.lastSeenAt,
    ),
  }),
);

/**
 * Session Relations
 */
export const userSessionsRelations = relations(userSessions, ({ one }) => ({
  user: one(users, {
    fields: [userSessions.userId],
    references: [users.id],
  }),
}));

/**
 * Type exports
 */
export type UserSession = typeof userSessions.$inferSelect;
export type NewUserSession = typeof userSessions.$inferInsert;

/**
 * Session status type
 */
export type SessionStatus = (typeof sessionStatusEnum.enumValues)[number];

/**
 * Session source type
 */
export type SessionSource = (typeof sessionSourceEnum.enumValues)[number];

/**
 * Device type
 */
export type DeviceType = (typeof deviceTypeEnum.enumValues)[number];

/**
 * Session with user information
 */
export type SessionWithUser = UserSession & {
  user: {
    id: string;
    email: string;
    firstName: string | null;
    lastName: string | null;
    role: string;
    status: string;
  };
};

/**
 * Session creation data
 */
export interface CreateSessionData {
  sessionId: string;
  userId: string;
  source: ActivitySource;
  userAgent?: string;
  ipAddress?: string;
  deviceId?: string;
  deviceInfo?: any;
  country?: string;
  city?: string;
  timezone?: string;
  expiresAt?: Date;
}

/**
 * Session update data
 */
export interface UpdateSessionData {
  lastSeenAt?: Date;
  requestCount?: number;
  lastActivityType?: string;
  ipAddress?: string;
  country?: string;
  city?: string;
}

/**
 * Session query filters
 */
export interface SessionQueryFilters {
  userId?: string;
  status?: SessionStatus;
  source?: SessionSource;
  deviceType?: DeviceType;
  ipAddress?: string;
  deviceId?: string;
  activeSince?: Date;
  expiredBefore?: Date;
  country?: string;
}

/**
 * Session statistics
 */
export interface SessionStats {
  totalSessions: number;
  activeSessions: number;
  expiredSessions: number;
  revokedSessions: number;
  bySource: Record<SessionSource, number>;
  byDeviceType: Record<DeviceType, number>;
  uniqueDevices: number;
  uniqueIPs: number;
  averageSessionDuration: number; // in minutes
}

/**
 * Device information structure
 */
export interface DeviceInfo {
  os?: string;
  browser?: string;
  version?: string;
  platform?: string;
  mobile?: boolean;
  screenResolution?: string;
  language?: string;
}
