import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateAllowanceTypeDto } from './dto/create-allowance-type.dto';
import { UpdateAllowanceTypeDto } from './dto/update-allowance-type.dto';
import { AllowanceTypeDto } from './dto/allowance-type.dto';
import { AllowanceTypeSlimDto } from './dto/allowance-type-slim.dto';
import { AllowanceTypeListDto } from './dto/allowance-type-list.dto';
import { allowanceTypes } from '../drizzle/schema/allowance-types.schema';
import {
  eq,
  and,
  or,
  ilike,
  sql,
  gte,
  lte,
  asc,
  desc,
  inArray,
} from 'drizzle-orm';
import { users } from '../drizzle/schema/users.schema';
import { ActivityLogService } from '../activity-log/activity-log.service';
import {
  EntityType,
  ActivityType,
  ExecutionStrategy,
  ActivitySource,
} from '../shared/types/activity.enum';
import { UsersService } from '../users/users.service';
import { ActivityMetadata } from '../shared/types/activity-metadata.type';

@Injectable()
export class AllowanceTypesService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly usersService: UsersService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createAllowanceTypeDto: CreateAllowanceTypeDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const existingAllowanceType = await this.db
        .select()
        .from(allowanceTypes)
        .where(
          and(
            eq(allowanceTypes.businessId, businessId),
            ilike(
              allowanceTypes.allowanceCode,
              createAllowanceTypeDto.allowanceCode,
            ),
            eq(allowanceTypes.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingAllowanceType) {
        throw new ConflictException(
          `An allowance type with the code '${createAllowanceTypeDto.allowanceCode}' already exists for this business`,
        );
      }

      const [newAllowanceType] = await this.db
        .insert(allowanceTypes)
        .values({
          businessId,
          allowanceName: createAllowanceTypeDto.allowanceName,
          allowanceCode: createAllowanceTypeDto.allowanceCode,
          calculationMethod: createAllowanceTypeDto.calculationMethod,
          isTaxable: createAllowanceTypeDto.isTaxable ?? true,
          taxRateId: createAllowanceTypeDto.taxRateId,
          amount: createAllowanceTypeDto.amount?.toString(),
          isActive: createAllowanceTypeDto.isActive ?? true,
          isEPFETFEligible: createAllowanceTypeDto.isEPFETFEligible ?? false,
          description: createAllowanceTypeDto.description,
          createdBy: userId,
        })
        .returning();

      await this.activityLogService.logCreate(
        newAllowanceType.id,
        EntityType.ALLOWANCE_TYPE,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id: newAllowanceType.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create allowance type: ${error.message}`,
      );
    }
  }

  async findAll(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
  ): Promise<{
    data: AllowanceTypeDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    const whereConditions = [
      eq(allowanceTypes.isDeleted, false),
      eq(allowanceTypes.businessId, businessId),
    ];

    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(allowanceTypes.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(allowanceTypes.createdAt, toDate));
      }
    }

    const result = await this.db
      .select()
      .from(allowanceTypes)
      .where(and(...whereConditions))
      .orderBy(asc(allowanceTypes.allowanceName))
      .limit(limit)
      .offset(offset);

    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(allowanceTypes)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    // Note: Skipping view activity logging for performance reasons
    // await this.activityLogService.logCreate(...);

    return {
      data: await Promise.all(
        result.map((allowanceType) =>
          this.mapToAllowanceTypeDto(allowanceType),
        ),
      ),
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findAllOptimized(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    allowanceName?: string,
    allowanceCode?: string,
    calculationMethod?: string,
    isTaxable?: string,
    isActive?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: AllowanceTypeListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      eq(allowanceTypes.isDeleted, false),
      eq(allowanceTypes.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(allowanceTypes.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(allowanceTypes.createdAt, toDate));
      }
    }

    // Add individual filter conditions
    if (allowanceName) {
      whereConditions.push(
        ilike(allowanceTypes.allowanceName, `%${allowanceName}%`),
      );
    }

    if (allowanceCode) {
      whereConditions.push(
        ilike(allowanceTypes.allowanceCode, `%${allowanceCode}%`),
      );
    }

    if (calculationMethod) {
      whereConditions.push(
        eq(allowanceTypes.calculationMethod, calculationMethod as any),
      );
    }

    if (isTaxable) {
      const boolValue = isTaxable.toLowerCase() === 'true';
      whereConditions.push(eq(allowanceTypes.isTaxable, boolValue));
    }

    if (isActive) {
      const boolValue = isActive.toLowerCase() === 'true';
      whereConditions.push(eq(allowanceTypes.isActive, boolValue));
    }

    // Add advanced filters if provided
    if (filters) {
      try {
        const parsedFilters = JSON.parse(filters);
        const filterConditions = [];

        for (const filter of parsedFilters) {
          const { id: fieldId, value, operator } = filter;

          if (fieldId === 'allowanceName') {
            switch (operator) {
              case 'iLike':
                filterConditions.push(
                  ilike(allowanceTypes.allowanceName, `%${value}%`),
                );
                break;
              case 'notILike':
                filterConditions.push(
                  sql`NOT ${ilike(allowanceTypes.allowanceName, `%${value}%`)}`,
                );
                break;
              case 'eq':
                filterConditions.push(eq(allowanceTypes.allowanceName, value));
                break;
              case 'ne':
                filterConditions.push(
                  sql`${allowanceTypes.allowanceName} != ${value}`,
                );
                break;
              case 'isEmpty':
                filterConditions.push(
                  sql`${allowanceTypes.allowanceName} IS NULL OR ${allowanceTypes.allowanceName} = ''`,
                );
                break;
              case 'isNotEmpty':
                filterConditions.push(
                  sql`${allowanceTypes.allowanceName} IS NOT NULL AND ${allowanceTypes.allowanceName} != ''`,
                );
                break;
            }
          } else if (fieldId === 'allowanceCode') {
            switch (operator) {
              case 'iLike':
                filterConditions.push(
                  ilike(allowanceTypes.allowanceCode, `%${value}%`),
                );
                break;
              case 'notILike':
                filterConditions.push(
                  sql`NOT ${ilike(allowanceTypes.allowanceCode, `%${value}%`)}`,
                );
                break;
              case 'eq':
                filterConditions.push(eq(allowanceTypes.allowanceCode, value));
                break;
              case 'ne':
                filterConditions.push(
                  sql`${allowanceTypes.allowanceCode} != ${value}`,
                );
                break;
              case 'isEmpty':
                filterConditions.push(
                  sql`${allowanceTypes.allowanceCode} IS NULL OR ${allowanceTypes.allowanceCode} = ''`,
                );
                break;
              case 'isNotEmpty':
                filterConditions.push(
                  sql`${allowanceTypes.allowanceCode} IS NOT NULL AND ${allowanceTypes.allowanceCode} != ''`,
                );
                break;
            }
          } else if (fieldId === 'calculationMethod') {
            switch (operator) {
              case 'eq':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    inArray(allowanceTypes.calculationMethod, value),
                  );
                } else {
                  filterConditions.push(
                    eq(allowanceTypes.calculationMethod, value),
                  );
                }
                break;
              case 'ne':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    sql`${allowanceTypes.calculationMethod} NOT IN (${value.map((s) => `'${s}'`).join(',')})`,
                  );
                } else {
                  filterConditions.push(
                    sql`${allowanceTypes.calculationMethod} != ${value}`,
                  );
                }
                break;
            }
          } else if (fieldId === 'isTaxable') {
            switch (operator) {
              case 'eq':
                if (Array.isArray(value)) {
                  const boolValues = value.map(
                    (v) => v === 'true' || v === true,
                  );
                  if (boolValues.includes(true) && boolValues.includes(false)) {
                    // Both true and false, no filtering needed
                  } else if (boolValues.includes(true)) {
                    filterConditions.push(eq(allowanceTypes.isTaxable, true));
                  } else if (boolValues.includes(false)) {
                    filterConditions.push(eq(allowanceTypes.isTaxable, false));
                  }
                } else {
                  const boolValue = value === 'true' || value === true;
                  filterConditions.push(
                    eq(allowanceTypes.isTaxable, boolValue),
                  );
                }
                break;
              case 'ne': {
                const boolValue = value === 'true' || value === true;
                filterConditions.push(eq(allowanceTypes.isTaxable, !boolValue));
                break;
              }
            }
          } else if (fieldId === 'isActive') {
            switch (operator) {
              case 'eq':
                if (Array.isArray(value)) {
                  const boolValues = value.map(
                    (v) => v === 'true' || v === true,
                  );
                  if (boolValues.includes(true) && boolValues.includes(false)) {
                    // Both true and false, no filtering needed
                  } else if (boolValues.includes(true)) {
                    filterConditions.push(eq(allowanceTypes.isActive, true));
                  } else if (boolValues.includes(false)) {
                    filterConditions.push(eq(allowanceTypes.isActive, false));
                  }
                } else {
                  const boolValue = value === 'true' || value === true;
                  filterConditions.push(eq(allowanceTypes.isActive, boolValue));
                }
                break;
              case 'ne': {
                const boolValue = value === 'true' || value === true;
                filterConditions.push(eq(allowanceTypes.isActive, !boolValue));
                break;
              }
            }
          }
        }

        if (filterConditions.length > 0) {
          if (joinOperator === 'or') {
            whereConditions.push(or(...filterConditions));
          } else {
            whereConditions.push(and(...filterConditions));
          }
        }
      } catch {
        // Invalid JSON, ignore filters
      }
    }

    // Build sort conditions
    let orderBy = [asc(allowanceTypes.allowanceName), asc(allowanceTypes.id)];

    if (sort) {
      try {
        const parsedSort = JSON.parse(sort);
        if (parsedSort.length > 0) {
          const sortField = parsedSort[0];
          const isDesc = sortField.desc === true;

          switch (sortField.id) {
            case 'allowanceName':
              orderBy = [
                isDesc
                  ? desc(allowanceTypes.allowanceName)
                  : asc(allowanceTypes.allowanceName),
                asc(allowanceTypes.id),
              ];
              break;
            case 'allowanceCode':
              orderBy = [
                isDesc
                  ? desc(allowanceTypes.allowanceCode)
                  : asc(allowanceTypes.allowanceCode),
                asc(allowanceTypes.id),
              ];
              break;
            case 'createdAt':
              orderBy = [
                isDesc
                  ? desc(allowanceTypes.createdAt)
                  : asc(allowanceTypes.createdAt),
                asc(allowanceTypes.id),
              ];
              break;
            default:
              // Keep default sorting
              break;
          }
        }
      } catch {
        // Invalid JSON, keep default sorting
      }
    }

    const result = await this.db
      .select()
      .from(allowanceTypes)
      .where(and(...whereConditions))
      .orderBy(...orderBy)
      .limit(limit)
      .offset(offset);

    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(allowanceTypes)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    // Note: Skipping view activity logging for performance reasons
    // await this.activityLogService.logCreate(...);

    return {
      data: result.map((allowanceType) =>
        this.mapToAllowanceTypeListDto(allowanceType),
      ),
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async checkAllowanceCodeAvailability(
    _userId: string,
    businessId: string | null,
    allowanceCode: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const existingAllowanceType = await this.db
      .select()
      .from(allowanceTypes)
      .where(
        and(
          eq(allowanceTypes.businessId, businessId),
          ilike(allowanceTypes.allowanceCode, allowanceCode),
          eq(allowanceTypes.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    return { available: !existingAllowanceType };
  }

  async findOne(userId: string, id: string): Promise<AllowanceTypeDto> {
    const allowanceType = await this.db
      .select()
      .from(allowanceTypes)
      .where(
        and(eq(allowanceTypes.id, id), eq(allowanceTypes.isDeleted, false)),
      )
      .then((results) => results[0]);

    if (!allowanceType) {
      throw new NotFoundException(`Allowance type with ID ${id} not found`);
    }

    const user = await this.db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (
      !user ||
      !user.activeBusinessId ||
      user.activeBusinessId !== allowanceType.businessId
    ) {
      throw new UnauthorizedException('Access denied to this allowance type');
    }

    // Note: Skipping view activity logging for performance reasons
    // await this.activityLogService.logCreate(...);

    return await this.mapToAllowanceTypeDto(allowanceType);
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateAllowanceTypeDto: UpdateAllowanceTypeDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    const existingAllowanceType = await this.db
      .select()
      .from(allowanceTypes)
      .where(
        and(eq(allowanceTypes.id, id), eq(allowanceTypes.isDeleted, false)),
      )
      .then((results) => results[0]);

    if (!existingAllowanceType) {
      throw new NotFoundException(`Allowance type with ID ${id} not found`);
    }

    if (businessId !== existingAllowanceType.businessId) {
      throw new UnauthorizedException(
        'Access denied to update this allowance type',
      );
    }

    if (
      updateAllowanceTypeDto.allowanceCode &&
      updateAllowanceTypeDto.allowanceCode !==
        existingAllowanceType.allowanceCode
    ) {
      const codeConflict = await this.db
        .select()
        .from(allowanceTypes)
        .where(
          and(
            eq(allowanceTypes.businessId, businessId),
            ilike(
              allowanceTypes.allowanceCode,
              updateAllowanceTypeDto.allowanceCode,
            ),
            eq(allowanceTypes.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (codeConflict) {
        throw new ConflictException(
          `An allowance type with the code '${updateAllowanceTypeDto.allowanceCode}' already exists for this business`,
        );
      }
    }

    try {
      const updateData = {
        ...(updateAllowanceTypeDto.allowanceName && {
          allowanceName: updateAllowanceTypeDto.allowanceName,
        }),
        ...(updateAllowanceTypeDto.allowanceCode && {
          allowanceCode: updateAllowanceTypeDto.allowanceCode,
        }),
        ...(updateAllowanceTypeDto.calculationMethod && {
          calculationMethod: updateAllowanceTypeDto.calculationMethod,
        }),
        ...(updateAllowanceTypeDto.isTaxable !== undefined && {
          isTaxable: updateAllowanceTypeDto.isTaxable,
        }),
        ...(updateAllowanceTypeDto.taxRateId && {
          taxRateId: updateAllowanceTypeDto.taxRateId,
        }),
        ...(updateAllowanceTypeDto.amount !== undefined && {
          amount: updateAllowanceTypeDto.amount?.toString(),
        }),
        ...(updateAllowanceTypeDto.isActive !== undefined && {
          isActive: updateAllowanceTypeDto.isActive,
        }),
        ...(updateAllowanceTypeDto.isEPFETFEligible !== undefined && {
          isEPFETFEligible: updateAllowanceTypeDto.isEPFETFEligible,
        }),
        ...(updateAllowanceTypeDto.description !== undefined && {
          description: updateAllowanceTypeDto.description,
        }),
        updatedBy: userId,
        updatedAt: new Date(),
      };

      const [updatedAllowanceType] = await this.db
        .update(allowanceTypes)
        .set(updateData)
        .where(eq(allowanceTypes.id, id))
        .returning();

      await this.activityLogService.logUpdate(
        id,
        EntityType.ALLOWANCE_TYPE,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id: updatedAllowanceType.id,
      };
    } catch (error) {
      throw new BadRequestException(
        `Failed to update allowance type: ${error.message}`,
      );
    }
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ success: boolean; message: string }> {
    const existingAllowanceType = await this.db
      .select()
      .from(allowanceTypes)
      .where(
        and(eq(allowanceTypes.id, id), eq(allowanceTypes.isDeleted, false)),
      )
      .then((results) => results[0]);

    if (!existingAllowanceType) {
      throw new NotFoundException(`Allowance type with ID ${id} not found`);
    }

    if (businessId !== existingAllowanceType.businessId) {
      throw new UnauthorizedException(
        'Access denied to delete this allowance type',
      );
    }

    await this.db
      .update(allowanceTypes)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(allowanceTypes.id, id));

    await this.activityLogService.logDelete(
      id,
      EntityType.ALLOWANCE_TYPE,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return {
      success: true,
      message: `Allowance type with ID ${id} has been deleted`,
    };
  }

  async findAllSlim(
    userId: string,
    businessId: string | null,
  ): Promise<AllowanceTypeSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const allowanceTypeResults = await this.db
      .select({
        id: allowanceTypes.id,
        allowanceName: allowanceTypes.allowanceName,
        allowanceCode: allowanceTypes.allowanceCode,
        calculationMethod: allowanceTypes.calculationMethod,
        isTaxable: allowanceTypes.isTaxable,
        isActive: allowanceTypes.isActive,
      })
      .from(allowanceTypes)
      .where(
        and(
          eq(allowanceTypes.isDeleted, false),
          eq(allowanceTypes.businessId, businessId),
        ),
      )
      .orderBy(asc(allowanceTypes.allowanceName));

    // Note: Skipping view activity logging for performance reasons
    // await this.activityLogService.logCreate(...);

    return allowanceTypeResults.map((allowanceType) => ({
      id: allowanceType.id.toString(),
      allowanceName: allowanceType.allowanceName,
      allowanceCode: allowanceType.allowanceCode,
      calculationMethod: allowanceType.calculationMethod,
      isTaxable: allowanceType.isTaxable,
      isActive: allowanceType.isActive,
    }));
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createAllowanceTypeDto: CreateAllowanceTypeDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    const allowanceType = await this.create(
      userId,
      businessId,
      createAllowanceTypeDto,
      metadata,
    );
    return { id: allowanceType.id };
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateAllowanceTypeDto: UpdateAllowanceTypeDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    await this.update(userId, businessId, id, updateAllowanceTypeDto, metadata);
    return { id };
  }

  private async mapToAllowanceTypeDto(
    allowanceType: typeof allowanceTypes.$inferSelect,
  ): Promise<AllowanceTypeDto> {
    const createdByName = await this.usersService.getUserName(
      allowanceType.createdBy.toString(),
    );
    let updatedByName: string | undefined;
    if (allowanceType.updatedBy) {
      updatedByName = await this.usersService.getUserName(
        allowanceType.updatedBy.toString(),
      );
    }

    const allowanceTypeDto: AllowanceTypeDto = {
      id: allowanceType.id.toString(),
      businessId: allowanceType.businessId.toString(),
      allowanceName: allowanceType.allowanceName,
      allowanceCode: allowanceType.allowanceCode,
      calculationMethod: allowanceType.calculationMethod,
      isTaxable: allowanceType.isTaxable,
      taxRateId: allowanceType.taxRateId?.toString(),
      amount: allowanceType.amount
        ? parseFloat(allowanceType.amount)
        : undefined,
      isActive: allowanceType.isActive,
      isEPFETFEligible: allowanceType.isEPFETFEligible,
      description: allowanceType.description,
      createdBy: createdByName,
      updatedBy: updatedByName,
      createdAt: allowanceType.createdAt,
      updatedAt: allowanceType.updatedAt,
    };

    return allowanceTypeDto;
  }

  private mapToAllowanceTypeListDto(
    allowanceType: typeof allowanceTypes.$inferSelect,
  ): AllowanceTypeListDto {
    return {
      id: allowanceType.id.toString(),
      allowanceName: allowanceType.allowanceName,
      allowanceCode: allowanceType.allowanceCode,
      calculationMethod: allowanceType.calculationMethod,
      isTaxable: allowanceType.isTaxable,
      taxRateId: allowanceType.taxRateId?.toString(),
      amount: allowanceType.amount
        ? parseFloat(allowanceType.amount)
        : undefined,
      isActive: allowanceType.isActive,
      isEPFETFEligible: allowanceType.isEPFETFEligible,
      description: allowanceType.description,
      createdAt: allowanceType.createdAt,
      updatedAt: allowanceType.updatedAt,
    };
  }

  // Bulk operations
  async bulkCreate(
    userId: string,
    businessId: string | null,
    createAllowanceTypesDto: CreateAllowanceTypeDto[],
    metadata?: ActivityMetadata,
  ): Promise<AllowanceTypeDto[]> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!createAllowanceTypesDto || createAllowanceTypesDto.length === 0) {
        throw new BadRequestException(
          'No allowance types provided for creation',
        );
      }

      // Check for duplicate allowance codes within the request
      const codes = createAllowanceTypesDto.map((dto) =>
        dto.allowanceCode.toLowerCase(),
      );
      const duplicateCodes = codes.filter(
        (code, index) => codes.indexOf(code) !== index,
      );
      if (duplicateCodes.length > 0) {
        throw new ConflictException(
          `Duplicate allowance codes found in request: ${duplicateCodes.join(', ')}`,
        );
      }

      // Check for existing allowance codes in the database
      const existingAllowanceTypes = await this.db
        .select({
          allowanceCode: allowanceTypes.allowanceCode,
        })
        .from(allowanceTypes)
        .where(
          and(
            eq(allowanceTypes.businessId, businessId),
            inArray(sql`LOWER(${allowanceTypes.allowanceCode})`, codes),
            eq(allowanceTypes.isDeleted, false),
          ),
        );

      if (existingAllowanceTypes.length > 0) {
        const existingCodes = existingAllowanceTypes.map(
          (at) => at.allowanceCode,
        );
        throw new ConflictException(
          `The following allowance codes already exist: ${existingCodes.join(', ')}`,
        );
      }

      const createdAllowanceTypes: AllowanceTypeDto[] = [];
      const currentTime = new Date();

      // Use transaction to ensure all creations succeed or fail together
      await this.db.transaction(async (tx) => {
        for (const createAllowanceTypeDto of createAllowanceTypesDto) {
          const [newAllowanceType] = await tx
            .insert(allowanceTypes)
            .values({
              businessId,
              allowanceName: createAllowanceTypeDto.allowanceName,
              allowanceCode: createAllowanceTypeDto.allowanceCode,
              calculationMethod: createAllowanceTypeDto.calculationMethod,
              isTaxable: createAllowanceTypeDto.isTaxable ?? true,
              taxRateId: createAllowanceTypeDto.taxRateId,
              amount: createAllowanceTypeDto.amount?.toString(),
              isActive: createAllowanceTypeDto.isActive ?? true,
              isEPFETFEligible:
                createAllowanceTypeDto.isEPFETFEligible ?? false,
              description: createAllowanceTypeDto.description,
              createdBy: userId,
              createdAt: currentTime,
              updatedAt: currentTime,
            })
            .returning();

          const allowanceTypeDto =
            await this.mapToAllowanceTypeDto(newAllowanceType);
          createdAllowanceTypes.push(allowanceTypeDto);

          // Individual activity logging is handled by logBulkOperation below
        }
      });

      // Log bulk create operation
      const createdIds = createdAllowanceTypes.map((at) => at.id);
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_CREATE,
        EntityType.ALLOWANCE_TYPE,
        createdIds,
        {
          names: createAllowanceTypesDto.map((dto) => dto.allowanceName),
          isActive: true,
        },
        userId,
        businessId,
        {
          filterCriteria: { count: createAllowanceTypesDto.length },
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return createdAllowanceTypes;
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk create allowance types: ${error.message}`,
      );
    }
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createAllowanceTypesDto: CreateAllowanceTypeDto[],
    metadata?: ActivityMetadata,
  ): Promise<{ ids: string[] }> {
    const allowanceTypes = await this.bulkCreate(
      userId,
      businessId,
      createAllowanceTypesDto,
      metadata,
    );
    return { ids: allowanceTypes.map((allowanceType) => allowanceType.id) };
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    allowanceTypeIds: string[],
    metadata?: ActivityMetadata,
  ): Promise<{
    deleted: number;
    message: string;
    deletedIds: string[];
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!allowanceTypeIds || allowanceTypeIds.length === 0) {
        throw new BadRequestException(
          'No allowance type IDs provided for deletion',
        );
      }

      // Get all allowance types that exist and belong to the business
      const existingAllowanceTypes = await this.db
        .select({
          id: allowanceTypes.id,
          allowanceName: allowanceTypes.allowanceName,
          businessId: allowanceTypes.businessId,
        })
        .from(allowanceTypes)
        .where(
          and(
            inArray(allowanceTypes.id, allowanceTypeIds),
            eq(allowanceTypes.businessId, businessId),
            eq(allowanceTypes.isDeleted, false),
          ),
        );

      if (existingAllowanceTypes.length === 0) {
        throw new NotFoundException(
          'No allowance types found for the provided IDs',
        );
      }

      const deletedIds: string[] = [];
      const currentTime = new Date();

      // Use transaction to ensure all deletions succeed or fail together
      await this.db.transaction(async (tx) => {
        for (const allowanceType of existingAllowanceTypes) {
          // Soft delete the allowance type
          await tx
            .update(allowanceTypes)
            .set({
              isDeleted: true,
              updatedBy: userId,
              updatedAt: currentTime,
            })
            .where(eq(allowanceTypes.id, allowanceType.id));

          deletedIds.push(allowanceType.id);

          // Individual activity logging is handled by logBulkOperation below
        }
      });

      // Log bulk delete operation
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_DELETE,
        EntityType.ALLOWANCE_TYPE,
        deletedIds,
        { isDeleted: true, updatedBy: userId },
        userId,
        businessId,
        {
          filterCriteria: { allowanceTypeIds },
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        deleted: deletedIds.length,
        message: `Successfully deleted ${deletedIds.length} allowance types`,
        deletedIds,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk delete allowance types: ${error.message}`,
      );
    }
  }

  async bulkUpdateAllowanceTypeStatus(
    userId: string,
    businessId: string | null,
    allowanceTypeIds: string[],
    isActive: boolean,
    metadata?: ActivityMetadata,
  ): Promise<{
    updated: number;
    updatedIds: string[];
    failed: Array<{ allowanceTypeId: string; error: string }>;
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!allowanceTypeIds || allowanceTypeIds.length === 0) {
        throw new BadRequestException(
          'No allowance type IDs provided for status update',
        );
      }

      let updatedCount = 0;
      const updatedIds: string[] = [];
      const failed: Array<{ allowanceTypeId: string; error: string }> = [];

      // Use transaction to ensure consistency
      await this.db.transaction(async (tx) => {
        for (const allowanceTypeId of allowanceTypeIds) {
          try {
            // Check if the allowance type exists and belongs to the business
            const existingAllowanceType = await tx
              .select({
                id: allowanceTypes.id,
                allowanceName: allowanceTypes.allowanceName,
                isActive: allowanceTypes.isActive,
              })
              .from(allowanceTypes)
              .where(
                and(
                  eq(allowanceTypes.id, allowanceTypeId),
                  eq(allowanceTypes.businessId, businessId),
                  eq(allowanceTypes.isDeleted, false),
                ),
              )
              .then((results) => results[0]);

            if (!existingAllowanceType) {
              failed.push({
                allowanceTypeId,
                error: 'Allowance type not found or access denied',
              });
              continue;
            }

            // Update the allowance type status
            await tx
              .update(allowanceTypes)
              .set({
                isActive,
                updatedAt: new Date(),
              })
              .where(
                and(
                  eq(allowanceTypes.id, allowanceTypeId),
                  eq(allowanceTypes.businessId, businessId),
                  eq(allowanceTypes.isDeleted, false),
                ),
              );

            updatedCount++;
            updatedIds.push(allowanceTypeId);

            // Individual activity logging is handled by logBulkOperation below
          } catch (error) {
            failed.push({
              allowanceTypeId,
              error: `Failed to update: ${error.message}`,
            });
          }
        }
      });

      // Log bulk status change operation if any allowance types were updated
      if (updatedIds.length > 0) {
        await this.activityLogService.logBulkOperation(
          ActivityType.BULK_STATUS_CHANGE,
          EntityType.ALLOWANCE_TYPE,
          updatedIds,
          { isActive },
          userId,
          businessId,
          {
            filterCriteria: { allowanceTypeIds, targetStatus: isActive },
            failures: failed.map((f) => ({
              id: f.allowanceTypeId,
              error: f.error,
            })),
            executionStrategy: ExecutionStrategy.SEQUENTIAL,
            source: metadata?.source || ActivitySource.WEB,
            ipAddress: metadata?.ipAddress,
            userAgent: metadata?.userAgent,
            sessionId: metadata?.sessionId,
          },
        );
      }

      return {
        updated: updatedCount,
        updatedIds,
        failed,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update allowance type status: ${error.message}`,
      );
    }
  }
}
