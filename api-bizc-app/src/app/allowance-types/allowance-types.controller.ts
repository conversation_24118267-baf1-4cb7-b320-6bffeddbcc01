import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  Query,
  UseGuards,
} from '@nestjs/common';
import { AllowanceTypesService } from './allowance-types.service';
import { CreateAllowanceTypeDto } from './dto/create-allowance-type.dto';
import { UpdateAllowanceTypeDto } from './dto/update-allowance-type.dto';
import { AllowanceTypeDto } from './dto/allowance-type.dto';
import { AllowanceTypeSlimDto } from './dto/allowance-type-slim.dto';
import { AllowanceTypeIdResponseDto } from './dto/allowance-type-id-response.dto';
import { DeleteAllowanceTypeResponseDto } from './dto/delete-allowance-type-response.dto';
import { PaginatedAllowanceTypesListResponseDto } from './dto/paginated-allowance-types-list-response.dto';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiParam,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { AllowanceTypeCodeAvailabilityResponseDto } from './dto/check-allowance-type-code.dto';
import { BulkCreateAllowanceTypeDto } from './dto/bulk-create-allowance-type.dto';
import { BulkAllowanceTypeIdsResponseDto } from './dto/bulk-allowance-type-ids-response.dto';
import { BulkDeleteAllowanceTypeDto } from './dto/bulk-delete-allowance-type.dto';
import { BulkDeleteAllowanceTypeResponseDto } from './dto/bulk-delete-allowance-type-response.dto';
import {
  BulkUpdateAllowanceTypeStatusDto,
  BulkUpdateAllowanceTypeStatusResponseDto,
} from './dto/bulk-update-allowance-type-status.dto';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { ActivityMetadata } from '../auth/decorators/activity-metadata.decorator';
import { Permission } from '../shared/types/permission.enum';
import type { ActivityMetadata as ActivityMetadataType } from '../shared/types/activity-metadata.type';

@ApiTags('allowance-types')
@Controller('allowance-types')
@UseGuards(PermissionsGuard)
export class AllowanceTypesController {
  constructor(private readonly allowanceTypesService: AllowanceTypesService) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.ALLOWANCE_TYPE_CREATE)
  @ApiOperation({ summary: 'Create a new allowance type' })
  @ApiResponse({
    status: 201,
    description: 'The allowance type has been successfully created',
    type: AllowanceTypeIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Allowance type code already exists',
  })
  create(
    @Request() req,
    @Body() createAllowanceTypeDto: CreateAllowanceTypeDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<AllowanceTypeIdResponseDto> {
    return this.allowanceTypesService.createAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      createAllowanceTypeDto,
      metadata,
    );
  }

  @Post('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ALLOWANCE_TYPE_CREATE)
  @ApiOperation({ summary: 'Bulk create allowance types' })
  @ApiResponse({
    status: 201,
    description: 'Allowance types have been successfully created',
    type: BulkAllowanceTypeIdsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Allowance type codes already exist',
  })
  bulkCreate(
    @Request() req,
    @Body() bulkCreateAllowanceTypeDto: BulkCreateAllowanceTypeDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkAllowanceTypeIdsResponseDto> {
    return this.allowanceTypesService.bulkCreateAndReturnIds(
      req.user.id,
      req.user.activeBusinessId,
      bulkCreateAllowanceTypeDto.allowanceTypes,
      metadata,
    );
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.ALLOWANCE_TYPE_READ)
  @ApiOperation({
    summary: 'Get all allowance types for the active business',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'from',
    description: 'Filter from date (YYYY-MM-DD format)',
    required: false,
    type: String,
    example: '2025-01-01',
  })
  @ApiQuery({
    name: 'to',
    description: 'Filter to date (YYYY-MM-DD format)',
    required: false,
    type: String,
    example: '2025-12-31',
  })
  @ApiQuery({
    name: 'allowanceName',
    description: 'Filter by allowance name',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'allowanceCode',
    description: 'Filter by allowance code',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'calculationMethod',
    description: 'Filter by calculation method',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'isTaxable',
    description: 'Filter by taxable status (true/false)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'isActive',
    description: 'Filter by active status (true/false)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'filters',
    description: 'Advanced filters in JSON format',
    required: false,
    type: String,
    example: '[{"id":"allowanceName","value":"Bonus","operator":"iLike"}]',
  })
  @ApiQuery({
    name: 'joinOperator',
    description: 'Join operator for filters (and/or)',
    required: false,
    enum: ['and', 'or'],
  })
  @ApiQuery({
    name: 'sort',
    description: 'Sort configuration in JSON format',
    required: false,
    type: String,
    example: '[{"id":"allowanceName","desc":false}]',
  })
  @ApiResponse({
    status: 200,
    description:
      "Returns all allowance types for the user's active business with pagination",
    type: PaginatedAllowanceTypesListResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Request() req,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('allowanceName') allowanceName?: string,
    @Query('allowanceCode') allowanceCode?: string,
    @Query('calculationMethod') calculationMethod?: string,
    @Query('isTaxable') isTaxable?: string,
    @Query('isActive') isActive?: string,
    @Query('filters') filters?: string,
    @Query('joinOperator') joinOperator?: 'and' | 'or',
    @Query('sort') sort?: string,
  ): Promise<PaginatedAllowanceTypesListResponseDto> {
    return this.allowanceTypesService.findAllOptimized(
      req.user.id,
      req.user.activeBusinessId,
      page ? parseInt(page.toString()) : undefined,
      limit ? parseInt(limit.toString()) : undefined,
      from,
      to,
      allowanceName,
      allowanceCode,
      calculationMethod,
      isTaxable,
      isActive,
      filters,
      joinOperator,
      sort,
    );
  }

  @Get('check-availability')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ALLOWANCE_TYPE_READ)
  @ApiOperation({ summary: 'Check if an allowance type code is available' })
  @ApiQuery({
    name: 'allowanceCode',
    description: 'Allowance type code to check',
    required: true,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Returns whether the allowance type code is available',
    type: AllowanceTypeCodeAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkAllowanceCodeAvailability(
    @Request() req,
    @Query('allowanceCode') allowanceCode: string,
  ): Promise<{ available: boolean }> {
    return this.allowanceTypesService.checkAllowanceCodeAvailability(
      req.user.id,
      req.user.activeBusinessId,
      allowanceCode,
    );
  }

  @Get('slim')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ALLOWANCE_TYPE_READ)
  @ApiOperation({ summary: 'Get all allowance types in slim format' })
  @ApiResponse({
    status: 200,
    description: 'All allowance types returned successfully',
    type: [AllowanceTypeSlimDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllSlim(@Request() req): Promise<AllowanceTypeSlimDto[]> {
    return this.allowanceTypesService.findAllSlim(
      req.user.id,
      req.user.activeBusinessId,
    );
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ALLOWANCE_TYPE_READ)
  @ApiOperation({ summary: 'Get an allowance type by ID' })
  @ApiParam({
    name: 'id',
    description: 'Allowance type ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the allowance type',
    type: AllowanceTypeDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Allowance type not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to this allowance type',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findOne(@Request() req, @Param('id') id: string): Promise<AllowanceTypeDto> {
    return this.allowanceTypesService.findOne(req.user.id, id);
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ALLOWANCE_TYPE_UPDATE)
  @ApiOperation({ summary: 'Update an allowance type' })
  @ApiParam({
    name: 'id',
    description: 'Allowance type ID',
  })
  @ApiResponse({
    status: 200,
    description: 'The allowance type has been successfully updated',
    type: AllowanceTypeIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 404,
    description: 'Allowance type not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to this allowance type',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Allowance type code already exists',
  })
  update(
    @Request() req,
    @Param('id') id: string,
    @Body() updateAllowanceTypeDto: UpdateAllowanceTypeDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<AllowanceTypeIdResponseDto> {
    return this.allowanceTypesService.updateAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateAllowanceTypeDto,
      metadata,
    );
  }

  @Delete('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ALLOWANCE_TYPE_DELETE)
  @ApiOperation({ summary: 'Bulk delete allowance types' })
  @ApiResponse({
    status: 200,
    description: 'Allowance types have been successfully deleted',
    type: BulkDeleteAllowanceTypeResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or allowance types not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'One or more allowance types not found',
  })
  async bulkDelete(
    @Request() req,
    @Body() bulkDeleteAllowanceTypeDto: BulkDeleteAllowanceTypeDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkDeleteAllowanceTypeResponseDto> {
    return this.allowanceTypesService.bulkDelete(
      req.user.id,
      req.user.activeBusinessId,
      bulkDeleteAllowanceTypeDto.allowanceTypeIds,
      metadata,
    );
  }

  @Patch('bulk-status')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ALLOWANCE_TYPE_UPDATE)
  @ApiOperation({ summary: 'Bulk update allowance type status' })
  @ApiResponse({
    status: 200,
    description: 'Allowance type status has been successfully updated',
    type: BulkUpdateAllowanceTypeStatusResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or allowance types not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async bulkUpdateStatus(
    @Request() req,
    @Body() bulkUpdateStatusDto: BulkUpdateAllowanceTypeStatusDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkUpdateAllowanceTypeStatusResponseDto> {
    const result =
      await this.allowanceTypesService.bulkUpdateAllowanceTypeStatus(
        req.user.id,
        req.user.activeBusinessId,
        bulkUpdateStatusDto.allowanceTypeIds,
        bulkUpdateStatusDto.isActive,
        metadata,
      );

    return {
      updated: result.updated,
      message: `Successfully updated status for ${result.updated} allowance types`,
      updatedIds: result.updatedIds,
      failed: result.failed.length > 0 ? result.failed : undefined,
    };
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ALLOWANCE_TYPE_DELETE)
  @ApiOperation({ summary: 'Delete an allowance type' })
  @ApiParam({
    name: 'id',
    description: 'Allowance type ID',
  })
  @ApiResponse({
    status: 200,
    description: 'The allowance type has been successfully deleted',
    type: DeleteAllowanceTypeResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Allowance type not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to delete this allowance type',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  remove(
    @Request() req,
    @Param('id') id: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<DeleteAllowanceTypeResponseDto> {
    return this.allowanceTypesService.remove(
      req.user.id,
      req.user.activeBusinessId,
      id,
      metadata,
    );
  }
}
