import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiParam,
  ApiBearerAuth,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { BillOfMaterialsService } from './bill-of-materials.service';
import { CreateBillOfMaterialsDto } from './dto/create-bill-of-materials.dto';
import { UpdateBillOfMaterialsDto } from './dto/update-bill-of-materials.dto';
import {
  BillOfMaterialsDto,
  BillOfMaterialsSlimDto,
} from './dto/bill-of-materials.dto';
import { BillOfMaterialsIdResponseDto } from './dto/bill-of-materials-id-response.dto';
import { BulkBillOfMaterialsIdsResponseDto } from './dto/bulk-bill-of-materials-ids-response.dto';
import { BulkCreateBillOfMaterialsDto } from './dto/bulk-create-bill-of-materials.dto';
import { DeleteBillOfMaterialsResponseDto } from './dto/delete-bill-of-materials-response.dto';
import { BulkDeleteBillOfMaterialsDto } from './dto/bulk-delete-bill-of-materials.dto';
import { BulkDeleteBillOfMaterialsResponseDto } from './dto/bulk-delete-bill-of-materials-response.dto';
import { PaginatedBillOfMaterialsResponseDto } from './dto/paginated-bill-of-materials-response.dto';
import {
  BulkUpdateBillOfMaterialsStatusDto,
  BulkUpdateBillOfMaterialsStatusResponseDto,
} from './dto/bulk-update-bill-of-materials-status.dto';
import { BomCodeAvailabilityResponseDto } from './dto/check-bom-code.dto';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { ActivityMetadata } from '../auth/decorators/activity-metadata.decorator';
import { Permission } from '../shared/types/permission.enum';
import type { ActivityMetadata as ActivityMetadataType } from '../shared/types/activity-metadata.type';

@ApiTags('bill-of-materials')
@Controller('bill-of-materials')
@UseGuards(PermissionsGuard)
export class BillOfMaterialsController {
  constructor(
    private readonly billOfMaterialsService: BillOfMaterialsService,
  ) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.BILL_OF_MATERIALS_CREATE)
  @ApiOperation({ summary: 'Create a new bill of materials' })
  @ApiBody({
    description: 'Bill of materials creation data',
    type: CreateBillOfMaterialsDto,
  })
  @ApiResponse({
    status: 201,
    description: 'The bill of materials has been successfully created',
    type: BillOfMaterialsIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - BOM code already exists',
  })
  create(
    @Request() req: any,
    @Body() createBillOfMaterialsDto: CreateBillOfMaterialsDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BillOfMaterialsIdResponseDto> {
    return this.billOfMaterialsService.createAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      createBillOfMaterialsDto,
      metadata,
    );
  }

  @Post('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.BILL_OF_MATERIALS_CREATE)
  @ApiOperation({ summary: 'Bulk create bill of materials' })
  @ApiBody({
    description: 'Bulk bill of materials creation data',
    type: BulkCreateBillOfMaterialsDto,
  })
  @ApiResponse({
    status: 201,
    description: 'The bill of materials have been successfully created',
    type: BulkBillOfMaterialsIdsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or duplicate BOM codes',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - BOM codes already exist',
  })
  bulkCreate(
    @Request() req: any,
    @Body() bulkCreateBillOfMaterialsDto: BulkCreateBillOfMaterialsDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkBillOfMaterialsIdsResponseDto> {
    return this.billOfMaterialsService.bulkCreateAndReturnIds(
      req.user.id,
      req.user.activeBusinessId,
      bulkCreateBillOfMaterialsDto.billOfMaterials,
      metadata,
    );
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.BILL_OF_MATERIALS_READ)
  @ApiOperation({
    summary: 'Get all bill of materials for the active business',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'from',
    description: 'Filter from date (YYYY-MM-DD format)',
    required: false,
    type: String,
    example: '2025-05-24',
  })
  @ApiQuery({
    name: 'to',
    description: 'Filter to date (YYYY-MM-DD format)',
    required: false,
    type: String,
    example: '2025-05-24',
  })
  @ApiQuery({
    name: 'bomCode',
    description: 'Filter by BOM code',
    required: false,
    type: String,
    example: 'BOM-001',
  })
  @ApiQuery({
    name: 'bomName',
    description: 'Filter by BOM name',
    required: false,
    type: String,
    example: 'Laptop Assembly',
  })
  @ApiQuery({
    name: 'status',
    description:
      'Filter by status (comma-separated for multiple values). Supports URL encoding: status=active%2Cinactive',
    required: false,
    type: String,
    example: 'active,draft',
  })
  @ApiQuery({
    name: 'bomType',
    description:
      'Filter by BOM type (comma-separated for multiple values). Supports URL encoding: bomType=manufacturing%2Cassembly',
    required: false,
    type: String,
    example: 'manufacturing,assembly',
  })
  @ApiQuery({
    name: 'filters',
    description:
      'Advanced filters as JSON string with operator support. Supported operators: iLike (contains), notILike (does not contain), eq (is), ne (is not), isEmpty (is empty), isNotEmpty (is not empty)',
    required: false,
    type: String,
    example:
      '[{"id":"bomName","value":"Assembly","operator":"iLike","type":"text","rowId":"1"},{"id":"status","value":"active","operator":"eq","type":"select","rowId":"2"}]',
  })
  @ApiQuery({
    name: 'joinOperator',
    description: 'Join operator for advanced filters',
    required: false,
    type: String,
    enum: ['and', 'or'],
    example: 'and',
  })
  @ApiQuery({
    name: 'sort',
    description:
      'Sort configuration as JSON string. Supported fields: bomCode, bomName, createdAt, updatedAt',
    required: false,
    type: String,
    example: '[{"id":"bomCode","desc":false}]',
  })
  @ApiResponse({
    status: 200,
    description:
      "Returns all bill of materials for the user's active business with pagination",
    type: PaginatedBillOfMaterialsResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Request() req: any,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('bomCode') bomCode?: string,
    @Query('bomName') bomName?: string,
    @Query('status') status?: string,
    @Query('bomType') bomType?: string,
    @Query('filters') filters?: string,
    @Query('joinOperator') joinOperator?: 'and' | 'or',
    @Query('sort') sort?: string,
  ): Promise<PaginatedBillOfMaterialsResponseDto> {
    return this.billOfMaterialsService.findAllOptimized(
      req.user.id,
      req.user.activeBusinessId,
      page ? parseInt(page.toString()) : undefined,
      limit ? parseInt(limit.toString()) : undefined,
      from,
      to,
      bomCode,
      bomName,
      status,
      bomType,
      filters,
      joinOperator,
      sort,
    );
  }

  @Get('check-bom-code-availability')
  @ApiBearerAuth()
  @RequirePermissions(Permission.BILL_OF_MATERIALS_READ)
  @ApiOperation({ summary: 'Check if a BOM code is available' })
  @ApiQuery({
    name: 'bomCode',
    description: 'BOM code to check',
    required: true,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Returns whether the BOM code is available',
    type: BomCodeAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkBomCodeAvailability(
    @Request() req: any,
    @Query('bomCode') bomCode: string,
  ): Promise<{ available: boolean }> {
    return this.billOfMaterialsService.checkBomCodeAvailability(
      req.user.id,
      req.user.activeBusinessId,
      bomCode,
    );
  }

  @Get('slim')
  @ApiBearerAuth()
  @RequirePermissions(Permission.BILL_OF_MATERIALS_READ)
  @ApiOperation({ summary: 'Get all bill of materials in slim format' })
  @ApiResponse({
    status: 200,
    description: 'All bill of materials returned successfully',
    type: [BillOfMaterialsSlimDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllSlim(@Request() req: any): Promise<BillOfMaterialsSlimDto[]> {
    return this.billOfMaterialsService.findAllSlim(
      req.user.id,
      req.user.activeBusinessId,
    );
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.BILL_OF_MATERIALS_READ)
  @ApiOperation({ summary: 'Get a bill of materials by ID' })
  @ApiParam({
    name: 'id',
    description: 'Bill of materials ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the bill of materials with components',
    type: BillOfMaterialsDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Bill of materials not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to this bill of materials',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findOne(
    @Request() req: any,
    @Param('id') id: string,
  ): Promise<BillOfMaterialsDto> {
    return this.billOfMaterialsService.findOne(req.user.id, id);
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.BILL_OF_MATERIALS_UPDATE)
  @ApiOperation({ summary: 'Update a bill of materials' })
  @ApiParam({
    name: 'id',
    description: 'Bill of materials ID',
  })
  @ApiBody({
    description: 'Bill of materials update data',
    type: UpdateBillOfMaterialsDto,
  })
  @ApiResponse({
    status: 200,
    description: 'The bill of materials has been successfully updated',
    type: BillOfMaterialsIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 404,
    description: 'Bill of materials not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to this bill of materials',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - BOM code already exists',
  })
  update(
    @Request() req: any,
    @Param('id') id: string,
    @Body() updateBillOfMaterialsDto: UpdateBillOfMaterialsDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BillOfMaterialsIdResponseDto> {
    return this.billOfMaterialsService.updateAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateBillOfMaterialsDto,
      metadata,
    );
  }

  @Delete('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.BILL_OF_MATERIALS_DELETE)
  @ApiOperation({ summary: 'Bulk delete bill of materials' })
  @ApiBody({
    description: 'Array of bill of materials IDs to delete',
    type: BulkDeleteBillOfMaterialsDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Bill of materials have been successfully deleted',
    type: BulkDeleteBillOfMaterialsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or bill of materials not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'One or more bill of materials not found',
  })
  async bulkDelete(
    @Request() req: any,
    @Body() bulkDeleteBillOfMaterialsDto: BulkDeleteBillOfMaterialsDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkDeleteBillOfMaterialsResponseDto> {
    return this.billOfMaterialsService.bulkDelete(
      req.user.id,
      req.user.activeBusinessId,
      bulkDeleteBillOfMaterialsDto.billOfMaterialsIds,
      metadata,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.BILL_OF_MATERIALS_DELETE)
  @ApiOperation({ summary: 'Delete a bill of materials' })
  @ApiParam({
    name: 'id',
    description: 'Bill of materials ID',
  })
  @ApiResponse({
    status: 200,
    description: 'The bill of materials has been successfully deleted',
    type: DeleteBillOfMaterialsResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Bill of materials not found',
  })
  @ApiResponse({
    status: 401,
    description:
      'Unauthorized - Access denied to delete this bill of materials',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  remove(
    @Request() req: any,
    @Param('id') id: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<DeleteBillOfMaterialsResponseDto> {
    return this.billOfMaterialsService.remove(
      req.user.id,
      req.user.activeBusinessId,
      id,
      metadata,
    );
  }

  @Patch('bulk-status')
  @ApiBearerAuth()
  @RequirePermissions(Permission.BILL_OF_MATERIALS_UPDATE)
  @ApiOperation({ summary: 'Bulk update bill of materials status' })
  @ApiBody({
    description: 'Array of bill of materials IDs and status to update',
    type: BulkUpdateBillOfMaterialsStatusDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Bill of materials status has been successfully updated',
    type: BulkUpdateBillOfMaterialsStatusResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or bill of materials not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async bulkUpdateStatus(
    @Request() req: any,
    @Body() bulkUpdateStatusDto: BulkUpdateBillOfMaterialsStatusDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkUpdateBillOfMaterialsStatusResponseDto> {
    const result = await this.billOfMaterialsService.bulkUpdateBomStatus(
      req.user.id,
      req.user.activeBusinessId,
      bulkUpdateStatusDto.billOfMaterialsIds,
      bulkUpdateStatusDto.status,
      metadata,
    );

    return {
      updated: result.updated,
      message: `Successfully updated status for ${result.updated} bill of materials`,
      updatedIds: result.updatedIds,
      failed: result.failed.length > 0 ? result.failed : undefined,
    };
  }
}
