import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsEnum, IsNotEmpty, IsUUID } from 'class-validator';
import { AccommodationUnitStatus } from '../../shared/types';

export class BulkUpdateAccommodationUnitStatusDto {
  @ApiProperty({
    description: 'Array of accommodation unit IDs to update',
    example: [
      '123e4567-e89b-12d3-a456-************',
      '123e4567-e89b-12d3-a456-************',
    ],
    type: [String],
  })
  @IsArray()
  @IsUUID('4', { each: true })
  @IsNotEmpty()
  accommodationUnitIds: string[];

  @ApiProperty({
    description: 'The system status to set for all accommodation units',
    enum: AccommodationUnitStatus,
    example: AccommodationUnitStatus.ACTIVE,
  })
  @IsEnum(AccommodationUnitStatus)
  @IsNotEmpty()
  systemStatus: AccommodationUnitStatus;
}

export class BulkUpdateAccommodationUnitStatusResponseDto {
  @ApiProperty({
    description: 'Number of accommodation units successfully updated',
    example: 5,
  })
  updated: number;

  @ApiProperty({
    description: 'Success message',
    example: 'Successfully updated status for 5 accommodation units',
  })
  message: string;

  @ApiProperty({
    description: 'Array of successfully updated accommodation unit IDs',
    example: [
      '123e4567-e89b-12d3-a456-************',
      '123e4567-e89b-12d3-a456-************',
    ],
    type: [String],
  })
  updatedIds: string[];

  @ApiProperty({
    description: 'Array of failed updates with error details',
    type: [Object],
    required: false,
    example: [
      {
        accommodationUnitId: '123e4567-e89b-12d3-a456-426614174002',
        error: 'Accommodation unit not found',
      },
    ],
  })
  failed?: Array<{ accommodationUnitId: string; error: string }>;
}
