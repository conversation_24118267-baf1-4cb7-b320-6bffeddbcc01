import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsUUID } from 'class-validator';

export class BulkDeleteAccommodationUnitDto {
  @ApiProperty({
    description: 'Array of accommodation unit IDs to delete',
    type: [String],
  })
  @IsArray()
  @IsUUID('4', { each: true })
  ids: string[];
}

export class BulkDeleteAccommodationUnitResponseDto {
  @ApiProperty({
    description: 'Array of deleted accommodation unit IDs',
    type: [String],
  })
  deletedIds: string[];

  @ApiProperty({ description: 'Success message' })
  message: string;

  @ApiProperty({ description: 'Number of accommodation units deleted' })
  count: number;

  @ApiProperty({ description: 'Whether the units are deleted' })
  isDeleted: boolean;
}
