import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  Query,
  UseGuards,
  UseInterceptors,
  UploadedFiles,
} from '@nestjs/common';
import {
  FilesInterceptor,
  FileFieldsInterceptor,
} from '@nestjs/platform-express';
import { AccommodationUnitsService } from './accommodation-units.service';
import { CreateAccommodationUnitDto } from './dto/create-accommodation-unit.dto';
import { UpdateAccommodationUnitDto } from './dto/update-accommodation-unit.dto';
import { AccommodationUnitDto } from './dto/accommodation-unit.dto';
import { AccommodationUnitSlimDto } from './dto/accommodation-unit-slim.dto';
import { AccommodationUnitQueryDto } from './dto/accommodation-unit-query.dto';
import { AccommodationUnitIdResponseDto } from './dto/accommodation-unit-id-response.dto';
import {
  BulkCreateAccommodationUnitDto,
  BulkAccommodationUnitIdsResponseDto,
} from './dto/bulk-create-accommodation-unit.dto';
import { DeleteAccommodationUnitResponseDto } from './dto/delete-accommodation-unit-response.dto';
import {
  BulkDeleteAccommodationUnitDto,
  BulkDeleteAccommodationUnitResponseDto,
} from './dto/bulk-delete-accommodation-unit.dto';
import { PaginatedAccommodationUnitsResponseDto } from './dto/paginated-accommodation-units-response.dto';
import { AccommodationUnitAvailabilityResponseDto } from './dto/check-accommodation-unit-availability.dto';
import {
  UpdateAccommodationUnitGlobalPositionsDto,
  UpdateAccommodationUnitTypePositionsDto,
  UpdateAccommodationUnitSubTypePositionsDto,
  UpdateAccommodationUnitPositionsResponseDto,
} from './dto/update-accommodation-unit-positions.dto';
import {
  BulkUpdateAccommodationUnitDto,
  BulkUpdateAccommodationUnitResponseDto,
} from './dto/bulk-update-accommodation-unit.dto';
import {
  BulkUpdateAccommodationUnitStatusDto,
  BulkUpdateAccommodationUnitStatusResponseDto,
} from './dto/bulk-update-accommodation-unit-status.dto';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiParam,
  ApiBearerAuth,
  ApiQuery,
  ApiConsumes,
  ApiBody,
} from '@nestjs/swagger';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { ActivityMetadata } from '../auth/decorators/activity-metadata.decorator';
import { Permission } from '../shared/types/permission.enum';
import type { ActivityMetadata as ActivityMetadataType } from '../shared/types/activity-metadata.type';

interface AuthenticatedRequest {
  user: {
    id: string;
    activeBusinessId: string | null;
  };
}

@ApiTags('accommodation-units')
@Controller('accommodation-units')
@UseGuards(PermissionsGuard)
export class AccommodationUnitsController {
  constructor(
    private readonly accommodationUnitsService: AccommodationUnitsService,
  ) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOMMODATION_UNIT_CREATE)
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'images', maxCount: 10 },
      { name: 'ogImage', maxCount: 1 },
    ]),
  )
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Create a new accommodation unit',
    description:
      'Creates an accommodation unit with support for multiple images and OG image for SEO.',
  })
  @ApiResponse({
    status: 201,
    description: 'The accommodation unit has been successfully created',
    type: AccommodationUnitIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Room number or name already exists',
  })
  create(
    @Request() req: AuthenticatedRequest,
    @Body() createAccommodationUnitDto: CreateAccommodationUnitDto,
    @UploadedFiles()
    files?: { images?: Express.Multer.File[]; ogImage?: Express.Multer.File[] },
    @ActivityMetadata() metadata?: ActivityMetadataType,
  ): Promise<AccommodationUnitIdResponseDto> {
    const images = files?.images;
    const ogImage = files?.ogImage?.[0];

    return this.accommodationUnitsService.createAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      createAccommodationUnitDto,
      images,
      ogImage,
      metadata,
    );
  }

  @Post('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOMMODATION_UNIT_CREATE)
  @UseInterceptors(FilesInterceptor('images', 50))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Bulk create accommodation units' })
  @ApiBody({
    description: 'Array of accommodation units to create with optional images',
    type: BulkCreateAccommodationUnitDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Bulk creation completed',
    type: BulkAccommodationUnitIdsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async bulkCreate(
    @Request() req: AuthenticatedRequest,
    @Body() bulkCreateAccommodationUnitDto: BulkCreateAccommodationUnitDto,
    @UploadedFiles() images?: Express.Multer.File[],
    @ActivityMetadata() metadata?: ActivityMetadataType,
  ): Promise<BulkAccommodationUnitIdsResponseDto> {
    const result = await this.accommodationUnitsService.bulkCreateAndReturnIds(
      req.user.id,
      req.user.activeBusinessId,
      bulkCreateAccommodationUnitDto.accommodationUnits,
      images,
      metadata,
    );

    // Transform the service response to match the expected DTO
    const successfulResults = result.ids;

    return {
      ids: successfulResults,
      message: result.message,
      count: successfulResults.length,
    };
  }

  @Patch('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOMMODATION_UNIT_UPDATE)
  @ApiOperation({ summary: 'Bulk update accommodation units' })
  @ApiBody({
    description: 'Array of accommodation units to update',
    type: BulkUpdateAccommodationUnitDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Bulk update completed',
    type: BulkUpdateAccommodationUnitResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async bulkUpdate(
    @Request() req: AuthenticatedRequest,
    @Body() bulkUpdateAccommodationUnitDto: BulkUpdateAccommodationUnitDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkUpdateAccommodationUnitResponseDto> {
    const updatedIds: string[] = [];
    const errors: string[] = [];

    for (const item of bulkUpdateAccommodationUnitDto.accommodationUnits) {
      try {
        await this.accommodationUnitsService.update(
          req.user.id,
          req.user.activeBusinessId,
          item.id,
          item.data,
          undefined,
          undefined,
          metadata,
        );
        updatedIds.push(item.id);
      } catch (error) {
        errors.push(`Failed to update ${item.id}: ${error.message}`);
      }
    }

    return {
      updatedIds,
      message: `${updatedIds.length} accommodation units updated successfully${errors.length > 0 ? `, ${errors.length} failed` : ''}`,
      count: updatedIds.length,
    };
  }

  @Patch('batch/positions/global')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOMMODATION_UNIT_UPDATE_POSITIONS)
  @ApiOperation({ summary: 'Batch update accommodation unit global positions' })
  @ApiBody({
    description: 'Array of accommodation unit global position updates',
    type: UpdateAccommodationUnitGlobalPositionsDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Global positions updated successfully',
    type: UpdateAccommodationUnitPositionsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid position data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async updateGlobalPositions(
    @Request() req: AuthenticatedRequest,
    @Body() updatePositionsDto: UpdateAccommodationUnitGlobalPositionsDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<UpdateAccommodationUnitPositionsResponseDto> {
    // Handle both new and legacy formats
    const updates =
      updatePositionsDto.updates ||
      updatePositionsDto.positions?.map((p) => ({
        id: p.id,
        position: p.position,
      })) ||
      [];

    const result =
      await this.accommodationUnitsService.updateAccommodationUnitGlobalPositions(
        req.user.id,
        req.user.activeBusinessId,
        updates,
        metadata,
      );

    return {
      updated: result.updated,
      message: `Successfully updated ${result.updated} accommodation unit global positions`,
    };
  }

  @Patch('batch/positions/type')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOMMODATION_UNIT_UPDATE_POSITIONS)
  @ApiOperation({ summary: 'Batch update accommodation unit type positions' })
  @ApiBody({
    description: 'Array of accommodation unit type position updates',
    type: UpdateAccommodationUnitTypePositionsDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Type positions updated successfully',
    type: UpdateAccommodationUnitPositionsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid position data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async updateTypePositions(
    @Request() req: AuthenticatedRequest,
    @Body() updatePositionsDto: UpdateAccommodationUnitTypePositionsDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<UpdateAccommodationUnitPositionsResponseDto> {
    // Handle both new and legacy formats
    const updates =
      updatePositionsDto.updates ||
      updatePositionsDto.positions?.map((p) => ({
        id: p.id,
        position: p.position,
      })) ||
      [];

    const result =
      await this.accommodationUnitsService.updateAccommodationUnitTypePositions(
        req.user.id,
        req.user.activeBusinessId,
        updates,
        metadata,
      );

    return {
      updated: result.updated,
      message: `Successfully updated ${result.updated} accommodation unit type positions`,
    };
  }

  @Patch('batch/positions/sub-type')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOMMODATION_UNIT_UPDATE_POSITIONS)
  @ApiOperation({
    summary: 'Batch update accommodation unit sub-type positions',
  })
  @ApiBody({
    description: 'Array of accommodation unit sub-type position updates',
    type: UpdateAccommodationUnitSubTypePositionsDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Sub-type positions updated successfully',
    type: UpdateAccommodationUnitPositionsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid position data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async updateSubTypePositions(
    @Request() req: AuthenticatedRequest,
    @Body() updatePositionsDto: UpdateAccommodationUnitSubTypePositionsDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<UpdateAccommodationUnitPositionsResponseDto> {
    // Handle both new and legacy formats
    const updates =
      updatePositionsDto.updates ||
      updatePositionsDto.positions?.map((p) => ({
        id: p.id,
        position: p.position,
      })) ||
      [];

    const result =
      await this.accommodationUnitsService.updateAccommodationUnitSubTypePositions(
        req.user.id,
        req.user.activeBusinessId,
        updates,
        metadata,
      );

    return {
      updated: result.updated,
      message: `Successfully updated ${result.updated} accommodation unit sub-type positions`,
    };
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOMMODATION_UNIT_READ)
  @ApiOperation({
    summary: 'Get all accommodation units with pagination and filtering',
    description:
      'Returns paginated accommodation units with support for search, filtering, and sorting',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Items per page',
    required: false,
    type: Number,
    example: 20,
  })
  @ApiQuery({
    name: 'search',
    description: 'Search by room number, name, or description',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'typeId',
    description: 'Filter by type ID',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'status',
    description: 'Filter by room status',
    required: false,
    enum: [
      'available',
      'occupied',
      'reserved',
      'out_of_order',
      'maintenance',
      'cleaning',
      'dirty',
      'inspected',
      'blocked',
    ],
  })
  @ApiQuery({
    name: 'floor',
    description: 'Filter by floor number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'bedType',
    description: 'Filter by bed type',
    required: false,
    enum: [
      'single',
      'double',
      'queen',
      'king',
      'twin',
      'sofa_bed',
      'bunk_bed',
      'murphy_bed',
      'daybed',
      'futon',
    ],
  })
  @ApiQuery({
    name: 'viewType',
    description: 'Filter by view type',
    required: false,
    enum: [
      'ocean_view',
      'mountain_view',
      'city_view',
      'garden_view',
      'pool_view',
      'courtyard_view',
      'street_view',
      'interior_view',
      'partial_ocean_view',
      'partial_mountain_view',
      'balcony_view',
      'no_view',
    ],
  })
  @ApiQuery({
    name: 'minAdults',
    description: 'Filter by minimum adults capacity',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'maxAdults',
    description: 'Filter by maximum adults capacity',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'minPrice',
    description: 'Filter by minimum price',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'maxPrice',
    description: 'Filter by maximum price',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'sort',
    description:
      'Sort field and direction (e.g., "roomNumber:asc", "floor:desc")',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Accommodation units retrieved successfully',
    type: PaginatedAccommodationUnitsResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Request() req: AuthenticatedRequest,
    @Query() queryDto: AccommodationUnitQueryDto,
  ): Promise<PaginatedAccommodationUnitsResponseDto> {
    return this.accommodationUnitsService.findAll(
      req.user.activeBusinessId,
      queryDto,
    );
  }

  @Get('check-availability')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOMMODATION_UNIT_READ)
  @ApiOperation({ summary: 'Check if a room number or name is available' })
  @ApiQuery({
    name: 'roomNumber',
    description: 'Room number to check',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'name',
    description: 'Unit name to check',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'excludeId',
    description: 'Accommodation unit ID to exclude from check (for updates)',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Returns whether the room number or name is available',
    type: AccommodationUnitAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async checkAvailability(
    @Request() req: AuthenticatedRequest,
    @Query('roomNumber') roomNumber?: string,
    @Query('name') name?: string,
    @Query('excludeId') excludeId?: string,
  ): Promise<AccommodationUnitAvailabilityResponseDto> {
    if (roomNumber) {
      const result =
        await this.accommodationUnitsService.checkRoomNumberAvailability(
          req.user.activeBusinessId,
          roomNumber,
          excludeId,
        );
      return {
        available: result.available,
        roomNumber,
        message: result.available
          ? 'Room number is available'
          : 'Room number already exists',
      };
    }

    if (name) {
      const result = await this.accommodationUnitsService.checkNameAvailability(
        req.user.activeBusinessId,
        name,
        excludeId,
      );
      return {
        available: result.available,
        name,
        message: result.available
          ? 'Unit name is available'
          : 'Unit name already exists',
      };
    }

    return {
      available: false,
      message: 'Please provide either roomNumber or name parameter',
    };
  }

  @Get('slim')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOMMODATION_UNIT_READ)
  @ApiOperation({
    summary: 'Get all accommodation units in slim format',
    description:
      'Returns accommodation units with minimal information for dropdowns and quick lists',
  })
  @ApiResponse({
    status: 200,
    description: 'All accommodation units returned successfully in slim format',
    type: [AccommodationUnitSlimDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllSlim(
    @Request() req: AuthenticatedRequest,
  ): Promise<AccommodationUnitSlimDto[]> {
    return this.accommodationUnitsService.findAllSlim(
      req.user.activeBusinessId,
    );
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOMMODATION_UNIT_READ)
  @ApiOperation({
    summary: 'Get an accommodation unit by ID',
    description:
      'Returns detailed accommodation unit information including related entities',
  })
  @ApiParam({
    name: 'id',
    description: 'Accommodation unit ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the accommodation unit with complete details',
    type: AccommodationUnitDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Accommodation unit not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to this accommodation unit',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findOne(
    @Request() _req: AuthenticatedRequest,
    @Param('id') id: string,
  ): Promise<AccommodationUnitDto> {
    return this.accommodationUnitsService.findOne(id);
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOMMODATION_UNIT_UPDATE)
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'images', maxCount: 10 },
      { name: 'ogImage', maxCount: 1 },
    ]),
  )
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Update an accommodation unit with optional images',
    description:
      'Updates accommodation unit information with support for image uploads.',
  })
  @ApiParam({
    name: 'id',
    description: 'Accommodation unit ID',
  })
  @ApiResponse({
    status: 200,
    description: 'The accommodation unit has been successfully updated',
    type: AccommodationUnitIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 404,
    description: 'Accommodation unit not found',
  })
  @ApiResponse({
    status: 401,
    description:
      'Unauthorized - Access denied to update this accommodation unit',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Room number or name already exists',
  })
  update(
    @Request() req: AuthenticatedRequest,
    @Param('id') id: string,
    @Body() updateAccommodationUnitDto: UpdateAccommodationUnitDto,
    @UploadedFiles()
    files?: { images?: Express.Multer.File[]; ogImage?: Express.Multer.File[] },
    @ActivityMetadata() metadata?: ActivityMetadataType,
  ): Promise<AccommodationUnitIdResponseDto> {
    const images = files?.images;
    const ogImage = files?.ogImage?.[0];

    return this.accommodationUnitsService.updateAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateAccommodationUnitDto,
      images,
      ogImage,
      metadata,
    );
  }

  @Delete('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOMMODATION_UNIT_DELETE)
  @ApiOperation({ summary: 'Bulk delete accommodation units' })
  @ApiBody({
    description: 'Array of accommodation unit IDs to delete',
    type: BulkDeleteAccommodationUnitDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Accommodation units have been successfully deleted',
    type: BulkDeleteAccommodationUnitResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 404,
    description: 'Some accommodation units not found',
  })
  @ApiResponse({
    status: 401,
    description:
      'Unauthorized - Access denied to delete these accommodation units',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  bulkDelete(
    @Request() req: AuthenticatedRequest,
    @Body() bulkDeleteAccommodationUnitDto: BulkDeleteAccommodationUnitDto,
    @ActivityMetadata() metadata?: ActivityMetadataType,
  ): Promise<BulkDeleteAccommodationUnitResponseDto> {
    return this.accommodationUnitsService.bulkDelete(
      req.user.id,
      req.user.activeBusinessId,
      bulkDeleteAccommodationUnitDto.ids,
      metadata,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOMMODATION_UNIT_DELETE)
  @ApiOperation({ summary: 'Delete an accommodation unit' })
  @ApiParam({
    name: 'id',
    description: 'Accommodation unit ID',
  })
  @ApiResponse({
    status: 200,
    description: 'The accommodation unit has been successfully deleted',
    type: DeleteAccommodationUnitResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Accommodation unit not found',
  })
  @ApiResponse({
    status: 401,
    description:
      'Unauthorized - Access denied to delete this accommodation unit',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  remove(
    @Request() req: AuthenticatedRequest,
    @Param('id') id: string,
    @ActivityMetadata() metadata?: ActivityMetadataType,
  ): Promise<DeleteAccommodationUnitResponseDto> {
    return this.accommodationUnitsService.remove(
      req.user.id,
      req.user.activeBusinessId,
      id,
      metadata,
    );
  }

  @Patch('bulk-status')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOMMODATION_UNIT_UPDATE)
  @ApiOperation({ summary: 'Bulk update accommodation unit system status' })
  @ApiBody({
    description: 'Array of accommodation unit IDs and status to update',
    type: BulkUpdateAccommodationUnitStatusDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Accommodation unit status has been successfully updated',
    type: BulkUpdateAccommodationUnitStatusResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or accommodation units not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async bulkUpdateStatus(
    @Request() req: AuthenticatedRequest,
    @Body() bulkUpdateStatusDto: BulkUpdateAccommodationUnitStatusDto,
    @ActivityMetadata() metadata?: ActivityMetadataType,
  ): Promise<BulkUpdateAccommodationUnitStatusResponseDto> {
    const result =
      await this.accommodationUnitsService.bulkUpdateAccommodationUnitStatus(
        req.user.id,
        req.user.activeBusinessId,
        bulkUpdateStatusDto.accommodationUnitIds,
        bulkUpdateStatusDto.systemStatus,
        metadata,
      );

    return {
      updated: result.updated,
      message: `Successfully updated status for ${result.updated} accommodation units`,
      updatedIds: result.updatedIds,
      failed: result.failed.length > 0 ? result.failed : undefined,
    };
  }
}
