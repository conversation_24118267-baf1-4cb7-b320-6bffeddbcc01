import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { DemoAppointmentsController } from './demo-appointments.controller';
import { DemoAppointmentsService } from './demo-appointments.service';
import { DrizzleModule } from '@app/drizzle/drizzle.module';
import { ActivityLogModule } from '../activity-log/activity-log.module';

@Module({
  controllers: [DemoAppointmentsController],
  providers: [DemoAppointmentsService],
  imports: [DrizzleModule, ConfigModule, ActivityLogModule],
  exports: [DemoAppointmentsService],
})
export class DemoAppointmentsModule {}
