import {
  Injectable,
  Inject,
  NotFoundException,
  BadRequestException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { users } from '../drizzle/schema/users.schema';
import { CreateDemoAppointmentDto } from './dto/create-demo-appointment.dto';
import { UpdateDemoAppointmentDto } from './dto/update-demo-appointment.dto';
import { DemoAppointmentDto } from './dto/demo-appointment.dto';
import { and, eq, sql, ne, isNull } from 'drizzle-orm';
import { AppointmentStatus, MeetingType } from '../shared/types';
import { demoAppointments } from '@app/drizzle/schema/demo-appointments.schema';
import { ActivityMetadata } from '../shared/types/activity-metadata.type';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { EntityType, ActivitySource } from '../shared/types/activity.enum';

@Injectable()
export class DemoAppointmentsService {
  // Maximum number of appointments allowed per time slot
  private static readonly MAX_APPOINTMENTS_PER_SLOT = 3;

  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createDemoAppointmentDto: CreateDemoAppointmentDto,
    metadata?: ActivityMetadata,
  ): Promise<DemoAppointmentDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Convert Date to string for database storage
      const formattedDate = createDemoAppointmentDto.appointmentDate
        .toISOString()
        .split('T')[0];

      // Check if the time slot is available
      const availability = await this.checkAvailability(
        businessId,
        formattedDate,
        createDemoAppointmentDto.startTime,
        createDemoAppointmentDto.endTime,
      );

      if (!availability.available) {
        throw new ConflictException(
          `Time slot is fully booked. Maximum ${availability.maxAllowed} appointments allowed per slot. Current count: ${availability.currentCount}`,
        );
      }

      // Create new appointment
      const [newAppointment] = await this.db
        .insert(demoAppointments)
        .values({
          businessId: businessId,
          title: createDemoAppointmentDto.title,
          description: createDemoAppointmentDto.description,
          meetingType: createDemoAppointmentDto.meetingType,
          meetingLink: createDemoAppointmentDto.meetingLink,
          appointmentDate: formattedDate,
          startTime: createDemoAppointmentDto.startTime,
          endTime: createDemoAppointmentDto.endTime,
          durationMinutes: createDemoAppointmentDto.durationMinutes || '60',
          assignedTo: createDemoAppointmentDto.assignedTo,
          createdBy: userId,
        })
        .returning();

      // Log the demo appointment creation activity
      await this.activityLogService.logCreate(
        newAppointment.id,
        EntityType.APPOINTMENT,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return this.mapToDemoAppointmentDto(newAppointment);
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create appointment: ${error.message}`,
      );
    }
  }

  async findAll(
    businessId: string | null,
    page = 1,
    limit = 10,
  ): Promise<{
    data: DemoAppointmentDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Find all appointments for the user's active business with pagination
    const result = await this.db
      .select()
      .from(demoAppointments)
      .where(
        and(
          eq(demoAppointments.businessId, businessId),
          eq(demoAppointments.isCancelled, false),
        ),
      )
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(demoAppointments)
      .where(
        and(
          eq(demoAppointments.businessId, businessId),
          eq(demoAppointments.isCancelled, false),
        ),
      );

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    return {
      data: result.map((appointment) =>
        this.mapToDemoAppointmentDto(appointment),
      ),
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findOne(userId: string, id: string): Promise<DemoAppointmentDto> {
    // Get the appointment
    const appointment = await this.db
      .select()
      .from(demoAppointments)
      .where(eq(demoAppointments.id, id))
      .then((results) => results[0]);

    if (!appointment) {
      throw new NotFoundException(`Appointment with ID ${id} not found`);
    }

    // Get user's activeBusinessId to verify permission
    const user = await this.db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (
      !user ||
      !user.activeBusinessId ||
      user.activeBusinessId !== appointment.businessId
    ) {
      throw new UnauthorizedException('Access denied to this appointment');
    }

    return this.mapToDemoAppointmentDto(appointment);
  }

  async update(
    businessId: string | null,
    id: string,
    updateDemoAppointmentDto: UpdateDemoAppointmentDto,
    metadata?: ActivityMetadata,
  ): Promise<DemoAppointmentDto> {
    // Get the appointment
    const existingAppointment = await this.db
      .select()
      .from(demoAppointments)
      .where(eq(demoAppointments.id, id))
      .then((results) => results[0]);

    if (!existingAppointment) {
      throw new NotFoundException(`Appointment with ID ${id} not found`);
    }

    if (businessId !== existingAppointment.businessId) {
      throw new UnauthorizedException(
        'Access denied to update this appointment',
      );
    }

    // If changing date/time, check availability
    if (
      (updateDemoAppointmentDto.appointmentDate ||
        updateDemoAppointmentDto.startTime ||
        updateDemoAppointmentDto.endTime) &&
      !(
        (updateDemoAppointmentDto.appointmentDate
          ? updateDemoAppointmentDto.appointmentDate.toISOString().split('T')[0]
          : null) === existingAppointment.appointmentDate &&
        updateDemoAppointmentDto.startTime === existingAppointment.startTime &&
        updateDemoAppointmentDto.endTime === existingAppointment.endTime
      )
    ) {
      // Check availability for the new time slot
      const newDate = updateDemoAppointmentDto.appointmentDate
        ? updateDemoAppointmentDto.appointmentDate.toISOString().split('T')[0]
        : existingAppointment.appointmentDate;
      const newStartTime =
        updateDemoAppointmentDto.startTime || existingAppointment.startTime;
      const newEndTime =
        updateDemoAppointmentDto.endTime || existingAppointment.endTime;

      const availability = await this.checkAvailability(
        businessId,
        newDate,
        newStartTime,
        newEndTime,
        id, // Exclude current appointment from count
      );

      if (!availability.available) {
        throw new ConflictException(
          `Time slot is fully booked. Maximum ${availability.maxAllowed} appointments allowed per slot. Current count: ${availability.currentCount}`,
        );
      }
    }

    // Include rescheduledAt if changing date/time
    const isRescheduled =
      updateDemoAppointmentDto.appointmentDate ||
      updateDemoAppointmentDto.startTime ||
      updateDemoAppointmentDto.endTime;

    // Build the update object
    const updateData: Record<string, any> = {};

    if (updateDemoAppointmentDto.title) {
      updateData.title = updateDemoAppointmentDto.title;
    }

    if (updateDemoAppointmentDto.description !== undefined) {
      updateData.description = updateDemoAppointmentDto.description;
    }

    if (updateDemoAppointmentDto.meetingType) {
      updateData.meetingType = updateDemoAppointmentDto.meetingType;
    }

    if (updateDemoAppointmentDto.meetingLink !== undefined) {
      updateData.meetingLink = updateDemoAppointmentDto.meetingLink;
    }

    if (updateDemoAppointmentDto.appointmentDate) {
      updateData.appointmentDate = updateDemoAppointmentDto.appointmentDate
        .toISOString()
        .split('T')[0];
    }

    if (updateDemoAppointmentDto.startTime) {
      updateData.startTime = updateDemoAppointmentDto.startTime;
    }

    if (updateDemoAppointmentDto.endTime) {
      updateData.endTime = updateDemoAppointmentDto.endTime;
    }

    if (updateDemoAppointmentDto.durationMinutes) {
      updateData.durationMinutes = updateDemoAppointmentDto.durationMinutes;
    }

    if (updateDemoAppointmentDto.assignedTo !== undefined) {
      updateData.assignedTo = updateDemoAppointmentDto.assignedTo;
    }

    if (isRescheduled) {
      updateData.rescheduledAt = new Date();
    }

    updateData.updatedAt = new Date();

    const [updatedAppointment] = await this.db
      .update(demoAppointments)
      .set(updateData)
      .where(eq(demoAppointments.id, id))
      .returning();

    // Log the demo appointment update activity
    await this.activityLogService.logUpdate(
      id,
      EntityType.APPOINTMENT,
      existingAppointment.createdBy, // Use the original creator as the user
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return this.mapToDemoAppointmentDto(updatedAppointment);
  }

  async cancel(
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ success: boolean; message: string }> {
    // Get the appointment
    const appointment = await this.db
      .select()
      .from(demoAppointments)
      .where(eq(demoAppointments.id, id))
      .then((results) => results[0]);

    if (!appointment) {
      throw new NotFoundException(`Appointment with ID ${id} not found`);
    }

    if (businessId !== appointment.businessId) {
      throw new UnauthorizedException(
        'Access denied to cancel this appointment',
      );
    }

    // Cancel the appointment
    await this.db
      .update(demoAppointments)
      .set({
        status: AppointmentStatus.CANCELLED,
        isCancelled: true,
        cancelledAt: new Date(),
        updatedAt: new Date(),
      })
      .where(eq(demoAppointments.id, id));

    // Log the demo appointment cancellation activity
    await this.activityLogService.logUpdate(
      id,
      EntityType.APPOINTMENT,
      appointment.createdBy, // Use the original creator as the user
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return {
      success: true,
      message: `Appointment with ID ${id} has been cancelled`,
    };
  }

  /**
   * Check availability for a specific time slot
   * Now supports multiple appointments per time slot up to MAX_APPOINTMENTS_PER_SLOT
   */
  async checkAvailability(
    businessId: string | null,
    appointmentDate: string,
    startTime: string,
    endTime: string,
    excludeId?: string,
  ): Promise<{ available: boolean; currentCount: number; maxAllowed: number }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    try {
      // Count existing appointments for the same time slot
      const whereConditions = [
        eq(demoAppointments.businessId, businessId),
        eq(demoAppointments.appointmentDate, appointmentDate),
        eq(demoAppointments.startTime, startTime),
        eq(demoAppointments.endTime, endTime),
        eq(demoAppointments.isCancelled, false),
        isNull(demoAppointments.deletedAt),
      ];

      if (excludeId) {
        whereConditions.push(ne(demoAppointments.id, excludeId));
      }

      const existingAppointments = await this.db
        .select()
        .from(demoAppointments)
        .where(and(...whereConditions));

      const currentCount = existingAppointments.length;
      const available =
        currentCount < DemoAppointmentsService.MAX_APPOINTMENTS_PER_SLOT;

      return {
        available,
        currentCount,
        maxAllowed: DemoAppointmentsService.MAX_APPOINTMENTS_PER_SLOT,
      };
    } catch (error) {
      throw new BadRequestException(
        `Failed to check availability: ${error.message}`,
      );
    }
  }

  public mapToDemoAppointmentDto(
    appointment: typeof demoAppointments.$inferSelect,
  ): DemoAppointmentDto {
    return {
      id: appointment.id,
      businessId: appointment.businessId,
      title: appointment.title,
      description: appointment.description,
      meetingType: appointment.meetingType as MeetingType,
      meetingLink: appointment.meetingLink,
      appointmentDate: new Date(appointment.appointmentDate), // Convert string to Date for DTO
      startTime: appointment.startTime,
      endTime: appointment.endTime,
      durationMinutes: appointment.durationMinutes,
      status: appointment.status as AppointmentStatus,
      isCancelled: appointment.isCancelled,
      cancelReason: appointment.cancelReason,
      createdBy: appointment.createdBy,
      assignedTo: appointment.assignedTo,
      createdAt: appointment.createdAt,
      updatedAt: appointment.updatedAt,
      cancelledAt: appointment.cancelledAt,
      rescheduledAt: appointment.rescheduledAt,
      completedAt: appointment.completedAt,
    };
  }

  /**
   * Assign a user to a specific appointment
   */
  async assignUser(
    businessId: string | null,
    appointmentId: string,
    userId: string,
    metadata?: ActivityMetadata,
  ): Promise<DemoAppointmentDto> {
    try {
      // Get the appointment
      const existingAppointment = await this.db
        .select()
        .from(demoAppointments)
        .where(eq(demoAppointments.id, appointmentId))
        .then((results) => results[0]);

      if (!existingAppointment) {
        throw new NotFoundException(
          `Appointment with ID ${appointmentId} not found`,
        );
      }

      // Check if the user has access to this appointment (same business)
      if (businessId !== existingAppointment.businessId) {
        throw new UnauthorizedException(
          'Access denied to update this appointment',
        );
      }

      // Check if the assigned user exists
      const assignedUser = await this.db
        .select()
        .from(users)
        .where(eq(users.id, userId))
        .then((results) => results[0]);

      if (!assignedUser) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }

      // Update the appointment with the assigned user
      const [updatedAppointment] = await this.db
        .update(demoAppointments)
        .set({
          assignedTo: userId,
          updatedAt: new Date(),
        })
        .where(eq(demoAppointments.id, appointmentId))
        .returning();

      // Log the demo appointment assignment activity
      await this.activityLogService.logUpdate(
        appointmentId,
        EntityType.APPOINTMENT,
        existingAppointment.createdBy, // Use the original creator as the user
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return this.mapToDemoAppointmentDto(updatedAppointment);
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to assign user to appointment: ${error.message}`,
      );
    }
  }
}
