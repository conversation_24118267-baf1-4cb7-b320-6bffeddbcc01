import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  UseGuards,
  Query,
} from '@nestjs/common';
import { DemoAppointmentsService } from './demo-appointments.service';
import { CreateDemoAppointmentDto } from './dto/create-demo-appointment.dto';
import { UpdateDemoAppointmentDto } from './dto/update-demo-appointment.dto';
import { DemoAppointmentDto } from './dto/demo-appointment.dto';
import { AssignUserDto } from './dto/assign-user.dto';
import {
  CheckAppointmentAvailabilityDto,
  AppointmentAvailabilityResponseDto,
} from './dto/check-appointment-availability.dto';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiParam,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../users/types/users.type';
import { ActivityMetadata } from '../auth/decorators/activity-metadata.decorator';
import type { ActivityMetadata as ActivityMetadataType } from '../shared/types/activity-metadata.type';

@ApiTags('demo-appointments')
@Controller('demo-appointments')
export class DemoAppointmentsController {
  constructor(
    private readonly demoAppointmentsService: DemoAppointmentsService,
  ) {}

  @Post()
  @ApiBearerAuth()
  @ApiOperation({
    summary:
      'Create a demo appointment - supports multiple appointments per time slot (max 3)',
  })
  @ApiResponse({
    status: 201,
    description: 'The appointment has been successfully created',
    type: DemoAppointmentDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Time slot is fully booked',
  })
  create(
    @Request() req: any,
    @Body() createDemoAppointmentDto: CreateDemoAppointmentDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<DemoAppointmentDto> {
    return this.demoAppointmentsService.create(
      req.user.id,
      req.user.activeBusinessId,
      createDemoAppointmentDto,
      metadata,
    );
  }

  @Get()
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get all demo appointments for the active business',
  })
  @ApiResponse({
    status: 200,
    description: "Returns the business's demo appointments",
    type: [DemoAppointmentDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  async findAll(@Request() req: any): Promise<{
    data: DemoAppointmentDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    return this.demoAppointmentsService.findAll(req.user.activeBusinessId);
  }

  @Get('check-exists')
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Check if the business has existing demo appointments',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the count of existing appointments',
    schema: {
      properties: {
        exists: { type: 'boolean' },
        count: { type: 'number' },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  async checkExists(
    @Request() req: any,
  ): Promise<{ exists: boolean; count: number }> {
    if (!req.user.activeBusinessId) {
      throw new Error('No active business found for this user');
    }

    const result = await this.demoAppointmentsService.findAll(
      req.user.activeBusinessId,
    );

    return {
      exists: result.data.length > 0,
      count: result.data.length,
    };
  }

  @Get('availability/check')
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Check availability for a specific time slot',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns availability information for the time slot',
    type: AppointmentAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  async checkAvailability(
    @Request() req: any,
    @Query() checkAvailabilityDto: CheckAppointmentAvailabilityDto,
  ): Promise<AppointmentAvailabilityResponseDto> {
    return this.demoAppointmentsService.checkAvailability(
      req.user.activeBusinessId,
      checkAvailabilityDto.appointmentDate,
      checkAvailabilityDto.startTime,
      checkAvailabilityDto.endTime,
      checkAvailabilityDto.excludeId,
    );
  }

  @Get(':id')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get a demo appointment by ID' })
  @ApiParam({
    name: 'id',
    description: 'Appointment ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the appointment',
    type: DemoAppointmentDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Appointment not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to this appointment',
  })
  findOne(
    @Request() req: any,
    @Param('id') id: string,
  ): Promise<DemoAppointmentDto> {
    return this.demoAppointmentsService.findOne(req.user.id, id);
  }

  @Patch(':id')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update a demo appointment' })
  @ApiParam({
    name: 'id',
    description: 'Appointment ID',
  })
  @ApiResponse({
    status: 200,
    description: 'The appointment has been successfully updated',
    type: DemoAppointmentDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 404,
    description: 'Appointment not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to update this appointment',
  })
  update(
    @Request() req: any,
    @Param('id') id: string,
    @Body() updateDemoAppointmentDto: UpdateDemoAppointmentDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<DemoAppointmentDto> {
    return this.demoAppointmentsService.update(
      req.user.activeBusinessId,
      id,
      updateDemoAppointmentDto,
      metadata,
    );
  }

  @Patch(':id/assign-user')
  @ApiBearerAuth()
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Assign a user to a demo appointment (Admin only)' })
  @ApiParam({
    name: 'id',
    description: 'Appointment ID',
  })
  @ApiResponse({
    status: 200,
    description: 'The user has been successfully assigned to the appointment',
    type: DemoAppointmentDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Requires admin role',
  })
  @ApiResponse({
    status: 404,
    description: 'Appointment or User not found',
  })
  assignUser(
    @Request() req: any,
    @Param('id') id: string,
    @Body() assignUserDto: AssignUserDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<DemoAppointmentDto> {
    return this.demoAppointmentsService.assignUser(
      req.user.activeBusinessId,
      id,
      assignUserDto.userId,
      metadata,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Cancel a demo appointment' })
  @ApiParam({
    name: 'id',
    description: 'Appointment ID',
  })
  @ApiResponse({
    status: 200,
    description: 'The appointment has been successfully cancelled',
    schema: {
      properties: {
        success: { type: 'boolean', example: true },
        message: {
          type: 'string',
          example: 'Appointment with ID 1 has been cancelled',
        },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Appointment not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to cancel this appointment',
  })
  cancel(
    @Request() req: any,
    @Param('id') id: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ) {
    return this.demoAppointmentsService.cancel(
      req.user.activeBusinessId,
      id,
      metadata,
    );
  }
}
