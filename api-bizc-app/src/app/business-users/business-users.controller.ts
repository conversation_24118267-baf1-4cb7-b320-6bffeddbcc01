import {
  Controller,
  Get,
  Post,
  Body,
  Request,
  Query,
  UseGuards,
  Patch,
  Param,
  Delete,
} from '@nestjs/common';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiBearerAuth,
  ApiQuery,
  ApiBody,
  ApiParam,
} from '@nestjs/swagger';
import { BusinessUsersService } from './business-users.service';
import {
  InviteBusinessUserDto,
  InviteBusinessUserResponseDto,
} from './dto/invite-business-user.dto';
import { BusinessUserSlimDto } from './dto/business-user-slim.dto';
import { PaginatedBusinessUsersResponseDto } from './dto/paginated-business-users-response.dto';
import { DeleteBusinessUserResponseDto } from './dto/delete-business-user-response.dto';
import {
  BulkDeleteBusinessUserDto,
  BulkDeleteBusinessUserResponseDto,
} from './dto/bulk-delete-business-user.dto';
import {
  UpdateBusinessUserStatusDto,
  BulkUpdateBusinessUserStatusDto,
  BusinessUserIdResponseDto,
  BulkUpdateBusinessUserStatusResponseDto,
} from './dto/update-business-user-status.dto';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { Permission } from '../shared/types/permission.enum';
import { ActivityMetadata } from '../auth/decorators/activity-metadata.decorator';
import type { ActivityMetadata as ActivityMetadataType } from '../shared/types/activity-metadata.type';

@ApiTags('business-users')
@Controller('business-users')
@UseGuards(PermissionsGuard)
export class BusinessUsersController {
  constructor(private readonly businessUsersService: BusinessUsersService) {}

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.BUSINESS_USER_READ)
  @ApiOperation({
    summary: 'Get all business users for the active business',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'email',
    description: 'Filter by user email',
    required: false,
    type: String,
    example: '<EMAIL>',
  })
  @ApiQuery({
    name: 'role',
    description: 'Filter by business user role',
    required: false,
    type: String,
    example: 'manager',
  })
  @ApiQuery({
    name: 'status',
    description: 'Filter by business user status',
    required: false,
    type: String,
    example: 'active',
  })
  @ApiResponse({
    status: 200,
    description:
      'Returns all business users for the active business with pagination',
    type: PaginatedBusinessUsersResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Request() req,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('email') email?: string,
    @Query('role') role?: string,
    @Query('status') status?: string,
  ): Promise<PaginatedBusinessUsersResponseDto> {
    return this.businessUsersService.findAll(
      req.user.id,
      req.user.activeBusinessId,
      page ? parseInt(page.toString()) : undefined,
      limit ? parseInt(limit.toString()) : undefined,
      email,
      role,
      status,
    );
  }

  @Get('slim')
  @ApiBearerAuth()
  @RequirePermissions(Permission.BUSINESS_USER_READ)
  @ApiOperation({
    summary: 'Get all business users in slim format',
    description:
      'Returns a lightweight list of business users with essential information only',
  })
  @ApiResponse({
    status: 200,
    description: 'All business users returned successfully in slim format',
    type: [BusinessUserSlimDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllSlim(@Request() req): Promise<BusinessUserSlimDto[]> {
    return this.businessUsersService.findAllSlim(req.user.activeBusinessId);
  }

  @Post('invite')
  @ApiBearerAuth()
  @RequirePermissions(Permission.BUSINESS_USER_CREATE)
  @ApiOperation({
    summary: 'Invite a user to the business',
    description:
      'Invite a user by email to join the business with a specific role. If the user does not exist, a new user account will be created.',
  })
  @ApiBody({
    description: 'User invitation details',
    type: InviteBusinessUserDto,
  })
  @ApiResponse({
    status: 201,
    description: 'User has been successfully invited to the business',
    type: InviteBusinessUserResponseDto,
  })
  @ApiResponse({
    status: 400,
    description:
      'Bad request - Invalid data or business role ID required for CUSTOM role',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Business role not found or does not belong to this business',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - User is already part of this business',
  })
  inviteUser(
    @Request() req,
    @Body() inviteDto: InviteBusinessUserDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<InviteBusinessUserResponseDto> {
    return this.businessUsersService.inviteUser(
      req.user.id,
      req.user.activeBusinessId,
      inviteDto,
      metadata,
    );
  }

  @Patch(':id/status')
  @ApiBearerAuth()
  @RequirePermissions(Permission.BUSINESS_USER_UPDATE)
  @ApiOperation({
    summary: 'Update business user status',
    description: 'Update the status of a business user',
  })
  @ApiParam({
    name: 'id',
    description: 'Business user ID',
    type: String,
  })
  @ApiBody({
    description: 'New status for the business user',
    type: UpdateBusinessUserStatusDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Business user status updated successfully',
    type: BusinessUserIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Business user not found',
  })
  updateStatus(
    @Request() req,
    @Param('id') id: string,
    @Body() updateStatusDto: UpdateBusinessUserStatusDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BusinessUserIdResponseDto> {
    return this.businessUsersService.updateStatus(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateStatusDto,
      metadata,
    );
  }

  @Delete('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.BUSINESS_USER_DELETE)
  @ApiOperation({
    summary: 'Bulk delete business users',
    description: 'Delete multiple business users at once',
  })
  @ApiBody({
    description: 'Array of business user IDs to delete',
    type: BulkDeleteBusinessUserDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Business users successfully deleted',
    type: BulkDeleteBusinessUserResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or no IDs provided',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'No business users found to delete',
  })
  bulkRemove(
    @Request() req,
    @Body() bulkDeleteDto: BulkDeleteBusinessUserDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkDeleteBusinessUserResponseDto> {
    return this.businessUsersService.bulkRemove(
      req.user.id,
      req.user.activeBusinessId,
      bulkDeleteDto.ids,
      metadata,
    );
  }

  @Patch('bulk-status')
  @ApiBearerAuth()
  @RequirePermissions(Permission.BUSINESS_USER_UPDATE)
  @ApiOperation({
    summary: 'Bulk update business user status',
    description: 'Update the status of multiple business users at once',
  })
  @ApiBody({
    description: 'Array of business user IDs and new status to apply',
    type: BulkUpdateBusinessUserStatusDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Business user statuses successfully updated',
    type: BulkUpdateBusinessUserStatusResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or no IDs provided',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'No business users found to update',
  })
  bulkUpdateStatus(
    @Request() req,
    @Body() bulkUpdateStatusDto: BulkUpdateBusinessUserStatusDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkUpdateBusinessUserStatusResponseDto> {
    return this.businessUsersService.bulkUpdateStatus(
      req.user.id,
      req.user.activeBusinessId,
      bulkUpdateStatusDto.ids,
      bulkUpdateStatusDto.status,
      metadata,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.BUSINESS_USER_DELETE)
  @ApiOperation({
    summary: 'Delete a business user',
    description: 'Remove a user from the business',
  })
  @ApiParam({
    name: 'id',
    description: 'Business user ID',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Business user successfully deleted',
    type: DeleteBusinessUserResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Business user not found',
  })
  remove(
    @Request() req,
    @Param('id') id: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<DeleteBusinessUserResponseDto> {
    return this.businessUsersService.remove(
      req.user.id,
      req.user.activeBusinessId,
      id,
      metadata,
    );
  }
}
