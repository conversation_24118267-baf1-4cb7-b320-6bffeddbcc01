import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  Query,
  UseGuards,
  UseInterceptors,
  UploadedFiles,
} from '@nestjs/common';
import { FilesInterceptor } from '@nestjs/platform-express';
import { AssetTransactionsService } from './asset-transactions.service';
import { CreateAssetTransactionDto } from './dto/create-asset-transaction.dto';
import { UpdateAssetTransactionDto } from './dto/update-asset-transaction.dto';
import { AssetTransactionDto } from './dto/asset-transaction.dto';
import { AssetTransactionSlimDto } from './dto/asset-transaction-slim.dto';
import { AssetTransactionIdResponseDto } from './dto/asset-transaction-id-response.dto';
import { DeleteAssetTransactionResponseDto } from './dto/delete-asset-transaction-response.dto';
import { BulkDeleteAssetTransactionDto } from './dto/bulk-delete-asset-transaction.dto';
import { BulkDeleteAssetTransactionResponseDto } from './dto/bulk-delete-asset-transaction-response.dto';
import { PaginatedAssetTransactionsResponseDto } from './dto/paginated-asset-transactions-response.dto';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiParam,
  ApiBearerAuth,
  ApiQuery,
  ApiConsumes,
  ApiBody,
} from '@nestjs/swagger';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { ActivityMetadata } from '../auth/decorators/activity-metadata.decorator';
import { Permission } from '../shared/types/permission.enum';
import type { ActivityMetadata as ActivityMetadataType } from '../shared/types/activity-metadata.type';

@ApiTags('asset-transactions')
@Controller('asset-transactions')
@UseGuards(PermissionsGuard)
export class AssetTransactionsController {
  constructor(
    private readonly assetTransactionsService: AssetTransactionsService,
  ) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_TRANSACTION_CREATE)
  @UseInterceptors(FilesInterceptor('attachments'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Create a new asset transaction with optional attachments',
  })
  @ApiBody({
    description:
      'Asset transaction creation data with optional file attachments',
    type: CreateAssetTransactionDto,
  })
  @ApiResponse({
    status: 201,
    description: 'The asset transaction has been successfully created',
    type: AssetTransactionIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Asset transaction reference number already exists',
  })
  create(
    @Request() req,
    @Body() createAssetTransactionDto: CreateAssetTransactionDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
    @UploadedFiles() attachments?: Express.Multer.File[],
  ): Promise<AssetTransactionIdResponseDto> {
    return this.assetTransactionsService.createAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      createAssetTransactionDto,
      metadata,
      attachments,
    );
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_TRANSACTION_READ)
  @ApiOperation({
    summary: 'Get all asset transactions for the active business',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'from',
    description: 'Start date for filtering (ISO string)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'to',
    description: 'End date for filtering (ISO string)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'refNo',
    description: 'Filter by reference number',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'transactionType',
    description: 'Filter by transaction type',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'assetId',
    description: 'Filter by asset ID',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'receiver',
    description: 'Filter by receiver staff member ID',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'filters',
    description: 'Advanced filters as JSON string',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'joinOperator',
    description: 'Join operator for advanced filters',
    required: false,
    enum: ['and', 'or'],
  })
  @ApiQuery({
    name: 'sort',
    description:
      'Sort field and direction (e.g., "refNo:asc", "createdAt:desc")',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Returns paginated list of asset transactions',
    type: PaginatedAssetTransactionsResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Request() req,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('refNo') refNo?: string,
    @Query('transactionType') transactionType?: string,
    @Query('assetId') assetId?: string,
    @Query('receiver') receiver?: string,
    @Query('filters') filters?: string,
    @Query('joinOperator') joinOperator?: 'and' | 'or',
    @Query('sort') sort?: string,
  ): Promise<PaginatedAssetTransactionsResponseDto> {
    return this.assetTransactionsService.findAll(
      req.user.id,
      req.user.activeBusinessId,
      page || 1,
      limit || 10,
      from,
      to,
      refNo,
      transactionType,
      assetId,
      receiver,
      filters,
      joinOperator,
      sort,
    );
  }

  @Get('slim')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_TRANSACTION_READ)
  @ApiOperation({
    summary: 'Get all asset transactions in slim format for dropdowns',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns list of asset transactions in slim format',
    type: [AssetTransactionSlimDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllSlim(@Request() req): Promise<AssetTransactionSlimDto[]> {
    return this.assetTransactionsService.findAllSlim(
      req.user.id,
      req.user.activeBusinessId,
    );
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_TRANSACTION_READ)
  @ApiOperation({ summary: 'Get an asset transaction by ID' })
  @ApiParam({
    name: 'id',
    description: 'Asset transaction ID',
    type: String,
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the asset transaction with the specified ID',
    type: AssetTransactionDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Asset transaction not found',
  })
  findOne(
    @Request() req,
    @Param('id') id: string,
  ): Promise<AssetTransactionDto> {
    return this.assetTransactionsService.findOne(
      req.user.id,
      req.user.activeBusinessId,
      id,
    );
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_TRANSACTION_UPDATE)
  @UseInterceptors(FilesInterceptor('attachments'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Update an asset transaction with optional attachments',
  })
  @ApiParam({
    name: 'id',
    description: 'Asset transaction ID',
    type: String,
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiBody({
    description: 'Asset transaction update data with optional file attachments',
    type: UpdateAssetTransactionDto,
  })
  @ApiResponse({
    status: 200,
    description: 'The asset transaction has been successfully updated',
    type: AssetTransactionIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Asset transaction not found',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Asset transaction reference number already exists',
  })
  update(
    @Request() req,
    @Param('id') id: string,
    @Body() updateAssetTransactionDto: UpdateAssetTransactionDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
    @UploadedFiles() attachments?: Express.Multer.File[],
  ): Promise<AssetTransactionIdResponseDto> {
    return this.assetTransactionsService.updateAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateAssetTransactionDto,
      metadata,
      attachments,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_TRANSACTION_DELETE)
  @ApiOperation({ summary: 'Delete an asset transaction' })
  @ApiParam({
    name: 'id',
    description: 'Asset transaction ID',
    type: String,
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiResponse({
    status: 200,
    description: 'The asset transaction has been successfully deleted',
    type: DeleteAssetTransactionResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Asset transaction not found',
  })
  remove(
    @Request() req,
    @Param('id') id: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<DeleteAssetTransactionResponseDto> {
    return this.assetTransactionsService.remove(
      req.user.id,
      req.user.activeBusinessId,
      id,
      metadata,
    );
  }

  @Delete()
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_TRANSACTION_DELETE)
  @ApiOperation({ summary: 'Bulk delete asset transactions' })
  @ApiResponse({
    status: 200,
    description: 'Asset transactions have been successfully deleted',
    type: BulkDeleteAssetTransactionResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or no IDs provided',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'No valid asset transactions found for deletion',
  })
  bulkDelete(
    @Request() req,
    @Body() bulkDeleteAssetTransactionDto: BulkDeleteAssetTransactionDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkDeleteAssetTransactionResponseDto> {
    return this.assetTransactionsService.bulkDelete(
      req.user.id,
      req.user.activeBusinessId,
      bulkDeleteAssetTransactionDto.ids,
      metadata,
    );
  }
}
