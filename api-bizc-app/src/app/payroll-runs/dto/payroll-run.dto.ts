import { ApiProperty } from '@nestjs/swagger';
import {
  PayrollRunType,
  PayrollRunStatus,
} from '../../drizzle/schema/payroll-run.schema';

export class PayrollRunDto {
  @ApiProperty({ description: 'Unique identifier for the payroll run' })
  id: string;

  @ApiProperty({ description: 'Business ID the payroll run belongs to' })
  businessId: string;

  @ApiProperty({ description: 'Unique run identifier' })
  runCode: string;

  @ApiProperty({
    example: PayrollRunType.REGULAR,
    enum: PayrollRunType,
    enumName: 'PayrollRunType',
    description: 'Type of payroll run',
  })
  runType: PayrollRunType;

  @ApiProperty({ description: 'Payroll month (e.g., "January", "February")' })
  payrollMonth: string;

  @ApiProperty({ description: 'Payroll year', example: 2024 })
  payrollYear: number;

  @ApiProperty({ description: 'Pay period start date', example: '2024-01-01' })
  payPeriodStart: string;

  @ApiProperty({ description: 'Pay period end date', example: '2024-01-31' })
  payPeriodEnd: string;

  @ApiProperty({ description: 'Payment date', example: '2024-02-05' })
  paymentDate: string;

  @ApiProperty({ description: 'Total number of employees', example: 50 })
  totalEmployees: number;

  @ApiProperty({ description: 'Total gross pay amount', example: '50000.00' })
  totalGrossPay: string;

  @ApiProperty({ description: 'Total deductions amount', example: '5000.00' })
  totalDeductions: string;

  @ApiProperty({ description: 'Total net pay amount', example: '45000.00' })
  totalNetPay: string;

  @ApiProperty({
    example: PayrollRunStatus.DRAFT,
    enum: PayrollRunStatus,
    enumName: 'PayrollRunStatus',
    description: 'Payroll run status',
  })
  runStatus: PayrollRunStatus;

  @ApiProperty({
    description: 'Additional notes',
    required: false,
    nullable: true,
  })
  notes?: string;

  @ApiProperty({
    example: 'John Doe',
    description: 'Name of the user who created the payroll run',
  })
  createdBy: string;

  @ApiProperty({
    example: 'Jane Smith',
    description: 'Name of the user who last updated the payroll run',
    required: false,
    nullable: true,
  })
  updatedBy?: string;

  @ApiProperty({
    example: '2023-01-01T00:00:00Z',
    description: 'Creation timestamp',
  })
  createdAt: Date;

  @ApiProperty({
    example: '2023-01-01T00:00:00Z',
    description: 'Last update timestamp',
    required: false,
    nullable: true,
  })
  updatedAt?: Date;
}
