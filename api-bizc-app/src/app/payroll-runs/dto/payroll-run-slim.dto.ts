import { ApiProperty } from '@nestjs/swagger';
import {
  PayrollRunType,
  PayrollRunStatus,
} from '../../drizzle/schema/payroll-run.schema';

export class PayrollRunSlimDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'Payroll run ID',
  })
  id: string;

  @ApiProperty({ example: 'PR-2024-01', description: 'Run code' })
  runCode: string;

  @ApiProperty({
    example: PayrollRunType.REGULAR,
    enum: PayrollRunType,
    enumName: 'PayrollRunType',
    description: 'Type of payroll run',
  })
  runType: PayrollRunType;

  @ApiProperty({ example: 'January', description: 'Payroll month' })
  payrollMonth: string;

  @ApiProperty({ example: 2024, description: 'Payroll year' })
  payrollYear: number;

  @ApiProperty({
    example: PayrollRunStatus.DRAFT,
    enum: PayrollRunStatus,
    enumName: 'PayrollRunStatus',
    description: 'Payroll run status',
  })
  runStatus: PayrollRunStatus;
}
