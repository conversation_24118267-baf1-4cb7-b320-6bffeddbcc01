import { ApiProperty } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  MaxLength,
  IsEnum,
  IsNumber,
  Min,
  Max,
  IsDateString,
  IsDecimal,
} from 'class-validator';
import {
  PayrollRunType,
  PayrollRunStatus,
} from '../../drizzle/schema/payroll-run.schema';

export class UpdatePayrollRunDto {
  @ApiProperty({
    description: 'Unique run identifier',
    required: false,
    maxLength: 191,
  })
  @IsOptional()
  @IsString()
  @MaxLength(191)
  runCode?: string;

  @ApiProperty({
    description: 'Type of payroll run',
    required: false,
    enum: PayrollRunType,
    enumName: 'PayrollRunType',
  })
  @IsOptional()
  @IsEnum(PayrollRunType, {
    message: 'Run type must be a valid PayrollRunType',
  })
  runType?: PayrollRunType;

  @ApiProperty({
    description: 'Payroll month (e.g., "January", "February")',
    required: false,
    maxLength: 191,
  })
  @IsOptional()
  @IsString()
  @MaxLength(191)
  payrollMonth?: string;

  @ApiProperty({
    description: 'Payroll year',
    required: false,
    example: 2024,
  })
  @IsOptional()
  @IsNumber()
  @Min(2000)
  @Max(2100)
  payrollYear?: number;

  @ApiProperty({
    description: 'Pay period start date',
    required: false,
    example: '2024-01-01',
  })
  @IsOptional()
  @IsDateString()
  payPeriodStart?: string;

  @ApiProperty({
    description: 'Pay period end date',
    required: false,
    example: '2024-01-31',
  })
  @IsOptional()
  @IsDateString()
  payPeriodEnd?: string;

  @ApiProperty({
    description: 'Payment date',
    required: false,
    example: '2024-02-05',
  })
  @IsOptional()
  @IsDateString()
  paymentDate?: string;

  @ApiProperty({
    description: 'Total number of employees',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  totalEmployees?: number;

  @ApiProperty({
    description: 'Total gross pay amount',
    required: false,
    example: '50000.00',
  })
  @IsOptional()
  @IsDecimal({ decimal_digits: '2' })
  totalGrossPay?: string;

  @ApiProperty({
    description: 'Total deductions amount',
    required: false,
    example: '5000.00',
  })
  @IsOptional()
  @IsDecimal({ decimal_digits: '2' })
  totalDeductions?: string;

  @ApiProperty({
    description: 'Total net pay amount',
    required: false,
    example: '45000.00',
  })
  @IsOptional()
  @IsDecimal({ decimal_digits: '2' })
  totalNetPay?: string;

  @ApiProperty({
    description: 'Payroll run status',
    required: false,
    enum: PayrollRunStatus,
    enumName: 'PayrollRunStatus',
  })
  @IsOptional()
  @IsEnum(PayrollRunStatus, {
    message: 'Run status must be a valid PayrollRunStatus',
  })
  runStatus?: PayrollRunStatus;

  @ApiProperty({
    description: 'Additional notes',
    required: false,
  })
  @IsOptional()
  @IsString()
  notes?: string;
}
