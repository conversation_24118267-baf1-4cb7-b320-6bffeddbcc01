import {
  Injectable,
  Inject,
  NotFoundException,
  BadRequestException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { and, eq, ilike, desc, asc, sql, gte, lte } from 'drizzle-orm';
import { CreateCustomFieldDto } from './dto/create-custom-field.dto';
import { UpdateCustomFieldDto } from './dto/update-custom-field.dto';
import { CustomFieldDto } from './dto/custom-field.dto';
import { GetCustomFieldsQueryDto } from './dto/get-custom-fields.dto';
import {
  GetCustomFieldsResponseDto,
  GetCustomFieldsCountResponseDto,
} from './dto/custom-fields-response.dto';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { ActivityLogName, EntityType, CustomFieldType } from '../shared/types';
import { customFields } from '../drizzle/schema/custom-fields.schema';
import type { ActivityMetadata } from '../shared/types/activity-metadata.type';

@Injectable()
export class CustomFieldsService {
  constructor(
    @Inject(DRIZZLE) private readonly db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
  ) {}

  async create(
    userId: string,
    businessId: string,
    entityType: EntityType,
    createCustomFieldDto: CreateCustomFieldDto,
  ): Promise<CustomFieldDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Validate field type specific requirements
      this.validateFieldTypeRequirements(createCustomFieldDto);

      // Check if field name already exists for this entity type and business
      const existingField = await this.db
        .select()
        .from(customFields)
        .where(
          and(
            eq(customFields.businessId, businessId),
            eq(customFields.entityType, entityType),
            ilike(customFields.name, createCustomFieldDto.name),
          ),
        )
        .then((results) => results[0]);

      if (existingField) {
        throw new ConflictException(
          `A custom field with the name '${createCustomFieldDto.name}' already exists for ${entityType}`,
        );
      }

      const [newField] = await this.db
        .insert(customFields)
        .values({
          businessId: businessId,
          name: createCustomFieldDto.name,
          type: createCustomFieldDto.type,
          entityType: entityType,
          required: createCustomFieldDto.required,
          placeholder: createCustomFieldDto.placeholder,
          options: createCustomFieldDto.options,
          createdBy: userId,
        })
        .returning();

      // Log the activity
      await this.activityLogService.log(
        ActivityLogName.CREATE,
        `Created custom field "${createCustomFieldDto.name}" for ${entityType}`,
        { id: newField.id, type: 'custom-field' },
        { id: userId, type: 'user' },
      );

      return this.mapToCustomFieldDto(newField);
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create custom field: ${error.message}`,
      );
    }
  }

  async findAll(
    userId: string,
    businessId: string,
    entityType: EntityType,
    query: GetCustomFieldsQueryDto,
  ): Promise<GetCustomFieldsResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = ((query.page || 1) - 1) * (query.limit || 10);
    const limit = query.limit || 10;

    // Build base query
    const whereConditions = [
      eq(customFields.businessId, businessId),
      eq(customFields.entityType, entityType),
    ];

    // Add search condition if provided
    if (query.search) {
      whereConditions.push(ilike(customFields.name, `%${query.search}%`));
    }

    // Add type filter if provided
    if (query.type) {
      whereConditions.push(
        eq(customFields.type, query.type as CustomFieldType),
      );
    }

    // Add required filter if provided
    if (query.required !== undefined) {
      whereConditions.push(eq(customFields.required, query.required));
    }

    // Add date range filtering if provided
    if (query.from) {
      const fromDate = new Date(query.from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(customFields.createdAt, fromDate));
      }
    }

    if (query.to) {
      const toDate = new Date(query.to);
      if (!isNaN(toDate.getTime())) {
        // Add 23:59:59 to include the entire day
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(customFields.createdAt, toDate));
      }
    }

    // Build order by clause
    const orderBy = query.sortOrder === 'asc' ? asc : desc;
    let orderByField;
    switch (query.sortBy) {
      case 'name':
        orderByField = customFields.name;
        break;
      case 'type':
        orderByField = customFields.type;
        break;
      case 'required':
        orderByField = customFields.required;
        break;
      case 'updatedAt':
        orderByField = customFields.updatedAt;
        break;
      default:
        orderByField = customFields.createdAt;
    }

    // Find custom fields with pagination
    const result = await this.db
      .select()
      .from(customFields)
      .where(and(...whereConditions))
      .orderBy(orderBy(orderByField))
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(customFields)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    // Log the activity
    await this.activityLogService.log(
      ActivityLogName.VIEW,
      `Viewed custom fields for ${entityType}`,
      { id: businessId, type: 'business' },
      { id: userId, type: 'user' },
    );

    return {
      fields: result.map((field) => this.mapToCustomFieldDto(field)),
      meta: {
        total,
        page: query.page || 1,
        totalPages,
        limit,
      },
    };
  }

  async getCounts(
    userId: string,
    businessId: string,
  ): Promise<GetCustomFieldsCountResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Get counts for each entity type
    const entityTypes = [
      EntityType.CUSTOMERS,
      EntityType.EXPENSES,
      EntityType.ITEMS,
      EntityType.CAMPAIGNS,
      EntityType.BOOKINGS,
      EntityType.RENTALS,
      EntityType.ROOM_BOOKINGS,
      EntityType.SMS_NEWSLETTERS,
    ];

    const countPromises = entityTypes.map(async (entityType) => {
      const result = await this.db
        .select({ count: sql<number>`count(*)` })
        .from(customFields)
        .where(
          and(
            eq(customFields.businessId, businessId),
            eq(customFields.entityType, entityType),
          ),
        );

      return {
        entityType,
        count: Number(result[0].count),
      };
    });

    const counts = await Promise.all(countPromises);
    const total = counts.reduce((sum, item) => sum + item.count, 0);

    // Log the activity
    await this.activityLogService.log(
      ActivityLogName.VIEW,
      'Viewed custom field counts',
      { id: businessId, type: 'business' },
      { id: userId, type: 'user' },
    );

    return {
      counts,
      total,
    };
  }

  async findOne(
    userId: string,
    businessId: string,
    entityType: EntityType,
    fieldName: string,
  ): Promise<CustomFieldDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const field = await this.db
      .select()
      .from(customFields)
      .where(
        and(
          eq(customFields.businessId, businessId),
          eq(customFields.entityType, entityType),
          ilike(customFields.name, fieldName),
        ),
      )
      .then((results) => results[0]);

    if (!field) {
      throw new NotFoundException(
        `Custom field "${fieldName}" not found for entity type "${entityType}"`,
      );
    }

    // Log the activity
    await this.activityLogService.log(
      ActivityLogName.VIEW,
      `Viewed custom field "${fieldName}" for ${entityType}`,
      { id: field.id, type: 'custom-field' },
      { id: userId, type: 'user' },
    );

    return this.mapToCustomFieldDto(field);
  }

  async update(
    userId: string,
    businessId: string,
    entityType: EntityType,
    fieldName: string,
    updateCustomFieldDto: UpdateCustomFieldDto,
  ): Promise<CustomFieldDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Get the existing field
    const existingField = await this.db
      .select()
      .from(customFields)
      .where(
        and(
          eq(customFields.businessId, businessId),
          eq(customFields.entityType, entityType),
          ilike(customFields.name, fieldName),
        ),
      )
      .then((results) => results[0]);

    if (!existingField) {
      throw new NotFoundException(
        `Custom field "${fieldName}" not found for entity type "${entityType}"`,
      );
    }

    // Validate field type specific requirements if type is being updated
    if (updateCustomFieldDto.type) {
      this.validateFieldTypeRequirements({
        ...existingField,
        ...updateCustomFieldDto,
      });
    }

    // If name is being updated, check for conflicts
    if (
      updateCustomFieldDto.name &&
      updateCustomFieldDto.name !== existingField.name
    ) {
      const nameConflict = await this.db
        .select()
        .from(customFields)
        .where(
          and(
            eq(customFields.businessId, businessId),
            eq(customFields.entityType, entityType),
            ilike(customFields.name, updateCustomFieldDto.name),
          ),
        )
        .then((results) => results[0]);

      if (nameConflict) {
        throw new ConflictException(
          `A custom field with the name '${updateCustomFieldDto.name}' already exists for ${entityType}`,
        );
      }
    }

    try {
      const [updatedField] = await this.db
        .update(customFields)
        .set({
          ...updateCustomFieldDto,
          updatedAt: new Date(),
        })
        .where(eq(customFields.id, existingField.id))
        .returning();

      // Log the activity
      await this.activityLogService.log(
        ActivityLogName.UPDATE,
        `Updated custom field "${fieldName}" for ${entityType}`,
        { id: updatedField.id, type: 'custom-field' },
        { id: userId, type: 'user' },
      );

      return this.mapToCustomFieldDto(updatedField);
    } catch (error) {
      throw new BadRequestException(
        `Failed to update custom field: ${error.message}`,
      );
    }
  }

  async remove(
    userId: string,
    businessId: string,
    entityType: EntityType,
    fieldName: string,
    metadata?: ActivityMetadata,
  ): Promise<{ success: boolean; message: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Get the existing field
    const existingField = await this.db
      .select()
      .from(customFields)
      .where(
        and(
          eq(customFields.businessId, businessId),
          eq(customFields.entityType, entityType),
          ilike(customFields.name, fieldName),
        ),
      )
      .then((results) => results[0]);

    if (!existingField) {
      throw new NotFoundException(
        `Custom field "${fieldName}" not found for entity type "${entityType}"`,
      );
    }

    // Delete the field
    await this.db
      .delete(customFields)
      .where(eq(customFields.id, existingField.id));

    // Log the activity
    await this.activityLogService.log(
      ActivityLogName.DELETE,
      `Deleted custom field "${fieldName}" for ${entityType}`,
      { id: existingField.id, type: 'custom-field' },
      { id: userId, type: 'user' },
    );

    return {
      success: true,
      message: `Custom field "${fieldName}" has been deleted`,
    };
  }

  async createAndReturnId(
    userId: string,
    businessId: string,
    entityType: EntityType,
    createCustomFieldDto: CreateCustomFieldDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    const customField = await this.create(
      userId,
      businessId,
      entityType,
      createCustomFieldDto,
    );
    return { id: customField.id };
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string,
    entityType: EntityType,
    createCustomFieldsDto: CreateCustomFieldDto[],
    metadata?: ActivityMetadata,
  ): Promise<{ ids: string[] }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!createCustomFieldsDto || createCustomFieldsDto.length === 0) {
        throw new BadRequestException('At least one custom field is required');
      }

      const fieldNames = createCustomFieldsDto.map((field) => field.name);
      const duplicateNames = fieldNames.filter(
        (name, index) => fieldNames.indexOf(name) !== index,
      );

      if (duplicateNames.length > 0) {
        throw new BadRequestException(
          `Duplicate field names in request: ${duplicateNames.join(', ')}`,
        );
      }

      // Check for existing field names
      const existingFields = await this.db
        .select()
        .from(customFields)
        .where(
          and(
            eq(customFields.businessId, businessId),
            eq(customFields.entityType, entityType),
          ),
        );

      const existingNames = existingFields.map((field) =>
        field.name.toLowerCase(),
      );
      const conflictingNames = fieldNames.filter((name) =>
        existingNames.includes(name.toLowerCase()),
      );

      if (conflictingNames.length > 0) {
        throw new ConflictException(
          `Field names already exist for ${entityType}: ${conflictingNames.join(', ')}`,
        );
      }

      // Validate all fields
      createCustomFieldsDto.forEach((field) => {
        this.validateFieldTypeRequirements(field);
      });

      // Create all fields
      const fieldsToInsert = createCustomFieldsDto.map((field) => ({
        businessId: businessId,
        name: field.name,
        type: field.type,
        entityType: entityType,
        required: field.required,
        placeholder: field.placeholder,
        options: field.options,
        createdBy: userId,
      }));

      const createdFields = await this.db
        .insert(customFields)
        .values(fieldsToInsert)
        .returning();

      // Log the bulk creation activity
      await this.activityLogService.log(
        ActivityLogName.CREATE,
        `Bulk created ${createdFields.length} custom fields for ${entityType}`,
        { id: businessId, type: 'business' },
        { id: userId, type: 'user' },
      );

      return { ids: createdFields.map((field) => field.id.toString()) };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk create custom fields: ${error.message}`,
      );
    }
  }

  async checkNameAvailability(
    userId: string,
    businessId: string,
    entityType: EntityType,
    name: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (!name || name.trim() === '') {
      throw new BadRequestException('Field name is required');
    }

    const existingField = await this.db
      .select()
      .from(customFields)
      .where(
        and(
          eq(customFields.businessId, businessId),
          eq(customFields.entityType, entityType),
          ilike(customFields.name, name.trim()),
        ),
      )
      .then((results) => results[0]);

    return { available: !existingField };
  }

  async updateAndReturnId(
    userId: string,
    businessId: string,
    entityType: EntityType,
    fieldName: string,
    updateCustomFieldDto: UpdateCustomFieldDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    const customField = await this.update(
      userId,
      businessId,
      entityType,
      fieldName,
      updateCustomFieldDto,
    );
    return { id: customField.id };
  }

  private validateFieldTypeRequirements(field: any): void {
    // For select, multi-select, and radio fields, options are required
    if (
      [
        CustomFieldType.SELECT,
        CustomFieldType.MULTI_SELECT,
        CustomFieldType.RADIO,
      ].includes(field.type)
    ) {
      if (
        !field.options ||
        !Array.isArray(field.options) ||
        field.options.length === 0
      ) {
        throw new BadRequestException(
          `Options are required for ${field.type} field type`,
        );
      }
    }
  }

  private mapToCustomFieldDto(
    field: typeof customFields.$inferSelect,
  ): CustomFieldDto {
    return {
      id: field.id.toString(),
      businessId: field.businessId.toString(),
      name: field.name,
      type: field.type as any,
      entityType: field.entityType as any,
      required: field.required,
      placeholder: field.placeholder,
      options: field.options,
      createdBy: field.createdBy.toString(),
      createdAt: field.createdAt,
      updatedAt: field.updatedAt,
    };
  }
}
