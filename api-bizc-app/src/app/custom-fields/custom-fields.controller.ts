import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  Request,
  UseGuards,
} from '@nestjs/common';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiParam,
  ApiBearerAuth,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { CustomFieldsService } from './custom-fields.service';
import { CreateCustomFieldDto } from './dto/create-custom-field.dto';
import { UpdateCustomFieldDto } from './dto/update-custom-field.dto';
import { CustomFieldDto } from './dto/custom-field.dto';
import { GetCustomFieldsQueryDto } from './dto/get-custom-fields.dto';
import {
  GetCustomFieldsResponseDto,
  GetCustomFieldsCountResponseDto,
} from './dto/custom-fields-response.dto';
import { CustomFieldIdResponseDto } from './dto/custom-field-id-response.dto';
import { BulkCreateCustomFieldDto } from './dto/bulk-create-custom-field.dto';
import { BulkCustomFieldIdsResponseDto } from './dto/bulk-custom-field-ids-response.dto';
import { DeleteCustomFieldResponseDto } from './dto/delete-custom-field-response.dto';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { Permission, EntityType } from '../shared/types';
import { ActivityMetadata } from '../auth/decorators/activity-metadata.decorator';
import type { ActivityMetadata as ActivityMetadataType } from '../shared/types/activity-metadata.type';

@ApiTags('custom-fields')
@Controller('custom-fields')
@UseGuards(PermissionsGuard)
export class CustomFieldsController {
  constructor(private readonly customFieldsService: CustomFieldsService) {}

  @Post(':entityType')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CUSTOM_FIELD_CREATE)
  @ApiOperation({ summary: 'Create a new custom field for an entity type' })
  @ApiParam({
    name: 'entityType',
    description: 'Entity type for the custom field',
    enum: EntityType,
    enumName: 'EntityType',
  })
  @ApiResponse({
    status: 201,
    description: 'The custom field has been successfully created',
    type: CustomFieldIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Field name already exists for this entity type',
  })
  create(
    @Request() req,
    @Param('entityType') entityType: EntityType,
    @Body() createCustomFieldDto: CreateCustomFieldDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<CustomFieldIdResponseDto> {
    return this.customFieldsService.createAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      entityType,
      createCustomFieldDto,
      metadata,
    );
  }

  @Post(':entityType/bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CUSTOM_FIELD_CREATE)
  @ApiOperation({ summary: 'Bulk create custom fields for an entity type' })
  @ApiParam({
    name: 'entityType',
    description: 'Entity type for the custom fields',
    enum: EntityType,
    enumName: 'EntityType',
  })
  @ApiBody({
    description: 'Bulk custom field creation',
    type: BulkCreateCustomFieldDto,
  })
  @ApiResponse({
    status: 201,
    description: 'The custom fields have been successfully created',
    type: BulkCustomFieldIdsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or duplicate names',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Field names already exist',
  })
  bulkCreate(
    @Request() req,
    @Param('entityType') entityType: EntityType,
    @Body() bulkCreateCustomFieldDto: BulkCreateCustomFieldDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkCustomFieldIdsResponseDto> {
    return this.customFieldsService.bulkCreateAndReturnIds(
      req.user.id,
      req.user.activeBusinessId,
      entityType,
      bulkCreateCustomFieldDto.customFields,
      metadata,
    );
  }

  @Get(':entityType')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CUSTOM_FIELD_READ)
  @ApiOperation({ summary: 'Get custom fields for an entity type' })
  @ApiParam({
    name: 'entityType',
    description: 'Entity type to fetch custom fields for',
    enum: EntityType,
    enumName: 'EntityType',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'search',
    description: 'Search term for field names',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'sortBy',
    description: 'Sort by field',
    required: false,
    enum: ['name', 'type', 'required', 'createdAt', 'updatedAt'],
  })
  @ApiQuery({
    name: 'sortOrder',
    description: 'Sort order',
    required: false,
    enum: ['asc', 'desc'],
  })
  @ApiQuery({
    name: 'from',
    description: 'Filter from date (YYYY-MM-DD format)',
    required: false,
    type: String,
    example: '2025-05-24',
  })
  @ApiQuery({
    name: 'to',
    description: 'Filter to date (YYYY-MM-DD format)',
    required: false,
    type: String,
    example: '2025-05-24',
  })
  @ApiQuery({
    name: 'type',
    description: 'Filter by field type',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'required',
    description: 'Filter by required status',
    required: false,
    type: Boolean,
  })
  @ApiResponse({
    status: 200,
    description: 'Returns custom fields for the specified entity type',
    type: GetCustomFieldsResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Request() req,
    @Param('entityType') entityType: EntityType,
    @Query() query: GetCustomFieldsQueryDto,
  ): Promise<GetCustomFieldsResponseDto> {
    return this.customFieldsService.findAll(
      req.user.id,
      req.user.activeBusinessId,
      entityType,
      query,
    );
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.CUSTOM_FIELD_READ)
  @ApiOperation({ summary: 'Get custom field counts for all entity types' })
  @ApiResponse({
    status: 200,
    description: 'Returns custom field counts for all entity types',
    type: GetCustomFieldsCountResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  getCounts(@Request() req): Promise<GetCustomFieldsCountResponseDto> {
    return this.customFieldsService.getCounts(
      req.user.id,
      req.user.activeBusinessId,
    );
  }

  @Get(':entityType/check-name-availability')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CUSTOM_FIELD_READ)
  @ApiOperation({ summary: 'Check if a custom field name is available' })
  @ApiParam({
    name: 'entityType',
    description: 'Entity type',
    enum: EntityType,
    enumName: 'EntityType',
  })
  @ApiQuery({
    name: 'name',
    description: 'Field name to check',
    required: true,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Returns availability status',
    schema: {
      type: 'object',
      properties: {
        available: { type: 'boolean' },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkNameAvailability(
    @Request() req,
    @Param('entityType') entityType: EntityType,
    @Query('name') name: string,
  ): Promise<{ available: boolean }> {
    return this.customFieldsService.checkNameAvailability(
      req.user.id,
      req.user.activeBusinessId,
      entityType,
      name,
    );
  }

  @Get(':entityType/:fieldName')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CUSTOM_FIELD_READ)
  @ApiOperation({ summary: 'Get a specific custom field by name' })
  @ApiParam({
    name: 'entityType',
    description: 'Entity type',
    enum: EntityType,
    enumName: 'EntityType',
  })
  @ApiParam({
    name: 'fieldName',
    description: 'Custom field name',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the custom field',
    type: CustomFieldDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Custom field not found',
  })
  findOne(
    @Request() req,
    @Param('entityType') entityType: EntityType,
    @Param('fieldName') fieldName: string,
  ): Promise<CustomFieldDto> {
    return this.customFieldsService.findOne(
      req.user.id,
      req.user.activeBusinessId,
      entityType,
      fieldName,
    );
  }

  @Patch(':entityType/:fieldName')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CUSTOM_FIELD_UPDATE)
  @ApiOperation({ summary: 'Update a custom field' })
  @ApiParam({
    name: 'entityType',
    description: 'Entity type',
    enum: EntityType,
    enumName: 'EntityType',
  })
  @ApiParam({
    name: 'fieldName',
    description: 'Custom field name to update',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'The custom field has been successfully updated',
    type: CustomFieldIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Custom field not found',
  })
  update(
    @Request() req,
    @Param('entityType') entityType: EntityType,
    @Param('fieldName') fieldName: string,
    @Body() updateCustomFieldDto: UpdateCustomFieldDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<CustomFieldIdResponseDto> {
    return this.customFieldsService.updateAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      entityType,
      fieldName,
      updateCustomFieldDto,
      metadata,
    );
  }

  @Delete(':entityType/:fieldName')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CUSTOM_FIELD_DELETE)
  @ApiOperation({ summary: 'Delete a custom field' })
  @ApiParam({
    name: 'entityType',
    description: 'Entity type',
    enum: EntityType,
    enumName: 'EntityType',
  })
  @ApiParam({
    name: 'fieldName',
    description: 'Custom field name to delete',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'The custom field has been successfully deleted',
    type: DeleteCustomFieldResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Custom field not found',
  })
  remove(
    @Request() req,
    @Param('entityType') entityType: EntityType,
    @Param('fieldName') fieldName: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<DeleteCustomFieldResponseDto> {
    return this.customFieldsService.remove(
      req.user.id,
      req.user.activeBusinessId,
      entityType,
      fieldName,
      metadata,
    );
  }
}
