import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { ActivitySource } from '../types/activity.enum';
import { detectSourceFromRequest } from './activity-source.decorator';

/**
 * Interface for complete request context needed for activity logging
 */
export interface RequestContext {
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  source: ActivitySource;
  timestamp: Date;
}

/**
 * RequestContext decorator - Extracts all context needed for activity logging
 *
 * This is a convenience decorator that combines IP, User-Agent, SessionId, and Source
 * into a single object, making it easy to pass to activity logging methods.
 *
 * @example
 * ```typescript
 * @Delete(':id')
 * remove(
 *   @RequestContext() context: RequestContext,
 *   @Request() req: AuthenticatedRequest,
 *   @Param('id') id: string,
 * ) {
 *   return this.service.remove(
 *     req.user.id,
 *     req.user.activeBusinessId,
 *     id,
 *     context  // Contains all activity logging context
 *   );
 * }
 * ```
 */
export const RequestContext = createParamDecorator(
  (_data: unknown, ctx: ExecutionContext): RequestContext => {
    const request = ctx.switchToHttp().getRequest();

    return {
      ipAddress: extractIpAddress(request),
      userAgent: request.headers['user-agent'],
      sessionId: extractSessionId(request),
      source: detectSourceFromRequest(request),
      timestamp: new Date(),
    };
  },
);

/**
 * IpAddress decorator - Extracts IP address from request
 */
export const IpAddress = createParamDecorator(
  (_data: unknown, ctx: ExecutionContext): string | undefined => {
    const request = ctx.switchToHttp().getRequest();
    return extractIpAddress(request);
  },
);

/**
 * UserAgent decorator - Extracts User-Agent header
 */
export const UserAgent = createParamDecorator(
  (_data: unknown, ctx: ExecutionContext): string | undefined => {
    const request = ctx.switchToHttp().getRequest();
    return request.headers['user-agent'];
  },
);

/**
 * Extract IP address from request with fallback chain
 */
function extractIpAddress(request: any): string | undefined {
  return (
    request.ip ||
    request.connection?.remoteAddress ||
    request.socket?.remoteAddress ||
    request.headers['x-forwarded-for']?.split(',')[0]?.trim() ||
    request.headers['x-real-ip'] ||
    request.headers['x-client-ip'] ||
    undefined
  );
}

/**
 * Extract session ID from request (same logic as SessionId decorator)
 */
function extractSessionId(request: any): string | undefined {
  return (
    request.headers['x-session-id'] ||
    request.query?.sessionId ||
    request.body?.sessionId ||
    undefined
  );
}

/**
 * Utility type for optional request context (when some fields might be missing)
 */
export interface OptionalRequestContext {
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  source?: ActivitySource;
  timestamp?: Date;
}
