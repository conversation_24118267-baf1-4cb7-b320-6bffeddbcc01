// Activity logging decorators
export { SessionId, SessionIdOptional } from './session-id.decorator';
export {
  Source,
  SourceWithConfidence,
  detectSourceFromRequest,
  detectSourceWithConfidenceFromRequest,
} from './activity-source.decorator';
export {
  RequestContext,
  IpAddress,
  UserAgent,
  type RequestContext as RequestContextType,
  type OptionalRequestContext,
} from './request-context.decorator';

// Re-export existing decorators for convenience
export { RequirePermissions } from '../../auth/decorators/permissions.decorator';
export { Roles } from '../../auth/decorators/roles.decorator';
export { SkipAuthentication } from '../../auth/decorators/skip-authentication.decorator';
