import { createParamDecorator, ExecutionContext } from '@nestjs/common';

/**
 * SessionId decorator - Extracts session ID from various request sources
 *
 * Priority order:
 * 1. X-Session-Id header
 * 2. sessionId query parameter
 * 3. sessionId from request body
 * 4. Generated fallback based on request fingerprint
 *
 * @example
 * ```typescript
 * @Post()
 * create(
 *   @SessionId() sessionId: string,
 *   @Body() dto: CreateDto
 * ) {
 *   // sessionId is automatically extracted
 * }
 * ```
 */
export const SessionId = createParamDecorator(
  (_data: unknown, ctx: ExecutionContext): string => {
    const request = ctx.switchToHttp().getRequest();

    // Priority 1: Custom header (recommended approach)
    const headerSessionId = request.headers['x-session-id'];
    if (headerSessionId) {
      return headerSessionId;
    }

    // Priority 2: Query parameter
    const querySessionId = request.query?.sessionId;
    if (querySessionId) {
      return querySessionId;
    }

    // Priority 3: Request body
    const bodySessionId = request.body?.sessionId;
    if (bodySessionId) {
      return bodySessionId;
    }

    // Priority 4: Generate fallback session ID
    return generateFallbackSessionId(request);
  },
);

/**
 * Generates a fallback session ID based on request characteristics
 * This creates a consistent session ID for the same request context
 */
function generateFallbackSessionId(request: any): string {
  const userAgent = request.headers['user-agent'] || '';
  const ip = request.ip || request.connection?.remoteAddress || '';
  const timestamp = Date.now();
  const userId = request.user?.id || 'anonymous';

  // Create a fingerprint from request characteristics
  const fingerprint = `${ip}_${userAgent}_${userId}`.replace(
    /[^a-zA-Z0-9_]/g,
    '',
  );
  const hash = Buffer.from(fingerprint).toString('base64').substring(0, 8);

  return `auto_${timestamp}_${hash}`;
}

/**
 * SessionIdOptional decorator - Same as SessionId but returns undefined if not found
 * Use this when session ID is truly optional
 */
export const SessionIdOptional = createParamDecorator(
  (_data: unknown, ctx: ExecutionContext): string | undefined => {
    const request = ctx.switchToHttp().getRequest();

    // Only check explicit sources, don't generate fallback
    return (
      request.headers['x-session-id'] ||
      request.query?.sessionId ||
      request.body?.sessionId ||
      undefined
    );
  },
);
