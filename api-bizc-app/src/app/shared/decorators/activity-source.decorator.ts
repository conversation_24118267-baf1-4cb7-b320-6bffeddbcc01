import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { ActivitySource } from '../types/activity.enum';

/**
 * Source decorator - Automatically detects the activity source from request
 *
 * Detection priority:
 * 1. X-Client-Source header (explicit)
 * 2. X-API-Key header (API source)
 * 3. User-Agent analysis (mobile/web/api)
 * 4. Authentication method analysis
 * 5. Default fallback to WEB
 *
 * @example
 * ```typescript
 * @Post()
 * create(
 *   @Source() source: ActivitySource,
 *   @Body() dto: CreateDto
 * ) {
 *   // source is automatically detected as WEB, MOBILE, or API
 * }
 * ```
 */
export const Source = createParamDecorator(
  (_data: unknown, ctx: ExecutionContext): ActivitySource => {
    const request = ctx.switchToHttp().getRequest();
    return detectActivitySource(request);
  },
);

/**
 * SourceWithConfidence decorator - Returns both source and confidence level
 * Useful for logging and debugging source detection accuracy
 */
export const SourceWithConfidence = createParamDecorator(
  (
    _data: unknown,
    ctx: ExecutionContext,
  ): {
    source: ActivitySource;
    confidence: number;
    method: string;
  } => {
    const request = ctx.switchToHttp().getRequest();
    return detectActivitySourceWithConfidence(request);
  },
);

/**
 * Core source detection logic
 */
function detectActivitySource(request: any): ActivitySource {
  const headers = request.headers;
  const userAgent = (headers['user-agent'] || '').toLowerCase();

  // Priority 1: Explicit source header
  const explicitSource = headers['x-client-source'];
  if (
    explicitSource &&
    Object.values(ActivitySource).includes(explicitSource)
  ) {
    return explicitSource as ActivitySource;
  }

  // Priority 2: API key indicates API source
  if (headers['x-api-key'] || headers['authorization']?.includes('ApiKey')) {
    return ActivitySource.API;
  }

  // Priority 3: Mobile detection from User-Agent
  if (isMobileUserAgent(userAgent)) {
    return ActivitySource.MOBILE;
  }

  // Priority 4: API client detection
  if (isApiClientUserAgent(userAgent)) {
    return ActivitySource.API;
  }

  // Priority 5: Default to web for browsers
  return ActivitySource.WEB;
}

/**
 * Enhanced source detection with confidence scoring
 */
function detectActivitySourceWithConfidence(request: any): {
  source: ActivitySource;
  confidence: number;
  method: string;
} {
  const headers = request.headers;
  const userAgent = (headers['user-agent'] || '').toLowerCase();

  // Explicit source header - highest confidence
  const explicitSource = headers['x-client-source'];
  if (
    explicitSource &&
    Object.values(ActivitySource).includes(explicitSource)
  ) {
    return {
      source: explicitSource as ActivitySource,
      confidence: 1.0,
      method: 'explicit-header',
    };
  }

  // API key detection - very high confidence
  if (headers['x-api-key'] || headers['authorization']?.includes('ApiKey')) {
    return {
      source: ActivitySource.API,
      confidence: 0.95,
      method: 'api-key',
    };
  }

  // Mobile app headers - high confidence
  if (headers['x-mobile-app'] || headers['x-app-version']) {
    return {
      source: ActivitySource.MOBILE,
      confidence: 0.9,
      method: 'mobile-headers',
    };
  }

  // User-Agent mobile detection - medium-high confidence
  if (isMobileUserAgent(userAgent)) {
    return {
      source: ActivitySource.MOBILE,
      confidence: 0.8,
      method: 'user-agent-mobile',
    };
  }

  // API client User-Agent - medium-high confidence
  if (isApiClientUserAgent(userAgent)) {
    return {
      source: ActivitySource.API,
      confidence: 0.85,
      method: 'user-agent-api',
    };
  }

  // Browser detection - medium confidence
  if (isBrowserUserAgent(userAgent)) {
    return {
      source: ActivitySource.WEB,
      confidence: 0.7,
      method: 'user-agent-browser',
    };
  }

  // Default fallback - low confidence
  return {
    source: ActivitySource.WEB,
    confidence: 0.5,
    method: 'default-fallback',
  };
}

/**
 * Detects mobile devices from User-Agent
 */
function isMobileUserAgent(userAgent: string): boolean {
  const mobilePatterns = [
    'mobile',
    'android',
    'iphone',
    'ipod',
    'ipad',
    'windows phone',
    'blackberry',
    'webos',
    'opera mini',
    'iemobile',
  ];

  return mobilePatterns.some((pattern) => userAgent.includes(pattern));
}

/**
 * Detects API clients from User-Agent
 */
function isApiClientUserAgent(userAgent: string): boolean {
  const apiClientPatterns = [
    'postman',
    'insomnia',
    'curl',
    'wget',
    'axios',
    'fetch',
    'okhttp',
    'python-requests',
    'java/',
    'php/',
    'go-http-client',
    'apache-httpclient',
    'restsharp',
  ];

  return apiClientPatterns.some((pattern) => userAgent.includes(pattern));
}

/**
 * Detects web browsers from User-Agent
 */
function isBrowserUserAgent(userAgent: string): boolean {
  const browserPatterns = [
    'mozilla',
    'chrome',
    'safari',
    'firefox',
    'edge',
    'opera',
  ];

  return browserPatterns.some((pattern) => userAgent.includes(pattern));
}

/**
 * Utility function for manual source detection (can be used in services)
 */
export function detectSourceFromRequest(request: any): ActivitySource {
  return detectActivitySource(request);
}

/**
 * Utility function for source detection with confidence (can be used in services)
 */
export function detectSourceWithConfidenceFromRequest(request: any): {
  source: ActivitySource;
  confidence: number;
  method: string;
} {
  return detectActivitySourceWithConfidence(request);
}
