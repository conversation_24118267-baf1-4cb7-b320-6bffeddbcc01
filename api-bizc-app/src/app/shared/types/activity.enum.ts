/**
 * Activity-related enums used across the application
 * These enums match the database schema definitions
 */

/**
 * Activity Type - Enhanced version matching schema
 * Used to categorize the type of activity being performed
 */
export enum ActivityType {
  // Single operations
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
  RESTORE = 'RESTORE',
  STATUS_CHANGE = 'STATUS_CHANGE',
  PRIORITY_CHANGE = 'PRIORITY_CHANGE',

  // Bulk operations
  BULK_CREATE = 'BULK_CREATE',
  BULK_UPDATE = 'BULK_UPDATE',
  BULK_DELETE = 'BULK_DELETE',
  BULK_STATUS_CHANGE = 'BULK_STATUS_CHANGE',
  BULK_PRIORITY_CHANGE = 'BULK_PRIORITY_CHANGE',

  // Auth operations
  LOGIN = 'LOGIN',
  LOGOUT = 'LOGOUT',
  LOGIN_FAILED = 'LOGIN_FAILED',

  // Data operations
  EXPORT = 'EXPORT',
  IMPORT = 'IMPORT',
  VIEW = 'VIEW',
  SEARCH = 'SEARCH',
}

/**
 * Entity Type - Enhanced version matching schema
 * Used to identify the type of entity being acted upon
 */
export enum EntityType {
  DESIGNATION = 'DESIGNATION',
  USER = 'USER',
  BUSINESS = 'BUSINESS',
  AUTH = 'AUTH',
  SYSTEM = 'SYSTEM',
  ASSET = 'ASSET',
  ALLOWANCE_TYPE = 'ALLOWANCE_TYPE',
  ACCOUNT = 'ACCOUNT',
  CATEGORY = 'CATEGORY',
  PRODUCT = 'PRODUCT',
  SERVICE = 'SERVICE',
  CUSTOMER = 'CUSTOMER',
  ORDER = 'ORDER',
  INVOICE = 'INVOICE',
  PAYMENT = 'PAYMENT',
  STAFF = 'STAFF',
  DEPARTMENT = 'DEPARTMENT',
  LOCATION = 'LOCATION',
  SUPPLIER = 'SUPPLIER',
  VEHICLE = 'VEHICLE',
  RESERVATION = 'RESERVATION',
  CAMPAIGN = 'CAMPAIGN',
  TEMPLATE = 'TEMPLATE',
  WORK_ORDER = 'WORK_ORDER',
  PROJECT = 'PROJECT',
  TASK = 'TASK',
  EXPENSE = 'EXPENSE',
  LEAD = 'LEAD',
  ESTIMATE = 'ESTIMATE',
  MEETING = 'MEETING',
  ACCOMMODATION_UNIT = 'ACCOMMODATION_UNIT',
  APPOINTMENT = 'APPOINTMENT',
  ASSET_DAMAGE = 'ASSET_DAMAGE',
  ASSET_MAINTENANCE = 'ASSET_MAINTENANCE',
  ASSET_CATEGORY = 'ASSET_CATEGORY',
  DISCOUNT_PLAN = 'DISCOUNT_PLAN',
}

/**
 * Activity Source - Where the activity originated from
 */
export enum ActivitySource {
  WEB = 'web',
  MOBILE = 'mobile',
  API = 'api',
  IMPORT = 'import',
  SYSTEM = 'system',
}

/**
 * Operation Type - Type of CRUD operation performed
 */
export enum OperationType {
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
  RESTORE = 'RESTORE',
}

/**
 * Delete Type - Type of deletion performed
 */
export enum DeleteType {
  SOFT = 'soft',
  HARD = 'hard',
}

/**
 * Triggered By - What triggered the activity
 */
export enum TriggeredBy {
  MANUAL = 'manual',
  SYSTEM = 'system',
  SCHEDULE = 'schedule',
  EVENT = 'event',
}

/**
 * Priority Impact - Impact of priority change
 */
export enum PriorityImpact {
  INCREASED = 'increased',
  DECREASED = 'decreased',
  BULK_UPDATE = 'bulk_update',
  NO_CHANGE = 'no_change',
}

/**
 * Execution Strategy - Strategy for bulk operations
 */
export enum ExecutionStrategy {
  PARALLEL = 'parallel',
  SEQUENTIAL = 'sequential',
}

/**
 * File Format - Supported file formats for import/export
 */
export enum FileFormat {
  CSV = 'csv',
  JSON = 'json',
  XLSX = 'xlsx',
}

/**
 * Sort Direction - Direction for sorting
 */
export enum SortDirection {
  ASC = 'asc',
  DESC = 'desc',
}

/**
 * Activity Log Name
 * Used to identify the type of activity in the log
 */
export enum ActivityLogName {
  LOGIN = 'login',
  LOGOUT = 'logout',
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
  VIEW = 'view',
  OTHER = 'other',

  // Meal Period activities
  MEAL_PERIOD_CREATED = 'meal_period_created',
  MEAL_PERIOD_UPDATED = 'meal_period_updated',
  MEAL_PERIOD_DELETED = 'meal_period_deleted',

  // Customer Group activities
  CUSTOMER_GROUP_CREATED = 'customer_group_created',
  CUSTOMER_GROUP_UPDATED = 'customer_group_updated',
  CUSTOMER_GROUP_DELETED = 'customer_group_deleted',

  // Business Settings activities
  BUSINESS_ACCOUNTING_SETTINGS_UPDATED = 'business_accounting_settings_updated',

  // Performance Review activities
  PERFORMANCE_REVIEW_CREATED = 'performance_review_created',
  PERFORMANCE_REVIEW_UPDATED = 'performance_review_updated',
  PERFORMANCE_REVIEW_DELETED = 'performance_review_deleted',
  PERFORMANCE_REVIEW_BULK_DELETED = 'performance_review_bulk_deleted',

  // Time Slot activities
  CREATE_TIME_SLOT = 'create_time_slot',
  UPDATE_TIME_SLOT = 'update_time_slot',
  DELETE_TIME_SLOT = 'delete_time_slot',

  // Table Reservation activities
  TABLE_RESERVATION_CREATED = 'table_reservation_created',
  TABLE_RESERVATION_UPDATED = 'table_reservation_updated',
  TABLE_RESERVATION_DELETED = 'table_reservation_deleted',
  TABLE_RESERVATION_STATUS_UPDATED = 'table_reservation_status_updated',
  TABLE_RESERVATION_CANCELLED = 'table_reservation_cancelled',
  TABLE_RESERVATION_CONFIRMED = 'table_reservation_confirmed',
  TABLE_RESERVATION_COMPLETED = 'table_reservation_completed',
  TABLE_RESERVATION_NO_SHOW = 'table_reservation_no_show',

  // Package activities
  PACKAGE_CREATED = 'package_created',
  PACKAGE_UPDATED = 'package_updated',
  PACKAGE_DELETED = 'package_deleted',
  PACKAGE_BULK_CREATED = 'package_bulk_created',
  PACKAGE_BULK_DELETED = 'package_bulk_deleted',
  PACKAGE_STATUS_UPDATED = 'package_status_updated',

  // Event Space activities
  EVENT_SPACE_CREATED = 'event_space_created',
  EVENT_SPACE_UPDATED = 'event_space_updated',
  EVENT_SPACE_DELETED = 'event_space_deleted',
  EVENT_SPACE_BULK_DELETED = 'event_space_bulk_deleted',

  // Vehicle activities
  VEHICLE_CREATED = 'vehicle_created',
  VEHICLE_UPDATED = 'vehicle_updated',
  VEHICLE_DELETED = 'vehicle_deleted',
  VEHICLE_BULK_DELETED = 'vehicle_bulk_deleted',

  // Asset activities
  ASSET_CREATED = 'asset_created',
  ASSET_UPDATED = 'asset_updated',
  ASSET_DELETED = 'asset_deleted',
  ASSET_BULK_DELETED = 'asset_bulk_deleted',
}
