import { ActivitySource } from './activity.enum';

/**
 * Activity metadata interface for audit logging
 * Contains request context information for activity tracking
 */
export interface ActivityMetadata {
  /** Client IP address */
  ipAddress?: string;
  /** User agent string */
  userAgent?: string;
  /** Session identifier */
  sessionId?: string;
  /** Activity source (WEB, MOBILE, API) */
  source?: ActivitySource;
}
