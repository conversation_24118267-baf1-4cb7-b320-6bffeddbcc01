import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
  UnauthorizedException,
  Inject,
} from '@nestjs/common';
import {
  eq,
  and,
  or,
  ne,
  ilike,
  desc,
  asc,
  count,
  inArray,
  isNull,
  gte,
  lte,
  sql,
} from 'drizzle-orm';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateCustomerGroupDto } from './dto/create-customer-group.dto';
import { UpdateCustomerGroupDto } from './dto/update-customer-group.dto';
import { CustomerGroupDto } from './dto/customer-group.dto';
import { CustomerGroupSlimDto } from './dto/customer-group-slim.dto';
import { CustomerGroupIdResponseDto } from './dto/customer-group-id-response.dto';
import { DeleteCustomerGroupResponseDto } from './dto/delete-customer-group-response.dto';
import { PaginatedCustomerGroupsResponseDto } from './dto/paginated-customer-groups-response.dto';
import { CustomerGroupNameAvailabilityResponseDto } from './dto/check-customer-group-name.dto';
import { BulkCustomerGroupIdsResponseDto } from './dto/bulk-customer-group-ids-response.dto';
import { customerGroups, business } from '../drizzle/schema/schema';
import { CustomerGroupStatus } from '../shared/types';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { ActivityLogName } from '../shared/types';
import type { ActivityMetadata } from '../shared/types/activity-metadata.type';
import { CustomersService } from '../customers/customers.service';

@Injectable()
export class CustomerGroupsService {
  constructor(
    @Inject(DRIZZLE) private readonly db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly customersService: CustomersService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createCustomerGroupDto: CreateCustomerGroupDto,
  ): Promise<CustomerGroupDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if name already exists in the business
      const existingCustomerGroup = await this.db
        .select({ id: customerGroups.id })
        .from(customerGroups)
        .where(
          and(
            eq(customerGroups.businessId, businessId),
            ilike(customerGroups.name, createCustomerGroupDto.name),
            eq(customerGroups.status, CustomerGroupStatus.ACTIVE),
            isNull(customerGroups.deletedAt),
          ),
        )
        .limit(1);

      if (existingCustomerGroup.length > 0) {
        throw new ConflictException(
          `Customer group with name "${createCustomerGroupDto.name}" already exists`,
        );
      }

      // Verify business exists
      const businessExists = await this.db
        .select({ id: business.id })
        .from(business)
        .where(eq(business.id, businessId))
        .limit(1);

      if (businessExists.length === 0) {
        throw new NotFoundException('Business not found');
      }

      const [newCustomerGroup] = await this.db
        .insert(customerGroups)
        .values({
          businessId,
          name: createCustomerGroupDto.name,
          description: createCustomerGroupDto.description,
          createdBy: userId,
          status: createCustomerGroupDto.status ?? CustomerGroupStatus.ACTIVE,
        })
        .returning();

      // Log the customer group creation activity
      await this.activityLogService.log(
        ActivityLogName.CREATE,
        `Customer group "${createCustomerGroupDto.name}" was created`,
        { id: newCustomerGroup.id.toString(), type: 'customer-group' },
        { id: userId, type: 'user' },
        { customerGroupId: newCustomerGroup.id, businessId },
      );

      return this.mapToDto(newCustomerGroup);
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create customer group: ${error.message}`,
      );
    }
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createCustomerGroupDto: CreateCustomerGroupDto,
    metadata?: ActivityMetadata,
  ): Promise<CustomerGroupIdResponseDto> {
    const customerGroup = await this.create(
      userId,
      businessId,
      createCustomerGroupDto,
    );
    return {
      id: customerGroup.id,
      message: 'Customer group created successfully',
    };
  }

  async bulkCreate(
    userId: string,
    businessId: string | null,
    createCustomerGroupsDto: CreateCustomerGroupDto[],
  ): Promise<CustomerGroupDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (!createCustomerGroupsDto || createCustomerGroupsDto.length === 0) {
      throw new BadRequestException('No customer groups provided for creation');
    }

    // Check for duplicate names in the input
    const inputNames = createCustomerGroupsDto.map((dto) =>
      dto.name.toLowerCase(),
    );
    const duplicateInputNames = inputNames.filter(
      (name, index) => inputNames.indexOf(name) !== index,
    );

    if (duplicateInputNames.length > 0) {
      throw new BadRequestException(
        `Duplicate customer group names in input: ${duplicateInputNames.join(', ')}`,
      );
    }

    // Check if any names already exist in the database
    const existingCustomerGroups = await this.db
      .select({ name: customerGroups.name })
      .from(customerGroups)
      .where(
        and(
          eq(customerGroups.businessId, businessId),
          inArray(
            customerGroups.name,
            createCustomerGroupsDto.map((dto) => dto.name),
          ),
          eq(customerGroups.status, CustomerGroupStatus.ACTIVE),
          isNull(customerGroups.deletedAt),
        ),
      );

    if (existingCustomerGroups.length > 0) {
      const existingNames = existingCustomerGroups.map((cg) => cg.name);
      throw new ConflictException(
        `Customer groups already exist: ${existingNames.join(', ')}`,
      );
    }

    try {
      const customerGroupsToInsert = createCustomerGroupsDto.map((dto) => ({
        businessId,
        name: dto.name,
        description: dto.description,
        createdBy: userId,
        status: dto.status ?? CustomerGroupStatus.ACTIVE,
      }));

      const newCustomerGroups = await this.db
        .insert(customerGroups)
        .values(customerGroupsToInsert)
        .returning();

      // Log the bulk creation activity
      await this.activityLogService.log(
        ActivityLogName.CREATE,
        `${newCustomerGroups.length} customer groups were created in bulk`,
        {
          id: businessId,
          type: 'business',
        },
        { id: userId, type: 'user' },
        { customerGroupIds: newCustomerGroups.map((cg) => cg.id), businessId },
      );

      return newCustomerGroups.map((customerGroup) =>
        this.mapToDto(customerGroup),
      );
    } catch (error) {
      throw new BadRequestException(
        `Failed to create customer groups: ${error.message}`,
      );
    }
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createCustomerGroupsDto: CreateCustomerGroupDto[],
    metadata?: ActivityMetadata,
  ): Promise<BulkCustomerGroupIdsResponseDto> {
    const customerGroups = await this.bulkCreate(
      userId,
      businessId,
      createCustomerGroupsDto,
    );
    return {
      ids: customerGroups.map((cg) => cg.id),
    };
  }

  async findAll(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    name?: string,
    description?: string,
    status?: string,
    filters?: string,
    joinOperator: 'and' | 'or' = 'and',
    sort?: string,
  ): Promise<PaginatedCustomerGroupsResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      eq(customerGroups.businessId, businessId),
      isNull(customerGroups.deletedAt),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(customerGroups.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        // Add 23:59:59 to include the entire day
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(customerGroups.createdAt, toDate));
      }
    }

    // Add name filtering if provided
    if (name) {
      whereConditions.push(ilike(customerGroups.name, `%${name}%`));
    }

    // Add description filtering if provided
    if (description) {
      whereConditions.push(
        ilike(customerGroups.description, `%${description}%`),
      );
    }

    // Add status filtering if provided
    if (status) {
      // Decode URL-encoded commas and split by comma
      const decodedStatus = decodeURIComponent(status);
      const statusArray = decodedStatus
        .split(',')
        .map((s) => s.trim() as CustomerGroupStatus);
      whereConditions.push(inArray(customerGroups.status, statusArray));
    }

    // Add advanced filters if provided
    if (filters) {
      try {
        const parsedFilters = JSON.parse(filters);
        const filterConditions = [];

        for (const filter of parsedFilters) {
          const { id: fieldId, value, operator } = filter;

          if (fieldId === 'name') {
            switch (operator) {
              case 'iLike':
                filterConditions.push(ilike(customerGroups.name, `%${value}%`));
                break;
              case 'notILike':
                filterConditions.push(
                  sql`NOT ${ilike(customerGroups.name, `%${value}%`)}`,
                );
                break;
              case 'eq':
                filterConditions.push(eq(customerGroups.name, value));
                break;
              case 'ne':
                filterConditions.push(sql`${customerGroups.name} != ${value}`);
                break;
              case 'isEmpty':
                filterConditions.push(
                  sql`${customerGroups.name} IS NULL OR ${customerGroups.name} = ''`,
                );
                break;
              case 'isNotEmpty':
                filterConditions.push(
                  sql`${customerGroups.name} IS NOT NULL AND ${customerGroups.name} != ''`,
                );
                break;
            }
          } else if (fieldId === 'description') {
            switch (operator) {
              case 'iLike':
                filterConditions.push(
                  ilike(customerGroups.description, `%${value}%`),
                );
                break;
              case 'notILike':
                filterConditions.push(
                  sql`NOT ${ilike(customerGroups.description, `%${value}%`)}`,
                );
                break;
              case 'eq':
                filterConditions.push(eq(customerGroups.description, value));
                break;
              case 'ne':
                filterConditions.push(
                  sql`${customerGroups.description} != ${value}`,
                );
                break;
              case 'isEmpty':
                filterConditions.push(
                  sql`${customerGroups.description} IS NULL OR ${customerGroups.description} = ''`,
                );
                break;
              case 'isNotEmpty':
                filterConditions.push(
                  sql`${customerGroups.description} IS NOT NULL AND ${customerGroups.description} != ''`,
                );
                break;
            }
          } else if (fieldId === 'status') {
            switch (operator) {
              case 'eq':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    inArray(
                      customerGroups.status,
                      value as CustomerGroupStatus[],
                    ),
                  );
                } else {
                  filterConditions.push(eq(customerGroups.status, value));
                }
                break;
              case 'ne':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    sql`${customerGroups.status} NOT IN (${value.map((s) => `'${s}'`).join(',')})`,
                  );
                } else {
                  filterConditions.push(
                    sql`${customerGroups.status} != ${value}`,
                  );
                }
                break;
              case 'iLike': // Contains (for backward compatibility)
                if (typeof value === 'string') {
                  const decodedValue = decodeURIComponent(value);
                  const statusValues = decodedValue
                    .split(',')
                    .map((s) => s.trim() as CustomerGroupStatus);
                  filterConditions.push(
                    inArray(customerGroups.status, statusValues),
                  );
                } else {
                  filterConditions.push(eq(customerGroups.status, value));
                }
                break;
            }
          }
        }

        if (filterConditions.length > 0) {
          if (joinOperator === 'or') {
            whereConditions.push(or(...filterConditions));
          } else {
            whereConditions.push(and(...filterConditions));
          }
        }
      } catch {
        // Invalid JSON, ignore filters
      }
    }

    // Check if sorting by numberOfCustomers (requires special handling)
    let sortByNumberOfCustomers = false;
    let numberOfCustomersSortDesc = false;

    if (sort) {
      try {
        const parsedSort = JSON.parse(sort);
        if (parsedSort.length > 0) {
          const sortField = parsedSort[0];
          if (sortField.id === 'numberOfCustomers') {
            sortByNumberOfCustomers = true;
            numberOfCustomersSortDesc = sortField.desc === true;
          }
        }
      } catch {
        // Invalid JSON, use default sort
      }
    }

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: count() })
      .from(customerGroups)
      .where(and(...whereConditions));

    const total = totalResult[0].count;
    const totalPages = Math.ceil(total / limit);

    let data;

    if (sortByNumberOfCustomers) {
      // For numberOfCustomers sorting, we need to get all data first, then sort and paginate
      const allCustomerGroupsData = await this.db
        .select({
          id: customerGroups.id,
          name: customerGroups.name,
          description: customerGroups.description,
          status: customerGroups.status,
          createdAt: customerGroups.createdAt,
          updatedAt: customerGroups.updatedAt,
        })
        .from(customerGroups)
        .where(and(...whereConditions))
        .orderBy(desc(customerGroups.updatedAt)); // Default fallback order

      // Get customer counts for ALL customer groups
      const allCustomerGroupIds = allCustomerGroupsData.map((cg) => cg.id);
      const allCustomerCounts =
        await this.customersService.countCustomersByGroupIds(
          businessId,
          allCustomerGroupIds,
        );

      // Map to response format with customer counts
      const allDataWithCounts = allCustomerGroupsData.map((customerGroup) => ({
        id: customerGroup.id,
        name: customerGroup.name,
        description: customerGroup.description,
        numberOfCustomers: allCustomerCounts[customerGroup.id] || 0,
        status: customerGroup.status,
      }));

      // Sort by numberOfCustomers
      allDataWithCounts.sort((a, b) => {
        const comparison = a.numberOfCustomers - b.numberOfCustomers;
        return numberOfCustomersSortDesc ? -comparison : comparison;
      });

      // Apply pagination after sorting
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      data = allDataWithCounts.slice(startIndex, endIndex);
    } else {
      // For regular sorting, use database sorting and then paginate
      let orderBy = desc(customerGroups.updatedAt);

      if (sort) {
        try {
          const parsedSort = JSON.parse(sort);
          if (parsedSort.length > 0) {
            const sortField = parsedSort[0];
            const isDesc = sortField.desc === true;

            switch (sortField.id) {
              case 'name':
                orderBy = isDesc
                  ? desc(customerGroups.name)
                  : asc(customerGroups.name);
                break;
              case 'description':
                orderBy = isDesc
                  ? desc(customerGroups.description)
                  : asc(customerGroups.description);
                break;
              case 'status':
                orderBy = isDesc
                  ? desc(customerGroups.status)
                  : asc(customerGroups.status);
                break;
              case 'createdAt':
                orderBy = isDesc
                  ? desc(customerGroups.createdAt)
                  : asc(customerGroups.createdAt);
                break;
              case 'updatedAt':
                orderBy = isDesc
                  ? desc(customerGroups.updatedAt)
                  : asc(customerGroups.updatedAt);
                break;
            }
          }
        } catch {
          // Invalid JSON, use default sort
        }
      }

      // Get paginated data with database sorting
      const customerGroupsData = await this.db
        .select({
          id: customerGroups.id,
          name: customerGroups.name,
          description: customerGroups.description,
          status: customerGroups.status,
          createdAt: customerGroups.createdAt,
          updatedAt: customerGroups.updatedAt,
        })
        .from(customerGroups)
        .where(and(...whereConditions))
        .orderBy(orderBy)
        .limit(limit)
        .offset(offset);

      // Get customer counts for the paginated customer groups
      const customerGroupIds = customerGroupsData.map((cg) => cg.id);
      const customerCounts =
        await this.customersService.countCustomersByGroupIds(
          businessId,
          customerGroupIds,
        );

      // Map to response format
      data = customerGroupsData.map((customerGroup) => ({
        id: customerGroup.id,
        name: customerGroup.name,
        description: customerGroup.description,
        numberOfCustomers: customerCounts[customerGroup.id] || 0,
        status: customerGroup.status,
      }));
    }

    // Log the activity
    await this.activityLogService.log(
      ActivityLogName.VIEW,
      'Viewed customer groups (with advanced filtering)',
      { id: businessId, type: 'business' },
      { id: userId, type: 'user' },
    );

    return {
      data,
      meta: {
        total,
        page,
        limit,
        totalPages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
    };
  }

  async findAllSlim(
    userId: string,
    businessId: string | null,
  ): Promise<CustomerGroupSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const customerGroupsData = await this.db
      .select({
        id: customerGroups.id,
        name: customerGroups.name,
        description: customerGroups.description,
        status: customerGroups.status,
        createdAt: customerGroups.createdAt,
        updatedAt: customerGroups.updatedAt,
      })
      .from(customerGroups)
      .where(
        and(
          eq(customerGroups.businessId, businessId),
          eq(customerGroups.status, CustomerGroupStatus.ACTIVE),
          isNull(customerGroups.deletedAt),
        ),
      )
      .orderBy(desc(customerGroups.updatedAt));

    return customerGroupsData.map((customerGroup) => ({
      id: customerGroup.id,
      name: customerGroup.name,
      description: customerGroup.description,
      status: customerGroup.status,
      createdAt: customerGroup.createdAt.toISOString(),
      updatedAt: customerGroup.updatedAt.toISOString(),
    }));
  }

  async findOne(
    userId: string,
    id: string,
    businessId: string | null,
  ): Promise<CustomerGroupDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const customerGroup = await this.db
      .select()
      .from(customerGroups)
      .where(
        and(
          eq(customerGroups.id, id),
          eq(customerGroups.businessId, businessId),
          isNull(customerGroups.deletedAt),
        ),
      )
      .limit(1);

    if (customerGroup.length === 0) {
      throw new NotFoundException('Customer group not found');
    }

    return this.mapToDto(customerGroup[0]);
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateCustomerGroupDto: UpdateCustomerGroupDto,
  ): Promise<CustomerGroupIdResponseDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if customer group exists
      const existingCustomerGroup = await this.db
        .select()
        .from(customerGroups)
        .where(
          and(
            eq(customerGroups.id, id),
            eq(customerGroups.businessId, businessId),
            isNull(customerGroups.deletedAt),
          ),
        )
        .limit(1);

      if (existingCustomerGroup.length === 0) {
        throw new NotFoundException('Customer group not found');
      }

      // Check for name conflicts if name is being updated
      if (updateCustomerGroupDto.name) {
        const conflictingCustomerGroup = await this.db
          .select({ id: customerGroups.id })
          .from(customerGroups)
          .where(
            and(
              eq(customerGroups.businessId, businessId),
              ilike(customerGroups.name, updateCustomerGroupDto.name),
              ne(customerGroups.id, id),
              isNull(customerGroups.deletedAt),
            ),
          )
          .limit(1);

        if (conflictingCustomerGroup.length > 0) {
          throw new ConflictException(
            `Customer group with name "${updateCustomerGroupDto.name}" already exists`,
          );
        }
      }

      const updateData: any = {
        updatedBy: userId,
        updatedAt: new Date(),
      };
      if (updateCustomerGroupDto.name)
        updateData.name = updateCustomerGroupDto.name;
      if (updateCustomerGroupDto.description !== undefined)
        updateData.description = updateCustomerGroupDto.description;
      if (updateCustomerGroupDto.amount !== undefined) {
        updateData.amount = updateCustomerGroupDto.amount.toString();
      }
      if (updateCustomerGroupDto.status)
        updateData.status = updateCustomerGroupDto.status;

      const [updatedCustomerGroup] = await this.db
        .update(customerGroups)
        .set(updateData)
        .where(eq(customerGroups.id, id))
        .returning();

      // Log the customer group update activity
      await this.activityLogService.log(
        ActivityLogName.UPDATE,
        `Customer group "${existingCustomerGroup[0].name}" was updated`,
        { id: updatedCustomerGroup.id.toString(), type: 'customer-group' },
        { id: userId, type: 'user' },
        { customerGroupId: updatedCustomerGroup.id, businessId },
      );

      return {
        id: updatedCustomerGroup.id,
        message: 'Customer group updated successfully',
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update customer group: ${error.message}`,
      );
    }
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateCustomerGroupDto: UpdateCustomerGroupDto,
    metadata?: ActivityMetadata,
  ): Promise<CustomerGroupIdResponseDto> {
    return this.update(userId, businessId, id, updateCustomerGroupDto);
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<DeleteCustomerGroupResponseDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const existingCustomerGroup = await this.db
        .select()
        .from(customerGroups)
        .where(
          and(
            eq(customerGroups.id, id),
            eq(customerGroups.businessId, businessId),
            isNull(customerGroups.deletedAt),
          ),
        )
        .limit(1);

      if (existingCustomerGroup.length === 0) {
        throw new NotFoundException('Customer group not found');
      }

      const deletedAt = new Date();
      await this.db
        .update(customerGroups)
        .set({
          status: CustomerGroupStatus.INACTIVE,
          deletedAt,
          deletedBy: userId,
          updatedAt: deletedAt,
          updatedBy: userId,
        })
        .where(eq(customerGroups.id, id));

      // Log the customer group deletion activity
      await this.activityLogService.log(
        ActivityLogName.DELETE,
        `Customer group "${existingCustomerGroup[0].name}" was deleted`,
        { id: id.toString(), type: 'customer-group' },
        { id: userId, type: 'user' },
        { customerGroupId: id, businessId },
      );

      return {
        success: true,
        message: 'Customer group deleted successfully',
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to delete customer group: ${error.message}`,
      );
    }
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    customerGroupIds: string[],
    metadata?: ActivityMetadata,
  ): Promise<{
    deleted: number;
    message: string;
    deletedIds: string[];
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!customerGroupIds || customerGroupIds.length === 0) {
        throw new BadRequestException(
          'No customer group IDs provided for deletion',
        );
      }

      // Get all customer groups that exist and belong to the business
      const existingCustomerGroups = await this.db
        .select({
          id: customerGroups.id,
          name: customerGroups.name,
          businessId: customerGroups.businessId,
        })
        .from(customerGroups)
        .where(
          and(
            inArray(customerGroups.id, customerGroupIds),
            eq(customerGroups.businessId, businessId),
            isNull(customerGroups.deletedAt),
          ),
        );

      if (existingCustomerGroups.length === 0) {
        throw new NotFoundException(
          'No valid customer groups found for deletion',
        );
      }

      // TODO: Add check for customers assigned to these groups
      // This would prevent deletion of customer groups that have customers assigned to them
      // For now, we'll proceed with deletion

      const deletedIds: string[] = [];
      const currentTime = new Date();

      // Use transaction to ensure all deletions succeed or fail together
      await this.db.transaction(async (tx) => {
        for (const customerGroup of existingCustomerGroups) {
          // Soft delete the customer group
          await tx
            .update(customerGroups)
            .set({
              status: CustomerGroupStatus.INACTIVE,
              deletedBy: userId,
              deletedAt: currentTime,
              updatedAt: currentTime,
              updatedBy: userId,
            })
            .where(eq(customerGroups.id, customerGroup.id));

          deletedIds.push(customerGroup.id);

          // Log the activity for each deleted customer group
          await this.activityLogService.log(
            ActivityLogName.DELETE,
            `Customer group "${customerGroup.name}" was deleted (bulk)`,
            { id: customerGroup.id, type: 'customer-group' },
            { id: userId, type: 'user' },
            { customerGroupId: customerGroup.id, businessId },
          );
        }
      });

      return {
        deleted: deletedIds.length,
        message: `Successfully deleted ${deletedIds.length} customer groups`,
        deletedIds,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk delete customer groups: ${error.message}`,
      );
    }
  }

  async checkNameAvailability(
    userId: string,
    businessId: string | null,
    name: string,
  ): Promise<CustomerGroupNameAvailabilityResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if a customer group with the same name already exists for this business
    // Using ilike for case-insensitive comparison
    const existingCustomerGroup = await this.db
      .select()
      .from(customerGroups)
      .where(
        and(
          eq(customerGroups.businessId, businessId),
          ilike(customerGroups.name, name),
          isNull(customerGroups.deletedAt),
        ),
      )
      .then((results) => results[0]);

    const available = !existingCustomerGroup;

    return {
      data: {
        name,
        available,
        message: available
          ? 'Customer group name is available'
          : 'Customer group name is already taken',
      },
    };
  }

  private mapToDto(customerGroup: any): CustomerGroupDto {
    return {
      id: customerGroup.id,
      businessId: customerGroup.businessId,
      name: customerGroup.name,
      description: customerGroup.description,
      amount: customerGroup.amount,
      createdBy: customerGroup.createdBy,
      updatedBy: customerGroup.updatedBy,
      deletedBy: customerGroup.deletedBy,
      status: customerGroup.status,
      deletedAt: customerGroup.deletedAt?.toISOString() || null,
      createdAt: customerGroup.createdAt.toISOString(),
      updatedAt: customerGroup.updatedAt.toISOString(),
    };
  }
}
