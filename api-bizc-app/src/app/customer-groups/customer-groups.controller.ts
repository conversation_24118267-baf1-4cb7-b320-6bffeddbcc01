import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  Query,
  UseGuards,
} from '@nestjs/common';
import { CustomerGroupsService } from './customer-groups.service';
import { CreateCustomerGroupDto } from './dto/create-customer-group.dto';
import { UpdateCustomerGroupDto } from './dto/update-customer-group.dto';
import { CustomerGroupDto } from './dto/customer-group.dto';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiParam,
  ApiBearerAuth,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { CustomerGroupNameAvailabilityResponseDto } from './dto/check-customer-group-name.dto';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { Permission } from '../shared/types/permission.enum';
import { CustomerGroupSlimDto } from './dto/customer-group-slim.dto';
import { PaginatedCustomerGroupsResponseDto } from './dto/paginated-customer-groups-response.dto';
import { DeleteCustomerGroupResponseDto } from './dto/delete-customer-group-response.dto';
import { CustomerGroupIdResponseDto } from './dto/customer-group-id-response.dto';
import { BulkCreateCustomerGroupDto } from './dto/bulk-create-customer-group.dto';
import { BulkCustomerGroupIdsResponseDto } from './dto/bulk-customer-group-ids-response.dto';
import { BulkDeleteCustomerGroupDto } from './dto/bulk-delete-customer-group.dto';
import { BulkDeleteCustomerGroupResponseDto } from './dto/bulk-delete-customer-group-response.dto';
import { ActivityMetadata } from '../auth/decorators/activity-metadata.decorator';
import type { ActivityMetadata as ActivityMetadataType } from '../shared/types/activity-metadata.type';

@ApiTags('customer-groups')
@Controller('customer-groups')
@UseGuards(PermissionsGuard)
export class CustomerGroupsController {
  constructor(private readonly customerGroupsService: CustomerGroupsService) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.CUSTOMER_GROUP_CREATE)
  @ApiOperation({ summary: 'Create a new customer group' })
  @ApiBody({
    description: 'Customer group creation data',
    schema: {
      type: 'object',
      properties: {
        name: {
          type: 'string',
          example: 'VIP Customers',
          description: 'Customer group name',
        },
        description: {
          type: 'string',
          example: 'Premium customers with special privileges and discounts',
          description: 'Customer group description (optional)',
        },
        amount: {
          type: 'number',
          example: 1000,
          description: 'Amount threshold for the customer group',
        },
        status: {
          type: 'string',
          enum: ['active', 'inactive'],
          description: 'Customer group status',
          default: 'active',
        },
      },
      required: ['name', 'amount'],
    },
  })
  @ApiResponse({
    status: 201,
    description: 'The customer group has been successfully created',
    type: CustomerGroupIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Customer group name already exists',
  })
  create(
    @Request() req,
    @Body() createCustomerGroupDto: CreateCustomerGroupDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<CustomerGroupIdResponseDto> {
    return this.customerGroupsService.createAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      createCustomerGroupDto,
      metadata,
    );
  }

  @Post('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CUSTOMER_GROUP_CREATE)
  @ApiOperation({ summary: 'Bulk create customer groups' })
  @ApiBody({
    description: 'Bulk customer group creation data',
    schema: {
      type: 'object',
      properties: {
        customerGroups: {
          type: 'string',
          description: 'JSON string containing array of customer group objects',
          example:
            '[{"name":"VIP Customers","description":"Premium customers with special privileges","amount":1000},{"name":"Regular Customers","description":"Standard customer group","amount":500}]',
        },
      },
      required: ['customerGroups'],
    },
  })
  @ApiResponse({
    status: 201,
    description: 'The customer groups have been successfully created',
    type: BulkCustomerGroupIdsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or duplicate names in input',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Customer group names already exist',
  })
  bulkCreate(
    @Request() req,
    @Body() bulkCreateCustomerGroupDto: BulkCreateCustomerGroupDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkCustomerGroupIdsResponseDto> {
    return this.customerGroupsService.bulkCreateAndReturnIds(
      req.user.id,
      req.user.activeBusinessId,
      bulkCreateCustomerGroupDto.customerGroups,
      metadata,
    );
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.CUSTOMER_GROUP_READ)
  @ApiOperation({
    summary:
      'Get all customer groups for the active business with comprehensive filtering and pagination',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
    example: 10,
  })
  @ApiQuery({
    name: 'from',
    required: false,
    type: String,
    description: 'Start date for filtering (ISO string)',
    example: '2023-01-01T00:00:00Z',
  })
  @ApiQuery({
    name: 'to',
    required: false,
    type: String,
    description: 'End date for filtering (ISO string)',
    example: '2023-12-31T23:59:59Z',
  })
  @ApiQuery({
    name: 'name',
    required: false,
    type: String,
    description: 'Filter by customer group name (partial match)',
    example: 'VIP',
  })
  @ApiQuery({
    name: 'description',
    required: false,
    type: String,
    description: 'Filter by customer group description (partial match)',
    example: 'Premium',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    type: String,
    description:
      'Filter by status (comma-separated for multiple values). Supports URL encoding: status=active%2Cinactive',
    example: 'active,inactive',
  })
  @ApiQuery({
    name: 'amount',
    required: false,
    type: String,
    description: 'Filter by amount threshold',
    example: '1000',
  })
  @ApiQuery({
    name: 'filters',
    required: false,
    type: String,
    description:
      'Advanced filters as JSON string with operator support. Supported operators: iLike (contains), notILike (does not contain), eq (is), ne (is not), isEmpty (is empty), isNotEmpty (is not empty), gte (greater than or equal), lte (less than or equal), gt (greater than), lt (less than)',
    example:
      '[{"id":"name","value":"VIP","operator":"iLike","type":"text","rowId":"1"},{"id":"status","value":"active","operator":"eq","type":"select","rowId":"2"},{"id":"amount","value":"1000","operator":"gte","type":"number","rowId":"3"}]',
  })
  @ApiQuery({
    name: 'joinOperator',
    required: false,
    enum: ['and', 'or'],
    description: 'Join operator for advanced filters',
    example: 'and',
  })
  @ApiQuery({
    name: 'sort',
    required: false,
    type: String,
    description:
      'Sort configuration as JSON string. Supported fields: name, description, status, createdAt, updatedAt, numberOfCustomers',
    example: '[{"id":"name","desc":false}]',
  })
  @ApiResponse({
    status: 200,
    description:
      'Returns all customer groups with comprehensive filtering and pagination',
    type: PaginatedCustomerGroupsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid filters format',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Request() req,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('name') name?: string,
    @Query('description') description?: string,
    @Query('status') status?: string,
    @Query('filters') filters?: string,
    @Query('joinOperator') joinOperator?: 'and' | 'or',
    @Query('sort') sort?: string,
  ): Promise<PaginatedCustomerGroupsResponseDto> {
    return this.customerGroupsService.findAll(
      req.user.id,
      req.user.activeBusinessId,
      page ? parseInt(page.toString()) : undefined,
      limit ? parseInt(limit.toString()) : undefined,
      from,
      to,
      name,
      description,
      status,
      filters,
      joinOperator,
      sort,
    );
  }

  @Get('check-name-availability')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CUSTOMER_GROUP_READ)
  @ApiOperation({
    summary: 'Check if a customer group name is available for the business',
  })
  @ApiQuery({
    name: 'name',
    required: true,
    type: String,
    description: 'Customer group name to check',
    example: 'VIP Customers',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns whether the customer group name is available',
    type: CustomerGroupNameAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkNameAvailability(
    @Request() req,
    @Query('name') name: string,
  ): Promise<CustomerGroupNameAvailabilityResponseDto> {
    return this.customerGroupsService.checkNameAvailability(
      req.user.id,
      req.user.activeBusinessId,
      name,
    );
  }

  @Get('slim')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CUSTOMER_GROUP_READ)
  @ApiOperation({
    summary: 'Get all active customer groups in slim format for dropdowns',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns all active customer groups (essential fields only)',
    type: [CustomerGroupSlimDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllSlim(@Request() req): Promise<CustomerGroupSlimDto[]> {
    return this.customerGroupsService.findAllSlim(
      req.user.id,
      req.user.activeBusinessId,
    );
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CUSTOMER_GROUP_READ)
  @ApiOperation({ summary: 'Get a customer group by id' })
  @ApiParam({
    name: 'id',
    description: 'Customer group ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the customer group',
    type: CustomerGroupDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Customer group not found',
  })
  findOne(@Request() req, @Param('id') id: string): Promise<CustomerGroupDto> {
    return this.customerGroupsService.findOne(
      req.user.id,
      id,
      req.user.activeBusinessId,
    );
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CUSTOMER_GROUP_UPDATE)
  @ApiOperation({ summary: 'Update a customer group' })
  @ApiParam({
    name: 'id',
    description: 'Customer group ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiBody({
    description: 'Customer group update data',
    schema: {
      type: 'object',
      properties: {
        name: {
          type: 'string',
          example: 'Premium Customers',
          description: 'Customer group name',
        },
        description: {
          type: 'string',
          example:
            'Premium customers with exclusive benefits and higher spending limits',
          description: 'Customer group description',
        },
        amount: {
          type: 'number',
          example: 1500,
          description: 'Amount threshold for the customer group',
        },
        status: {
          type: 'string',
          enum: ['active', 'inactive'],
          description: 'Customer group status',
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'The customer group has been successfully updated',
    type: CustomerGroupIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Customer group not found',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Customer group name already exists',
  })
  update(
    @Request() req,
    @Param('id') id: string,
    @Body() updateCustomerGroupDto: UpdateCustomerGroupDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<CustomerGroupIdResponseDto> {
    return this.customerGroupsService.updateAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateCustomerGroupDto,
      metadata,
    );
  }

  @Delete('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CUSTOMER_GROUP_DELETE)
  @ApiOperation({ summary: 'Bulk delete customer groups' })
  @ApiBody({
    description: 'Array of customer group IDs to delete',
    type: BulkDeleteCustomerGroupDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Customer groups have been successfully deleted',
    type: BulkDeleteCustomerGroupResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or customer groups not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'One or more customer groups not found',
  })
  async bulkDelete(
    @Request() req,
    @Body() bulkDeleteCustomerGroupDto: BulkDeleteCustomerGroupDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkDeleteCustomerGroupResponseDto> {
    return this.customerGroupsService.bulkDelete(
      req.user.id,
      req.user.activeBusinessId,
      bulkDeleteCustomerGroupDto.customerGroupIds,
      metadata,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CUSTOMER_GROUP_DELETE)
  @ApiOperation({ summary: 'Delete a customer group' })
  @ApiParam({
    name: 'id',
    description: 'Customer group ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiResponse({
    status: 200,
    description: 'The customer group has been successfully deleted',
    type: DeleteCustomerGroupResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Customer group not found',
  })
  remove(
    @Request() req,
    @Param('id') id: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<DeleteCustomerGroupResponseDto> {
    return this.customerGroupsService.remove(
      req.user.id,
      req.user.activeBusinessId,
      id,
      metadata,
    );
  }
}
