import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsEnum, IsNotEmpty, IsUUID } from 'class-validator';
import { BrandStatus } from '../../shared/types';

export class BulkUpdateBrandStatusDto {
  @ApiProperty({
    description: 'Array of brand IDs to update',
    example: [
      '123e4567-e89b-12d3-a456-************',
      '123e4567-e89b-12d3-a456-************',
    ],
    type: [String],
  })
  @IsArray()
  @IsUUID('4', { each: true })
  @IsNotEmpty()
  brandIds: string[];

  @ApiProperty({
    description: 'The status to set for all brands',
    enum: BrandStatus,
    example: BrandStatus.ACTIVE,
  })
  @IsEnum(BrandStatus)
  @IsNotEmpty()
  status: BrandStatus;
}

export class BulkUpdateBrandStatusResponseDto {
  @ApiProperty({
    description: 'Number of brands updated',
    example: 5,
  })
  updated: number;

  @ApiProperty({
    description: 'Success message',
    example: 'Successfully updated status for 5 brands',
  })
  message: string;

  @ApiProperty({
    description: 'Array of updated brand IDs',
    example: [
      '123e4567-e89b-12d3-a456-************',
      '123e4567-e89b-12d3-a456-************',
    ],
    type: [String],
  })
  updatedIds: string[];

  @ApiProperty({
    description: 'Array of failed updates (if any)',
    required: false,
    type: [Object],
    example: [
      {
        brandId: '123e4567-e89b-12d3-a456-************',
        error: 'Brand not found',
      },
    ],
  })
  failed?: Array<{ brandId: string; error: string }>;
}
