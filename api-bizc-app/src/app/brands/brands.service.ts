import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateBrandDto } from './dto/create-brand.dto';
import { UpdateBrandDto } from './dto/update-brand.dto';
import { BrandDto } from './dto/brand.dto';
import { BrandSlimDto } from './dto/brand-slim.dto';
import { brands } from '../drizzle/schema/brands.schema';
import { media } from '../drizzle/schema/media.schema';
import { products } from '../drizzle/schema/products.schema';
import {
  eq,
  and,
  ilike,
  sql,
  gte,
  lte,
  desc,
  asc,
  inArray,
  or,
} from 'drizzle-orm';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { BrandStatus } from '../shared/types';
import { MediaService } from '../media/media.service';
import { GcsUploadService } from '../gcs-upload/gcs-upload.service';
import { UsersService } from '../users/users.service';
import type { ActivityMetadata } from '../shared/types/activity-metadata.type';

@Injectable()
export class BrandsService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly mediaService: MediaService,
    private readonly gcsUploadService: GcsUploadService,
    private readonly usersService: UsersService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createBrandDto: CreateBrandDto,
    logoFile?: Express.Multer.File,
    ogImageFile?: Express.Multer.File,
  ): Promise<BrandDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a brand with the same name already exists for this business
      // Using ilike for case-insensitive comparison
      const existingBrand = await this.db
        .select()
        .from(brands)
        .where(
          and(
            eq(brands.businessId, businessId),
            ilike(brands.name, createBrandDto.name),
            eq(brands.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingBrand) {
        throw new ConflictException(
          `A brand with the name '${createBrandDto.name}' already exists for this business`,
        );
      }

      // Create slug from name if not provided
      if (!createBrandDto.slug) {
        createBrandDto.slug = createBrandDto.name
          .toLowerCase()
          .replace(/\s+/g, '-')
          .replace(/[^a-z0-9-]/g, '');
      }

      // Check if a brand with the same slug already exists for this business
      if (createBrandDto.slug) {
        const existingSlugBrand = await this.db
          .select()
          .from(brands)
          .where(
            and(
              eq(brands.businessId, businessId),
              eq(brands.slug, createBrandDto.slug),
              eq(brands.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (existingSlugBrand) {
          throw new ConflictException(
            `A brand with the slug '${createBrandDto.slug}' already exists for this business`,
          );
        }
      }

      let logoId: string | undefined;
      let ogImageId: string | undefined;

      // Upload logo if provided
      if (logoFile) {
        const uploadedMedia = await this.mediaService.uploadMedia(
          logoFile,
          'brands',
          businessId,
          userId,
        );
        logoId = uploadedMedia.id;
      }

      // Upload OG image if provided
      if (ogImageFile) {
        const uploadedOgMedia = await this.mediaService.uploadMedia(
          ogImageFile,
          'brands/og-images',
          businessId,
          userId,
        );
        ogImageId = uploadedOgMedia.id;
      }

      // Use a transaction to ensure position reordering and brand creation are atomic
      const newBrand = await this.db.transaction(async (tx) => {
        // Shift all existing brands down by 1 position to make room at position 1
        await this.reorderPositions(tx, businessId, 1);

        // Insert new brand at position 1
        const [brand] = await tx
          .insert(brands)
          .values({
            businessId,
            name: createBrandDto.name,
            slug: createBrandDto.slug,
            availableOnline: createBrandDto.availableOnline ?? false,
            position: 1, // Always create new brands at position 1 (first)
            logo: logoId,
            seoTitle: createBrandDto.seoTitle,
            seoDescription: createBrandDto.seoDescription,
            seoKeywords: createBrandDto.seoKeywords,
            ogImage: ogImageId,
            createdBy: userId,
            status: createBrandDto.status ?? BrandStatus.ACTIVE,
          })
          .returning();

        return brand;
      });

      // Log the activity
      await this.activityLogService.log(
        ActivityLogName.CREATE,
        `Brand '${newBrand.name}' was created`,
        { id: newBrand.id, type: 'brand' },
        { id: userId, type: 'user' },
        {
          brandId: newBrand.id,
          brandName: newBrand.name,
          businessId,
        },
      );

      return this.mapToBrandDto(newBrand);
    } catch (error) {
      console.error('Error creating brand:', error);
      throw error;
    }
  }

  async findAllOptimized(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    name?: string,
    slug?: string,
    status?: string,
    availableOnline?: string,
    sort?: string,
  ): Promise<{
    data: any[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const offset = (page - 1) * limit;

      // Build dynamic where conditions
      const whereConditions: any[] = [
        eq(brands.businessId, businessId),
        eq(brands.isDeleted, false),
      ];

      // Add filters
      if (from && to) {
        whereConditions.push(gte(brands.createdAt, new Date(from)));
        whereConditions.push(lte(brands.createdAt, new Date(to)));
      }

      if (name) {
        whereConditions.push(ilike(brands.name, `%${name}%`));
      }

      if (slug) {
        whereConditions.push(ilike(brands.slug, `%${slug}%`));
      }

      if (status) {
        whereConditions.push(eq(brands.status, status as BrandStatus));
      }

      if (availableOnline !== undefined) {
        const isAvailableOnline = availableOnline.toLowerCase() === 'true';
        whereConditions.push(eq(brands.availableOnline, isAvailableOnline));
      }

      // Determine sort order
      let orderBy;
      if (sort) {
        const [field, direction] = sort.split(':');
        const isDesc = direction?.toLowerCase() === 'desc';

        switch (field) {
          case 'name':
            orderBy = isDesc ? desc(brands.name) : asc(brands.name);
            break;
          case 'position':
            orderBy = isDesc ? desc(brands.position) : asc(brands.position);
            break;
          case 'createdAt':
            orderBy = isDesc ? desc(brands.createdAt) : asc(brands.createdAt);
            break;
          case 'updatedAt':
            orderBy = isDesc ? desc(brands.updatedAt) : asc(brands.updatedAt);
            break;
          default:
            orderBy = asc(brands.position);
        }
      } else {
        orderBy = asc(brands.position);
      }

      // Get total count
      const totalResult = await this.db
        .select({ count: sql`count(*)` })
        .from(brands)
        .where(and(...whereConditions));

      const total = Number(totalResult[0].count);
      const totalPages = Math.ceil(total / limit);

      // Get brands with pagination
      const brandsResult = await this.db
        .select()
        .from(brands)
        .where(and(...whereConditions))
        .orderBy(orderBy)
        .limit(limit)
        .offset(offset);

      // Map to optimized list DTOs
      const brandListDtos = await Promise.all(
        brandsResult.map((brand) => this.mapToBrandListDto(brand)),
      );

      return {
        data: brandListDtos,
        meta: {
          total,
          page,
          totalPages,
        },
      };
    } catch (error) {
      console.error('Error finding brands:', error);
      throw error;
    }
  }

  async findAll(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    name?: string,
    slug?: string,
    status?: string,
    availableOnline?: string,
    sort?: string,
  ): Promise<{
    data: BrandDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const offset = (page - 1) * limit;

      // Build dynamic where conditions
      const whereConditions: any[] = [
        eq(brands.businessId, businessId),
        eq(brands.isDeleted, false),
      ];

      // Add filters
      if (from && to) {
        whereConditions.push(gte(brands.createdAt, new Date(from)));
        whereConditions.push(lte(brands.createdAt, new Date(to)));
      }

      if (name) {
        whereConditions.push(ilike(brands.name, `%${name}%`));
      }

      if (slug) {
        whereConditions.push(ilike(brands.slug, `%${slug}%`));
      }

      if (status) {
        whereConditions.push(eq(brands.status, status as BrandStatus));
      }

      if (availableOnline !== undefined) {
        const isAvailableOnline = availableOnline.toLowerCase() === 'true';
        whereConditions.push(eq(brands.availableOnline, isAvailableOnline));
      }

      // Determine sort order
      let orderBy;
      if (sort) {
        const [field, direction] = sort.split(':');
        const isDesc = direction?.toLowerCase() === 'desc';

        switch (field) {
          case 'name':
            orderBy = isDesc ? desc(brands.name) : asc(brands.name);
            break;
          case 'position':
            orderBy = isDesc ? desc(brands.position) : asc(brands.position);
            break;
          case 'createdAt':
            orderBy = isDesc ? desc(brands.createdAt) : asc(brands.createdAt);
            break;
          case 'updatedAt':
            orderBy = isDesc ? desc(brands.updatedAt) : asc(brands.updatedAt);
            break;
          default:
            orderBy = asc(brands.position);
        }
      } else {
        orderBy = asc(brands.position);
      }

      // Get total count
      const totalResult = await this.db
        .select({ count: sql`count(*)` })
        .from(brands)
        .where(and(...whereConditions));

      const total = Number(totalResult[0].count);
      const totalPages = Math.ceil(total / limit);

      // Get brands with pagination
      const brandsResult = await this.db
        .select()
        .from(brands)
        .where(and(...whereConditions))
        .orderBy(orderBy)
        .limit(limit)
        .offset(offset);

      // Map to DTOs
      const brandDtos = await Promise.all(
        brandsResult.map((brand) => this.mapToBrandDto(brand)),
      );

      return {
        data: brandDtos,
        meta: {
          total,
          page,
          totalPages,
        },
      };
    } catch (error) {
      console.error('Error finding brands:', error);
      throw error;
    }
  }

  async checkNameAvailability(
    userId: string,
    businessId: string | null,
    name: string,
  ): Promise<{ available: boolean }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const existingBrand = await this.db
        .select()
        .from(brands)
        .where(
          and(
            eq(brands.businessId, businessId),
            ilike(brands.name, name),
            eq(brands.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      return { available: !existingBrand };
    } catch (error) {
      console.error('Error checking brand name availability:', error);
      throw error;
    }
  }

  async checkSlugAvailability(
    userId: string,
    businessId: string | null,
    slug: string,
  ): Promise<{ available: boolean }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const existingBrand = await this.db
        .select()
        .from(brands)
        .where(
          and(
            eq(brands.businessId, businessId),
            eq(brands.slug, slug),
            eq(brands.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      return { available: !existingBrand };
    } catch (error) {
      console.error('Error checking brand slug availability:', error);
      throw error;
    }
  }

  async findOne(userId: string, id: string): Promise<BrandDto> {
    try {
      const brand = await this.db
        .select()
        .from(brands)
        .where(and(eq(brands.id, id), eq(brands.isDeleted, false)))
        .then((results) => results[0]);

      if (!brand) {
        throw new NotFoundException('Brand not found');
      }

      return this.mapToBrandDto(brand);
    } catch (error) {
      console.error('Error finding brand:', error);
      throw error;
    }
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateBrandDto: UpdateBrandDto,
    logoFile?: Express.Multer.File,
    ogImageFile?: Express.Multer.File,
  ): Promise<BrandDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if brand exists
      const existingBrand = await this.db
        .select()
        .from(brands)
        .where(
          and(
            eq(brands.id, id),
            eq(brands.businessId, businessId),
            eq(brands.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingBrand) {
        throw new NotFoundException('Brand not found');
      }

      // Check if name is being updated and if it conflicts
      if (updateBrandDto.name && updateBrandDto.name !== existingBrand.name) {
        const conflictingBrand = await this.db
          .select()
          .from(brands)
          .where(
            and(
              eq(brands.businessId, businessId),
              ilike(brands.name, updateBrandDto.name),
              eq(brands.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (conflictingBrand && conflictingBrand.id !== id) {
          throw new ConflictException(
            `A brand with the name '${updateBrandDto.name}' already exists for this business`,
          );
        }
      }

      // Check if slug is being updated and if it conflicts
      if (updateBrandDto.slug && updateBrandDto.slug !== existingBrand.slug) {
        const conflictingSlugBrand = await this.db
          .select()
          .from(brands)
          .where(
            and(
              eq(brands.businessId, businessId),
              eq(brands.slug, updateBrandDto.slug),
              eq(brands.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (conflictingSlugBrand && conflictingSlugBrand.id !== id) {
          throw new ConflictException(
            `A brand with the slug '${updateBrandDto.slug}' already exists for this business`,
          );
        }
      }

      let logoId = existingBrand.logo;
      let ogImageId = existingBrand.ogImage;

      // Upload new logo if provided
      if (logoFile) {
        const uploadedMedia = await this.mediaService.uploadMedia(
          logoFile,
          'brands',
          businessId,
          userId,
        );
        logoId = uploadedMedia.id;
      }

      // Upload new OG image if provided
      if (ogImageFile) {
        const uploadedOgMedia = await this.mediaService.uploadMedia(
          ogImageFile,
          'brands/og-images',
          businessId,
          userId,
        );
        ogImageId = uploadedOgMedia.id;
      }

      // Update brand
      const [updatedBrand] = await this.db
        .update(brands)
        .set({
          ...updateBrandDto,
          logo: logoId,
          ogImage: ogImageId,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(brands.id, id))
        .returning();

      // Log the activity
      await this.activityLogService.log(
        ActivityLogName.UPDATE,
        `Brand '${updatedBrand.name}' was updated`,
        { id: updatedBrand.id, type: 'brand' },
        { id: userId, type: 'user' },
        {
          brandId: updatedBrand.id,
          brandName: updatedBrand.name,
          businessId,
        },
      );

      return this.mapToBrandDto(updatedBrand);
    } catch (error) {
      console.error('Error updating brand:', error);
      throw error;
    }
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ success: boolean; message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if brand exists
      const existingBrand = await this.db
        .select()
        .from(brands)
        .where(
          and(
            eq(brands.id, id),
            eq(brands.businessId, businessId),
            eq(brands.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingBrand) {
        throw new NotFoundException('Brand not found');
      }

      // Soft delete the brand
      await this.db
        .update(brands)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(brands.id, id));

      // Log the activity
      await this.activityLogService.log(
        ActivityLogName.DELETE,
        `Brand '${existingBrand.name}' was deleted`,
        { id: existingBrand.id, type: 'brand' },
        { id: userId, type: 'user' },
        {
          brandId: existingBrand.id,
          brandName: existingBrand.name,
          businessId,
        },
      );

      return {
        success: true,
        message: 'Brand deleted successfully',
      };
    } catch (error) {
      console.error('Error deleting brand:', error);
      throw error;
    }
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    brandIds: string[],
    metadata?: ActivityMetadata,
  ): Promise<{
    deleted: number;
    message: string;
    deletedIds: string[];
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!brandIds || brandIds.length === 0) {
        throw new BadRequestException('No brand IDs provided');
      }

      // Check which brands exist and belong to this business
      const existingBrands = await this.db
        .select()
        .from(brands)
        .where(
          and(
            inArray(brands.id, brandIds),
            eq(brands.businessId, businessId),
            eq(brands.isDeleted, false),
          ),
        );

      if (existingBrands.length === 0) {
        throw new NotFoundException('No brands found to delete');
      }

      const existingBrandIds = existingBrands.map((brand) => brand.id);

      // Soft delete the brands
      await this.db
        .update(brands)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(inArray(brands.id, existingBrandIds));

      // Log the activity for each deleted brand
      for (const brand of existingBrands) {
        await this.activityLogService.log(
          ActivityLogName.DELETE,
          `Brand '${brand.name}' was deleted via bulk operation`,
          { id: brand.id, type: 'brand' },
          { id: userId, type: 'user' },
          {
            brandId: brand.id,
            brandName: brand.name,
            businessId,
          },
        );
      }

      return {
        deleted: existingBrands.length,
        message: `${existingBrands.length} brands deleted successfully`,
        deletedIds: existingBrandIds,
      };
    } catch (error) {
      console.error('Error bulk deleting brands:', error);
      throw error;
    }
  }

  async findAllSlim(
    userId: string,
    businessId: string | null,
  ): Promise<BrandSlimDto[]> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const brandsResult = await this.db
        .select({
          id: brands.id,
          name: brands.name,
          logo: brands.logo,
          logoFileName: media.fileName,
          position: brands.position,
        })
        .from(brands)
        .leftJoin(media, eq(brands.logo, media.id))
        .where(
          and(eq(brands.businessId, businessId), eq(brands.isDeleted, false)),
        )
        .orderBy(asc(brands.position), asc(brands.name));

      return await Promise.all(
        brandsResult.map(async (brand) => {
          let logoUrl: string | undefined;

          if (brand.logoFileName) {
            try {
              // Generate signed URL with 60 minutes expiration for the logo
              logoUrl = await this.gcsUploadService.generateSignedUrl(
                brand.logoFileName,
                'brands', // folder where brand logos are stored
                60, // expiration in minutes
              );
            } catch (error) {
              console.warn(
                `Failed to generate signed URL for brand ${brand.id} logo:`,
                error.message,
              );
            }
          }

          return {
            id: brand.id,
            name: brand.name,
            logo: logoUrl,
            position: brand.position,
          };
        }),
      );
    } catch (error) {
      console.error('Error finding slim brands:', error);
      throw error;
    }
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createBrandDto: CreateBrandDto,
    logoFile?: Express.Multer.File,
    ogImageFile?: Express.Multer.File,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    const brand = await this.create(
      userId,
      businessId,
      createBrandDto,
      logoFile,
      ogImageFile,
    );
    return { id: brand.id };
  }

  async bulkCreate(
    userId: string,
    businessId: string | null,
    createBrandsDto: CreateBrandDto[],
    logoFiles?: Express.Multer.File[],
  ): Promise<BrandDto[]> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const createdBrands: BrandDto[] = [];

      // Use transaction for atomic bulk creation with proper position handling
      await this.db.transaction(async (tx) => {
        // Shift all existing brands down by the number of new brands being added
        await tx
          .update(brands)
          .set({
            position: sql`${brands.position} + ${createBrandsDto.length}`,
            updatedAt: new Date(),
          })
          .where(
            and(eq(brands.businessId, businessId), eq(brands.isDeleted, false)),
          );

        // Create brands with positions starting from 1
        for (let i = 0; i < createBrandsDto.length; i++) {
          const brandDto = createBrandsDto[i];
          let logoFile: Express.Multer.File | undefined;
          let logoId: string | undefined;

          // Map logo file based on logoIndex
          if (
            logoFiles &&
            brandDto.logoIndex !== undefined &&
            logoFiles[brandDto.logoIndex]
          ) {
            logoFile = logoFiles[brandDto.logoIndex];
          }

          // Upload logo if provided
          if (logoFile) {
            const uploadedMedia = await this.mediaService.uploadMedia(
              logoFile,
              'brands',
              businessId,
              userId,
            );
            logoId = uploadedMedia.id;
          }

          // Create slug from name if not provided
          if (!brandDto.slug) {
            brandDto.slug = brandDto.name
              .toLowerCase()
              .replace(/\s+/g, '-')
              .replace(/[^a-z0-9-]/g, '');
          }

          // Create the brand with proper position (starting from position 1)
          const [newBrand] = await tx
            .insert(brands)
            .values({
              businessId,
              name: brandDto.name,
              slug: brandDto.slug,
              availableOnline: brandDto.availableOnline ?? false,
              position: i + 1, // Position starts from 1 for new brands
              logo: logoId,
              seoTitle: brandDto.seoTitle,
              seoDescription: brandDto.seoDescription,
              seoKeywords: brandDto.seoKeywords,
              createdBy: userId,
              status: brandDto.status ?? BrandStatus.ACTIVE,
            })
            .returning();

          const brandDto_mapped = await this.mapToBrandDto(newBrand);
          createdBrands.push(brandDto_mapped);

          // Log the activity
          await this.activityLogService.log(
            ActivityLogName.CREATE,
            `Brand '${newBrand.name}' was created via bulk operation`,
            { id: newBrand.id, type: 'brand' },
            { id: userId, type: 'user' },
            {
              brandId: newBrand.id,
              brandName: newBrand.name,
              businessId,
            },
          );
        }
      });

      return createdBrands;
    } catch (error) {
      console.error('Error bulk creating brands:', error);
      throw error;
    }
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createBrandsDto: CreateBrandDto[],
    logoFiles?: Express.Multer.File[],
    metadata?: ActivityMetadata,
  ): Promise<{ ids: string[] }> {
    const brands = await this.bulkCreate(
      userId,
      businessId,
      createBrandsDto,
      logoFiles,
    );
    return { ids: brands.map((brand) => brand.id) };
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateBrandDto: UpdateBrandDto,
    logoFile?: Express.Multer.File,
    ogImageFile?: Express.Multer.File,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    const brand = await this.update(
      userId,
      businessId,
      id,
      updateBrandDto,
      logoFile,
      ogImageFile,
    );
    return { id: brand.id };
  }

  async updateBrandPositions(
    userId: string,
    businessId: string | null,
    updates: { id: string; position: number }[],
    metadata?: ActivityMetadata,
  ): Promise<{ updated: number; message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!updates || updates.length === 0) {
        throw new BadRequestException('No position updates provided');
      }

      // Validate position values (must be positive integers)
      for (const update of updates) {
        if (!Number.isInteger(update.position) || update.position < 1) {
          throw new BadRequestException(
            `Invalid position ${update.position} for brand ${update.id}. Position must be a positive integer starting from 1.`,
          );
        }
      }

      let updated = 0;

      // Use transaction for atomic updates
      await this.db.transaction(async (tx) => {
        // Update positions using batch updates for better performance
        await this.batchUpdatePositions(tx, businessId, updates);
        updated = updates.length;
      });

      // Log the activity
      await this.activityLogService.log(
        ActivityLogName.UPDATE,
        `${updated} brand positions were updated`,
        undefined,
        { id: userId, type: 'user' },
        {
          updatedCount: updated,
          updates,
          businessId,
        },
      );

      return {
        updated,
        message: `Successfully updated ${updated} brand positions`,
      };
    } catch (error) {
      console.error('Error updating brand positions:', error);
      throw error;
    }
  }

  async bulkUpdateBrandStatus(
    userId: string,
    businessId: string | null,
    brandIds: string[],
    status: BrandStatus,
    metadata?: ActivityMetadata,
  ): Promise<{
    updated: number;
    updatedIds: string[];
    failed: Array<{ brandId: string; error: string }>;
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!brandIds || brandIds.length === 0) {
        throw new BadRequestException(
          'No brand IDs provided for status update',
        );
      }

      let updatedCount = 0;
      const updatedIds: string[] = [];
      const failed: Array<{ brandId: string; error: string }> = [];

      // Process each brand ID
      await this.db.transaction(async (tx) => {
        for (const brandId of brandIds) {
          try {
            // Check if brand exists and belongs to the business
            const existingBrand = await tx
              .select()
              .from(brands)
              .where(
                and(
                  eq(brands.id, brandId),
                  eq(brands.businessId, businessId),
                  eq(brands.isDeleted, false),
                ),
              )
              .then((results) => results[0]);

            if (!existingBrand) {
              failed.push({
                brandId,
                error: 'Brand not found or access denied',
              });
              continue;
            }

            // Skip if status is already the same
            if (existingBrand.status === status) {
              failed.push({
                brandId,
                error: `Brand already has status: ${status}`,
              });
              continue;
            }

            // Update the brand status
            await tx
              .update(brands)
              .set({
                status,
                updatedAt: new Date(),
              })
              .where(
                and(
                  eq(brands.id, brandId),
                  eq(brands.businessId, businessId),
                  eq(brands.isDeleted, false),
                ),
              );

            updatedCount++;
            updatedIds.push(brandId);
          } catch (error) {
            failed.push({
              brandId,
              error: `Failed to update: ${error.message}`,
            });
          }
        }
      });

      // Log bulk status change operation if any brands were updated
      if (updatedIds.length > 0) {
        await this.activityLogService.logBulkOperation(
          ActivityType.BULK_STATUS_CHANGE,
          EntityType.BRAND,
          updatedIds,
          { status },
          userId,
          businessId,
          {
            filterCriteria: { brandIds, targetStatus: status },
            failures: failed.map((f) => ({ id: f.brandId, error: f.error })),
            executionStrategy: ExecutionStrategy.SEQUENTIAL,
            source: metadata?.source || ActivitySource.WEB,
            ipAddress: metadata?.ipAddress,
            userAgent: metadata?.userAgent,
            sessionId: metadata?.sessionId,
          },
        );
      }

      return {
        updated: updatedCount,
        updatedIds,
        failed,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk update brand status: ${error.message}`,
      );
    }
  }

  private async getProductsCountForBrand(
    brandId: string,
    businessId: string,
  ): Promise<number> {
    try {
      const result = await this.db
        .select({ count: sql`count(*)` })
        .from(products)
        .where(
          and(
            eq(products.brandId, brandId),
            eq(products.businessId, businessId),
            eq(products.isDeleted, false),
          ),
        );

      return Number(result[0].count);
    } catch (error) {
      console.error('Error getting products count for brand:', error);
      return 0;
    }
  }

  private async mapToBrandListDto(
    brand: typeof brands.$inferSelect,
  ): Promise<any> {
    try {
      // Get products count
      const productsCount = await this.getProductsCountForBrand(
        brand.id,
        brand.businessId,
      );

      const brandListDto: any = {
        id: brand.id,
        name: brand.name,
        slug: brand.slug,
        status: brand.status,
        availableOnline: brand.availableOnline,
        position: brand.position,
        productsCount,
      };

      // Fetch logo information and generate signed URL if logo exists
      if (brand.logo) {
        try {
          const logoData = await this.mediaService.findById(
            brand.logo,
            brand.businessId,
          );

          // Generate signed URL with 60 minutes expiration for the logo
          brandListDto.logo = await this.gcsUploadService.generateSignedUrl(
            logoData.fileName,
            'brands', // folder where brand logos are stored
            60, // expiration in minutes
          );
        } catch (error) {
          console.warn(
            `Failed to generate signed URL for brand ${brand.id} logo:`,
            error.message,
          );
        }
      }

      return brandListDto;
    } catch (error) {
      console.error('Error mapping brand to list DTO:', error);
      throw error;
    }
  }

  private async mapToBrandDto(
    brand: typeof brands.$inferSelect,
  ): Promise<BrandDto> {
    try {
      // Get creator and updater names
      const creatorPromise = this.usersService.getUserName(brand.createdBy);
      const updaterPromise = brand.updatedBy
        ? this.usersService.getUserName(brand.updatedBy)
        : Promise.resolve(null);

      // Get products count
      const productsCountPromise = this.getProductsCountForBrand(
        brand.id,
        brand.businessId,
      );

      const [creator, updater, productsCount] = await Promise.all([
        creatorPromise,
        updaterPromise,
        productsCountPromise,
      ]);

      const brandDto: BrandDto = {
        id: brand.id,
        businessId: brand.businessId,
        name: brand.name,
        slug: brand.slug,
        availableOnline: brand.availableOnline,
        position: brand.position,
        seoTitle: brand.seoTitle,
        seoDescription: brand.seoDescription,
        seoKeywords: brand.seoKeywords,
        createdBy: creator || 'Unknown User',
        updatedBy: updater || undefined,
        status: brand.status,
        productsCount,
        createdAt: brand.createdAt,
        updatedAt: brand.updatedAt,
      };

      // Fetch logo information and generate signed URL if logo exists
      if (brand.logo) {
        try {
          const logoData = await this.mediaService.findById(
            brand.logo,
            brand.businessId,
          );

          // Generate signed URL with 60 minutes expiration for the logo
          brandDto.logo = await this.gcsUploadService.generateSignedUrl(
            logoData.fileName,
            'brands', // folder where brand logos are stored
            60, // expiration in minutes
          );
        } catch (error) {
          console.warn(
            `Failed to generate signed URL for brand ${brand.id} logo:`,
            error.message,
          );
        }
      }

      // Fetch OG image information and generate signed URL if ogImage exists
      if (brand.ogImage) {
        try {
          const ogImageData = await this.mediaService.findById(
            brand.ogImage,
            brand.businessId,
          );

          // Generate signed URL with 60 minutes expiration for the OG image
          brandDto.ogImage = await this.gcsUploadService.generateSignedUrl(
            ogImageData.fileName,
            'brands/og-images', // folder where OG images are stored
            60, // expiration in minutes
          );
        } catch (error) {
          console.warn(
            `Failed to generate signed URL for brand ${brand.id} OG image:`,
            error.message,
          );
        }
      }

      return brandDto;
    } catch (error) {
      console.error('Error mapping brand to DTO:', error);
      throw error;
    }
  }

  /**
   * Optimized reorder positions when inserting a brand at a specific position
   * Uses batch updates and optimized queries for better performance
   * @param tx - Database transaction
   * @param businessId - The business ID
   * @param insertPosition - The position where the new brand will be inserted
   */
  private async reorderPositions(
    tx: any,
    businessId: string,
    insertPosition: number,
  ): Promise<void> {
    try {
      // Use a single optimized query with proper indexing
      // This query will be much faster with the composite index on (businessId, position, isDeleted)
      await tx
        .update(brands)
        .set({
          position: sql`${brands.position} + 1`,
          updatedAt: new Date(),
        })
        .where(
          and(
            eq(brands.businessId, businessId),
            gte(brands.position, insertPosition),
            eq(brands.isDeleted, false),
          ),
        );
    } catch (error) {
      console.error('Error reordering brand positions:', error);
      throw error;
    }
  }

  /**
   * Batch update positions for multiple brands
   * @param tx - Database transaction
   * @param businessId - The business ID
   * @param updates - Array of position updates
   */
  private async batchUpdatePositions(
    tx: any,
    businessId: string,
    updates: { id: string; position: number }[],
  ): Promise<void> {
    try {
      // Update positions in batches for better performance
      const batchSize = 50;
      for (let i = 0; i < updates.length; i += batchSize) {
        const batch = updates.slice(i, i + batchSize);

        // Use Promise.all for concurrent updates within the batch
        await Promise.all(
          batch.map(async (update) => {
            await tx
              .update(brands)
              .set({
                position: update.position,
                updatedAt: new Date(),
              })
              .where(
                and(
                  eq(brands.id, update.id),
                  eq(brands.businessId, businessId),
                  eq(brands.isDeleted, false),
                ),
              );
          }),
        );
      }
    } catch (error) {
      console.error('Error batch updating brand positions:', error);
      throw error;
    }
  }
}
