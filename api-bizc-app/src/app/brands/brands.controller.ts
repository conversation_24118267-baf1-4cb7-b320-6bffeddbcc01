import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  Query,
  UseGuards,
  UseInterceptors,
  UploadedFiles,
} from '@nestjs/common';
import {
  FilesInterceptor,
  FileFieldsInterceptor,
} from '@nestjs/platform-express';
import { BrandsService } from './brands.service';
import { CreateBrandDto } from './dto/create-brand.dto';
import { UpdateBrandDto } from './dto/update-brand.dto';
import { BrandDto } from './dto/brand.dto';
import { BrandSlimDto } from './dto/brand-slim.dto';
import { BrandIdResponseDto } from './dto/brand-id-response.dto';
import { BulkBrandIdsResponseDto } from './dto/bulk-brand-ids-response.dto';
import { BulkCreateBrandDto } from './dto/bulk-create-brand.dto';
import { DeleteBrandResponseDto } from './dto/delete-brand-response.dto';
import { BulkDeleteBrandDto } from './dto/bulk-delete-brand.dto';
import { BulkDeleteBrandResponseDto } from './dto/bulk-delete-brand-response.dto';
import { PaginatedBrandsResponseDto } from './dto/paginated-brands-response.dto';
import {
  UpdateBrandPositionsDto,
  UpdateBrandPositionsResponseDto,
} from './dto/update-brand-positions.dto';
import {
  BulkUpdateBrandStatusDto,
  BulkUpdateBrandStatusResponseDto,
} from './dto/bulk-update-brand-status.dto';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiParam,
  ApiBearerAuth,
  ApiQuery,
  ApiConsumes,
  ApiBody,
} from '@nestjs/swagger';
import { BrandNameAvailabilityResponseDto } from './dto/check-brand-name.dto';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { ActivityMetadata } from '../auth/decorators/activity-metadata.decorator';
import { Permission } from '../shared/types/permission.enum';
import type { ActivityMetadata as ActivityMetadataType } from '../shared/types/activity-metadata.type';

@ApiTags('brands')
@Controller('brands')
@UseGuards(PermissionsGuard)
export class BrandsController {
  constructor(private readonly brandsService: BrandsService) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.BRAND_CREATE)
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'logo', maxCount: 1 },
      { name: 'ogImage', maxCount: 1 },
    ]),
  )
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Create a new brand with optional images' })
  @ApiBody({
    description: 'Brand creation with optional logo and ogImage upload',
    schema: {
      type: 'object',
      properties: {
        name: {
          type: 'string',
          example: 'Samsung',
          description: 'Brand name',
        },
        slug: {
          type: 'string',
          example: 'samsung',
          description: 'URL-friendly version of the brand name',
        },
        availableOnline: {
          type: 'boolean',
          example: true,
          description: 'Whether the brand is available online',
          default: false,
        },
        status: {
          type: 'string',
          enum: ['active', 'inactive'],
          description: 'Brand status',
          default: 'active',
        },
        seoTitle: {
          type: 'string',
          example: 'Samsung Electronics',
          description: 'SEO title for search engines',
        },
        seoDescription: {
          type: 'string',
          example: 'Samsung brand products and electronics',
          description: 'SEO description for search engines',
        },
        seoKeywords: {
          type: 'array',
          items: {
            type: 'string',
          },
          description: 'SEO keywords for search engines',
          example: ['samsung', 'electronics', 'technology'],
        },
        logo: {
          type: 'string',
          format: 'binary',
          description: 'Brand logo file',
        },
        ogImage: {
          type: 'string',
          format: 'binary',
          description: 'Open Graph image file for social media',
        },
      },
      required: ['name'],
    },
  })
  @ApiResponse({
    status: 201,
    description: 'The brand has been successfully created',
    type: BrandIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or file',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  create(
    @Request() req,
    @Body() createBrandDto: CreateBrandDto,
    @UploadedFiles()
    files: {
      logo?: Express.Multer.File[];
      ogImage?: Express.Multer.File[];
    } = {},
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BrandIdResponseDto> {
    // Extract logo and ogImage from the files object
    const logo = files?.logo?.[0];
    const ogImage = files?.ogImage?.[0];

    return this.brandsService.createAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      createBrandDto,
      logo,
      ogImage,
      metadata,
    );
  }

  @Post('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.BRAND_CREATE)
  @UseInterceptors(FilesInterceptor('images'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Bulk create brands with optional images' })
  @ApiBody({
    description:
      'Bulk brand creation with optional logo uploads. Logos can be mapped to brands using field names like "logo_0", "logo_1", etc., or by using the brands array with logoIndex property.',
    schema: {
      type: 'object',
      properties: {
        brands: {
          type: 'string',
          description:
            'JSON string containing array of brand objects. Each brand can optionally include a "logoIndex" property to specify which logo file to use.',
          example:
            '[{"name":"Samsung","availableOnline":true,"logoIndex":0},{"name":"Apple","availableOnline":false},{"name":"Sony","availableOnline":true,"logoIndex":1}]',
        },
        images: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
          description:
            'Array of brand logo files (optional). Logos are mapped to brands using the logoIndex property in the brands array.',
        },
      },
      required: ['brands'],
    },
  })
  @ApiResponse({
    status: 201,
    description: 'The brands have been successfully created',
    type: BulkBrandIdsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data, duplicate names, or files',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Brand names already exist',
  })
  bulkCreate(
    @Request() req,
    @Body() bulkCreateBrandDto: BulkCreateBrandDto,
    @UploadedFiles() images: Express.Multer.File[] = [],
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkBrandIdsResponseDto> {
    return this.brandsService.bulkCreateAndReturnIds(
      req.user.id,
      req.user.activeBusinessId,
      bulkCreateBrandDto.brands,
      images,
      metadata,
    );
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.BRAND_READ)
  @ApiOperation({ summary: 'Get all brands with pagination and filtering' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page (default: 10)',
    example: 10,
  })
  @ApiQuery({
    name: 'from',
    required: false,
    type: String,
    description: 'Start date for filtering (ISO 8601 format)',
    example: '2023-01-01T00:00:00Z',
  })
  @ApiQuery({
    name: 'to',
    required: false,
    type: String,
    description: 'End date for filtering (ISO 8601 format)',
    example: '2023-12-31T23:59:59Z',
  })
  @ApiQuery({
    name: 'name',
    required: false,
    type: String,
    description: 'Filter by brand name (partial match)',
    example: 'Samsung',
  })
  @ApiQuery({
    name: 'slug',
    required: false,
    type: String,
    description: 'Filter by brand slug (partial match)',
    example: 'samsung',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: ['active', 'inactive'],
    description: 'Filter by brand status',
    example: 'active',
  })
  @ApiQuery({
    name: 'availableOnline',
    required: false,
    type: String,
    description: 'Filter by online availability (true/false)',
    example: 'true',
  })
  @ApiQuery({
    name: 'filters',
    description:
      'Advanced filters as JSON string with operator support. Supported operators: iLike (contains), notILike (does not contain), eq (is), ne (is not), isEmpty (is empty), isNotEmpty (is not empty)',
    required: false,
    type: String,
    example:
      '[{"id":"name","value":"Samsung","operator":"iLike","type":"text","rowId":"1"},{"id":"status","value":"active","operator":"eq","type":"select","rowId":"2"}]',
  })
  @ApiQuery({
    name: 'joinOperator',
    description: 'Join operator for advanced filters',
    required: false,
    type: String,
    enum: ['and', 'or'],
    example: 'and',
  })
  @ApiQuery({
    name: 'sort',
    description:
      'Sort configuration as JSON string. Supported fields: name, position, createdAt, updatedAt',
    required: false,
    type: String,
    example: '[{"id":"position","desc":false}]',
  })
  @ApiResponse({
    status: 200,
    description: 'List of brands retrieved successfully',
    type: PaginatedBrandsResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Request() req,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('name') name?: string,
    @Query('slug') slug?: string,
    @Query('status') status?: string,
    @Query('availableOnline') availableOnline?: string,
    @Query('filters') filters?: string,
    @Query('joinOperator') joinOperator?: 'and' | 'or',
    @Query('sort') sort?: string,
  ): Promise<PaginatedBrandsResponseDto> {
    return this.brandsService.findAllOptimized(
      req.user.id,
      req.user.activeBusinessId,
      page ? +page : 1,
      limit ? +limit : 10,
      from,
      to,
      name,
      slug,
      status,
      availableOnline,
      filters,
      joinOperator,
      sort,
    );
  }

  @Get('check-name-availability')
  @ApiBearerAuth()
  @RequirePermissions(Permission.BRAND_READ)
  @ApiOperation({ summary: 'Check if a brand name is available' })
  @ApiQuery({
    name: 'name',
    required: true,
    type: String,
    description: 'Brand name to check',
    example: 'Samsung',
  })
  @ApiResponse({
    status: 200,
    description: 'Name availability checked successfully',
    type: BrandNameAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkNameAvailability(
    @Request() req,
    @Query('name') name: string,
  ): Promise<{ available: boolean }> {
    return this.brandsService.checkNameAvailability(
      req.user.id,
      req.user.activeBusinessId,
      name,
    );
  }

  @Get('check-slug-availability')
  @ApiBearerAuth()
  @RequirePermissions(Permission.BRAND_READ)
  @ApiOperation({ summary: 'Check if a brand slug is available' })
  @ApiQuery({
    name: 'slug',
    required: true,
    type: String,
    description: 'Brand slug to check',
    example: 'samsung',
  })
  @ApiResponse({
    status: 200,
    description: 'Slug availability checked successfully',
    type: BrandNameAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkSlugAvailability(
    @Request() req,
    @Query('slug') slug: string,
  ): Promise<{ available: boolean }> {
    return this.brandsService.checkSlugAvailability(
      req.user.id,
      req.user.activeBusinessId,
      slug,
    );
  }

  @Get('slim')
  @ApiBearerAuth()
  @RequirePermissions(Permission.BRAND_READ)
  @ApiOperation({
    summary: 'Get all brands in slim format (id, name, logo, position only)',
  })
  @ApiResponse({
    status: 200,
    description: 'Slim brands list retrieved successfully',
    type: [BrandSlimDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllSlim(@Request() req): Promise<BrandSlimDto[]> {
    return this.brandsService.findAllSlim(
      req.user.id,
      req.user.activeBusinessId,
    );
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.BRAND_READ)
  @ApiOperation({ summary: 'Get a specific brand by ID' })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'Brand ID',
    example: 'uuid-here',
  })
  @ApiResponse({
    status: 200,
    description: 'Brand retrieved successfully',
    type: BrandDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Brand not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findOne(@Request() req, @Param('id') id: string): Promise<BrandDto> {
    return this.brandsService.findOne(req.user.id, id);
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.BRAND_UPDATE)
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'logo', maxCount: 1 },
      { name: 'ogImage', maxCount: 1 },
    ]),
  )
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Update a brand with optional images' })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'Brand ID',
    example: 'uuid-here',
  })
  @ApiBody({
    description: 'Brand update with optional logo and ogImage upload',
    schema: {
      type: 'object',
      properties: {
        name: {
          type: 'string',
          example: 'Samsung Electronics',
          description: 'Brand name',
        },
        slug: {
          type: 'string',
          example: 'samsung-electronics',
          description: 'URL-friendly version of the brand name',
        },
        availableOnline: {
          type: 'boolean',
          example: true,
          description: 'Whether the brand is available online',
        },
        status: {
          type: 'string',
          enum: ['active', 'inactive'],
          description: 'Brand status',
        },
        seoTitle: {
          type: 'string',
          example: 'Samsung Electronics - Premium Technology',
          description: 'SEO title for search engines',
        },
        seoDescription: {
          type: 'string',
          example:
            'Discover Samsung brand products and cutting-edge electronics',
          description: 'SEO description for search engines',
        },
        seoKeywords: {
          type: 'array',
          items: {
            type: 'string',
          },
          description: 'SEO keywords for search engines',
          example: ['samsung', 'electronics', 'technology', 'smartphones'],
        },
        logo: {
          type: 'string',
          format: 'binary',
          description: 'Brand logo file',
        },
        ogImage: {
          type: 'string',
          format: 'binary',
          description: 'Open Graph image file for social media',
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'The brand has been successfully updated',
    type: BrandIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or file',
  })
  @ApiResponse({
    status: 404,
    description: 'Brand not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  update(
    @Request() req,
    @Param('id') id: string,
    @Body() updateBrandDto: UpdateBrandDto,
    @UploadedFiles()
    files: {
      logo?: Express.Multer.File[];
      ogImage?: Express.Multer.File[];
    } = {},
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BrandIdResponseDto> {
    // Extract logo and ogImage from the files object
    const logo = files?.logo?.[0];
    const ogImage = files?.ogImage?.[0];

    return this.brandsService.updateAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateBrandDto,
      logo,
      ogImage,
      metadata,
    );
  }

  @Delete('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.BRAND_DELETE)
  @ApiOperation({ summary: 'Bulk delete brands' })
  @ApiBody({
    description: 'Bulk brand deletion',
    type: BulkDeleteBrandDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Brands have been successfully deleted',
    type: BulkDeleteBrandResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - No brand IDs provided',
  })
  @ApiResponse({
    status: 404,
    description: 'No brands found to delete',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async bulkDelete(
    @Request() req,
    @Body() bulkDeleteBrandDto: BulkDeleteBrandDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkDeleteBrandResponseDto> {
    return this.brandsService.bulkDelete(
      req.user.id,
      req.user.activeBusinessId,
      bulkDeleteBrandDto.brandIds,
      metadata,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.BRAND_DELETE)
  @ApiOperation({ summary: 'Delete a brand' })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'Brand ID',
    example: 'uuid-here',
  })
  @ApiResponse({
    status: 200,
    description: 'The brand has been successfully deleted',
    type: DeleteBrandResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Brand not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  remove(
    @Request() req,
    @Param('id') id: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<DeleteBrandResponseDto> {
    return this.brandsService.remove(
      req.user.id,
      req.user.activeBusinessId,
      id,
      metadata,
    );
  }

  @Patch('positions/update')
  @ApiBearerAuth()
  @RequirePermissions(Permission.BRAND_UPDATE)
  @ApiOperation({ summary: 'Update brand positions' })
  @ApiBody({
    description: 'Bulk brand position updates',
    type: UpdateBrandPositionsDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Brand positions updated successfully',
    type: UpdateBrandPositionsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - No position updates provided',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async updatePositions(
    @Request() req,
    @Body() updateBrandPositionsDto: UpdateBrandPositionsDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<UpdateBrandPositionsResponseDto> {
    return this.brandsService.updateBrandPositions(
      req.user.id,
      req.user.activeBusinessId,
      updateBrandPositionsDto.updates,
      metadata,
    );
  }

  @Patch('bulk-status')
  @ApiBearerAuth()
  @RequirePermissions(Permission.BRAND_UPDATE)
  @ApiOperation({ summary: 'Bulk update brand status' })
  @ApiBody({
    description: 'Array of brand IDs and status to update',
    type: BulkUpdateBrandStatusDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Brand status has been successfully updated',
    type: BulkUpdateBrandStatusResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or brands not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async bulkUpdateStatus(
    @Request() req,
    @Body() bulkUpdateStatusDto: BulkUpdateBrandStatusDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkUpdateBrandStatusResponseDto> {
    const result = await this.brandsService.bulkUpdateBrandStatus(
      req.user.id,
      req.user.activeBusinessId,
      bulkUpdateStatusDto.brandIds,
      bulkUpdateStatusDto.status,
      metadata,
    );

    return {
      updated: result.updated,
      message: `Successfully updated status for ${result.updated} brands`,
      updatedIds: result.updatedIds,
      failed: result.failed.length > 0 ? result.failed : undefined,
    };
  }
}
