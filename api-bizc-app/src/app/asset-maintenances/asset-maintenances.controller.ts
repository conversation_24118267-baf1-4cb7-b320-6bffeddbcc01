import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  Query,
  UseGuards,
  UseInterceptors,
  UploadedFiles,
} from '@nestjs/common';
import { FilesInterceptor } from '@nestjs/platform-express';
import { AssetMaintenancesService } from './asset-maintenances.service';
import { CreateAssetMaintenanceDto } from './dto/create-asset-maintenance.dto';
import { UpdateAssetMaintenanceDto } from './dto/update-asset-maintenance.dto';
import { AssetMaintenanceDto } from './dto/asset-maintenance.dto';
import { AssetMaintenanceSlimDto } from './dto/asset-maintenance-slim.dto';
import { AssetMaintenanceIdResponseDto } from './dto/asset-maintenance-id-response.dto';
import { BulkAssetMaintenanceIdsResponseDto } from './dto/bulk-asset-maintenance-ids-response.dto';
import { BulkCreateAssetMaintenanceDto } from './dto/bulk-create-asset-maintenance.dto';
import { BulkDeleteAssetMaintenanceDto } from './dto/bulk-delete-asset-maintenance.dto';
import { BulkDeleteAssetMaintenanceResponseDto } from './dto/bulk-delete-asset-maintenance-response.dto';
import { DeleteAssetMaintenanceResponseDto } from './dto/delete-asset-maintenance-response.dto';
import { PaginatedAssetMaintenancesResponseDto } from './dto/paginated-asset-maintenances-response.dto';
import {
  BulkUpdateAssetMaintenanceStatusDto,
  BulkUpdateAssetMaintenanceStatusResponseDto,
} from './dto/bulk-update-asset-maintenance-status.dto';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBody,
  ApiBearerAuth,
  ApiConsumes,
} from '@nestjs/swagger';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { ActivityMetadata } from '../auth/decorators/activity-metadata.decorator';
import { Permission } from '../shared/types/permission.enum';
import type { ActivityMetadata as ActivityMetadataType } from '../shared/types/activity-metadata.type';

@ApiTags('asset-maintenances')
@Controller('asset-maintenances')
@UseGuards(PermissionsGuard)
export class AssetMaintenancesController {
  constructor(
    private readonly assetMaintenancesService: AssetMaintenancesService,
  ) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_MAINTENANCE_CREATE)
  @UseInterceptors(FilesInterceptor('attachments'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Create a new asset maintenance with optional attachments',
  })
  @ApiBody({
    description: 'Asset maintenance data with optional attachment files',
    type: CreateAssetMaintenanceDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Asset maintenance created successfully',
    type: AssetMaintenanceIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description:
      'Conflict - Asset maintenance title already exists for this asset',
  })
  create(
    @Request() req,
    @Body() createAssetMaintenanceDto: CreateAssetMaintenanceDto,
    @UploadedFiles() attachments?: Express.Multer.File[],
    @ActivityMetadata() metadata?: ActivityMetadataType,
  ): Promise<AssetMaintenanceIdResponseDto> {
    return this.assetMaintenancesService.create(
      req.user.id,
      req.user.activeBusinessId,
      createAssetMaintenanceDto,
      attachments,
      metadata,
    );
  }

  @Post('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_MAINTENANCE_CREATE)
  @UseInterceptors(FilesInterceptor('attachments'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Bulk create asset maintenances with optional attachments',
  })
  @ApiBody({
    description: 'Bulk asset maintenance data with optional attachment files',
    type: BulkCreateAssetMaintenanceDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Asset maintenances created successfully',
    type: BulkAssetMaintenanceIdsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Asset maintenance titles already exist',
  })
  bulkCreate(
    @Request() req,
    @Body() bulkCreateAssetMaintenanceDto: BulkCreateAssetMaintenanceDto,
    @UploadedFiles() attachments?: Express.Multer.File[],
    @ActivityMetadata() metadata?: ActivityMetadataType,
  ): Promise<BulkAssetMaintenanceIdsResponseDto> {
    return this.assetMaintenancesService.bulkCreateAndReturnIds(
      req.user.id,
      req.user.activeBusinessId,
      bulkCreateAssetMaintenanceDto.assetMaintenances,
      attachments,
      metadata,
    );
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_MAINTENANCE_READ)
  @ApiOperation({
    summary: 'Get all asset maintenances for the active business',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'from',
    description: 'Start date for filtering (YYYY-MM-DD)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'to',
    description: 'End date for filtering (YYYY-MM-DD)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'title',
    description: 'Filter by title (partial match)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'workOrderNumber',
    description: 'Filter by work order number (partial match)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'status',
    description: 'Filter by status',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'priority',
    description: 'Filter by priority',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'assetId',
    description: 'Filter by asset ID',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'filters',
    description: 'Advanced filters as JSON string',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'joinOperator',
    description: 'Join operator for multiple filters (and/or)',
    required: false,
    enum: ['and', 'or'],
  })
  @ApiQuery({
    name: 'sort',
    description: 'Sort field and direction (field:asc/desc)',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Asset maintenances returned successfully',
    type: PaginatedAssetMaintenancesResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Request() req,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('title') title?: string,
    @Query('workOrderNumber') workOrderNumber?: string,
    @Query('status') status?: string,
    @Query('priority') priority?: string,
    @Query('assetId') assetId?: string,
    @Query('filters') filters?: string,
    @Query('joinOperator') joinOperator?: 'and' | 'or',
    @Query('sort') sort?: string,
  ): Promise<PaginatedAssetMaintenancesResponseDto> {
    return this.assetMaintenancesService.findAllOptimized(
      req.user.id,
      req.user.activeBusinessId,
      page ? parseInt(page.toString()) : undefined,
      limit ? parseInt(limit.toString()) : undefined,
      from,
      to,
      title,
      workOrderNumber,
      status,
      priority,
      assetId,
      filters,
      joinOperator,
      sort,
    );
  }

  @Get('check-title-availability')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_MAINTENANCE_READ)
  @ApiOperation({
    summary:
      'Check if asset maintenance title is available for a specific asset',
  })
  @ApiQuery({
    name: 'title',
    description: 'Asset maintenance title to check',
    required: true,
    type: String,
  })
  @ApiQuery({
    name: 'assetId',
    description: 'Asset ID to check title availability for',
    required: true,
    type: String,
  })
  @ApiQuery({
    name: 'excludeId',
    description: 'Asset maintenance ID to exclude from check (for updates)',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Title availability checked successfully',
    schema: {
      type: 'object',
      properties: {
        available: { type: 'boolean' },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkTitleAvailability(
    @Request() req,
    @Query('title') title: string,
    @Query('assetId') assetId: string,
    @Query('excludeId') excludeId?: string,
  ): Promise<{ available: boolean }> {
    return this.assetMaintenancesService.checkTitleAvailability(
      req.user.activeBusinessId,
      title,
      assetId,
      excludeId,
    );
  }

  @Get('slim')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_MAINTENANCE_READ)
  @ApiOperation({ summary: 'Get all asset maintenances in slim format' })
  @ApiResponse({
    status: 200,
    description: 'All asset maintenances returned successfully',
    type: [AssetMaintenanceSlimDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllSlim(@Request() req): Promise<AssetMaintenanceSlimDto[]> {
    return this.assetMaintenancesService.findAllSlim(
      req.user.id,
      req.user.activeBusinessId,
    );
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_MAINTENANCE_READ)
  @ApiOperation({ summary: 'Get an asset maintenance by ID' })
  @ApiParam({
    name: 'id',
    description: 'Asset maintenance ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the asset maintenance with all details',
    type: AssetMaintenanceDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Asset maintenance not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to this asset maintenance',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findOne(
    @Request() req,
    @Param('id') id: string,
  ): Promise<AssetMaintenanceDto> {
    return this.assetMaintenancesService.findOne(
      req.user.id,
      req.user.activeBusinessId,
      id,
    );
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_MAINTENANCE_UPDATE)
  @UseInterceptors(FilesInterceptor('attachments'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Update an asset maintenance with optional attachments',
  })
  @ApiParam({
    name: 'id',
    description: 'Asset maintenance ID',
  })
  @ApiBody({
    description: 'Asset maintenance update data with optional attachment files',
    type: UpdateAssetMaintenanceDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Asset maintenance updated successfully',
    type: AssetMaintenanceDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 404,
    description: 'Asset maintenance not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to this asset maintenance',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description:
      'Conflict - Asset maintenance title already exists for this asset',
  })
  update(
    @Request() req,
    @Param('id') id: string,
    @Body() updateAssetMaintenanceDto: UpdateAssetMaintenanceDto,
    @UploadedFiles() attachments?: Express.Multer.File[],
    @ActivityMetadata() metadata?: ActivityMetadataType,
  ): Promise<AssetMaintenanceDto> {
    return this.assetMaintenancesService.update(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateAssetMaintenanceDto,
      attachments,
      metadata,
    );
  }

  @Delete('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_MAINTENANCE_DELETE)
  @ApiOperation({ summary: 'Bulk delete asset maintenances' })
  @ApiBody({
    description: 'Array of asset maintenance IDs to delete',
    type: BulkDeleteAssetMaintenanceDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Asset maintenances deleted successfully',
    type: BulkDeleteAssetMaintenanceResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  bulkDelete(
    @Request() req,
    @Body() bulkDeleteAssetMaintenanceDto: BulkDeleteAssetMaintenanceDto,
    @ActivityMetadata() metadata?: ActivityMetadataType,
  ): Promise<BulkDeleteAssetMaintenanceResponseDto> {
    return this.assetMaintenancesService.bulkDelete(
      req.user.id,
      req.user.activeBusinessId,
      bulkDeleteAssetMaintenanceDto.assetMaintenanceIds,
      metadata,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_MAINTENANCE_DELETE)
  @ApiOperation({ summary: 'Delete an asset maintenance' })
  @ApiParam({
    name: 'id',
    description: 'Asset maintenance ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Asset maintenance deleted successfully',
    type: DeleteAssetMaintenanceResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Asset maintenance not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to this asset maintenance',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  remove(
    @Request() req,
    @Param('id') id: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<DeleteAssetMaintenanceResponseDto> {
    return this.assetMaintenancesService.remove(
      req.user.id,
      req.user.activeBusinessId,
      id,
      metadata,
    );
  }

  @Patch('bulk-status')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_MAINTENANCE_UPDATE)
  @ApiOperation({ summary: 'Bulk update asset maintenance status' })
  @ApiBody({
    description: 'Array of asset maintenance IDs and status to update',
    type: BulkUpdateAssetMaintenanceStatusDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Asset maintenance status has been successfully updated',
    type: BulkUpdateAssetMaintenanceStatusResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or asset maintenances not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async bulkUpdateStatus(
    @Request() req,
    @Body() bulkUpdateStatusDto: BulkUpdateAssetMaintenanceStatusDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkUpdateAssetMaintenanceStatusResponseDto> {
    const result =
      await this.assetMaintenancesService.bulkUpdateAssetMaintenanceStatus(
        req.user.id,
        req.user.activeBusinessId,
        bulkUpdateStatusDto.assetMaintenanceIds,
        bulkUpdateStatusDto.status,
        metadata,
      );

    return {
      updated: result.updated,
      message: `Successfully updated status for ${result.updated} asset maintenances`,
      updatedIds: result.updatedIds,
      failed: result.failed.length > 0 ? result.failed : undefined,
    };
  }
}
