import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateAssetMaintenanceDto } from './dto/create-asset-maintenance.dto';
import { UpdateAssetMaintenanceDto } from './dto/update-asset-maintenance.dto';
import { AssetMaintenanceDto } from './dto/asset-maintenance.dto';
import { AssetMaintenanceSlimDto } from './dto/asset-maintenance-slim.dto';
import { AssetMaintenanceListDto } from './dto/asset-maintenance-list.dto';
import { assetMaintenances } from '../drizzle/schema/asset-maintenances.schema';
import { assets } from '../drizzle/schema/assets.schema';
import { suppliers } from '../drizzle/schema/suppliers.schema';
import { staffMembers } from '../drizzle/schema/staff.schema';
import { accounts } from '../drizzle/schema/accounts.schema';
import { paymentMethods } from '../drizzle/schema/payment-methods.schema';
import { taxes } from '../drizzle/schema/taxes.schema';
import {
  and,
  eq,
  ilike,
  gte,
  lte,
  desc,
  asc,
  or,
  sql,
  count,
} from 'drizzle-orm';
import { ActivityLogService } from '../activity-log/activity-log.service';
import {
  EntityType,
  ActivityType,
  ExecutionStrategy,
  ActivitySource,
} from '../shared/types/activity.enum';
import { MediaService } from '../media/media.service';
import { GcsUploadService } from '../gcs-upload/gcs-upload.service';
import { UsersService } from '../users/users.service';
import { MaintenanceStatus, MaintenancePriority } from '../shared/types';
import { AmountType } from '../drizzle/schema/expenses.schema';
import { ActivityMetadata } from '../shared/types/activity-metadata.type';

@Injectable()
export class AssetMaintenancesService {
  constructor(
    @Inject(DRIZZLE) private readonly db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly mediaService: MediaService,
    private readonly gcsUploadService: GcsUploadService,
    private readonly usersService: UsersService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createAssetMaintenanceDto: CreateAssetMaintenanceDto,
    attachmentFiles?: Express.Multer.File[],
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if an asset maintenance with the same title already exists for this business and asset
      const existingAssetMaintenance = await this.db
        .select()
        .from(assetMaintenances)
        .where(
          and(
            eq(assetMaintenances.businessId, businessId),
            eq(assetMaintenances.assetId, createAssetMaintenanceDto.assetId),
            ilike(assetMaintenances.title, createAssetMaintenanceDto.title),
            eq(assetMaintenances.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingAssetMaintenance) {
        throw new ConflictException(
          `Asset maintenance with title "${createAssetMaintenanceDto.title}" already exists for this asset`,
        );
      }

      // Verify that the asset exists and belongs to the business
      const asset = await this.db
        .select()
        .from(assets)
        .where(
          and(
            eq(assets.id, createAssetMaintenanceDto.assetId),
            eq(assets.businessId, businessId),
            eq(assets.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!asset) {
        throw new NotFoundException(
          'Asset not found or does not belong to this business',
        );
      }

      // Create the asset maintenance
      const [newAssetMaintenance] = await this.db
        .insert(assetMaintenances)
        .values({
          businessId,
          assetId: createAssetMaintenanceDto.assetId,
          workOrderNumber: createAssetMaintenanceDto.workOrderNumber,
          title: createAssetMaintenanceDto.title,
          description: createAssetMaintenanceDto.description,
          instructions: createAssetMaintenanceDto.instructions,
          status: createAssetMaintenanceDto.status || MaintenanceStatus.PENDING,
          priority:
            createAssetMaintenanceDto.priority || MaintenancePriority.NORMAL,
          supplierId: createAssetMaintenanceDto.supplierId,
          assignedTo: createAssetMaintenanceDto.assignedTo,
          qualityCheckBy: createAssetMaintenanceDto.qualityCheckBy,
          targetCompletionDate: createAssetMaintenanceDto.targetCompletionDate
            ? new Date(createAssetMaintenanceDto.targetCompletionDate)
            : null,
          actualStartDate: createAssetMaintenanceDto.actualStartDate
            ? new Date(createAssetMaintenanceDto.actualStartDate)
            : null,
          completionNotes: createAssetMaintenanceDto.completionNotes,
          expenseAccountId: createAssetMaintenanceDto.expenseAccountId,
          paymentAccountId: createAssetMaintenanceDto.paymentAccountId,
          paymentDate: createAssetMaintenanceDto.paymentDate,
          paymentMethodId: createAssetMaintenanceDto.paymentMethodId,
          paymentReferenceNumber:
            createAssetMaintenanceDto.paymentReferenceNumber,
          amountType:
            createAssetMaintenanceDto.amountType || AmountType.EXCLUSIVE_OF_TAX,
          subtotal: createAssetMaintenanceDto.subtotal || '0.00',
          total: createAssetMaintenanceDto.total || '0.00',
          taxId: createAssetMaintenanceDto.taxId,
          createdBy: userId,
          updatedBy: userId,
        })
        .returning();

      // Handle file attachments if provided
      if (attachmentFiles && attachmentFiles.length > 0) {
        const filesToUpload = createAssetMaintenanceDto.attachmentIndexes
          ? createAssetMaintenanceDto.attachmentIndexes
              .map((index) => attachmentFiles[index])
              .filter(Boolean)
          : attachmentFiles;

        if (filesToUpload.length > 0) {
          await this.mediaService.uploadMultipleMediaWithReference(
            filesToUpload,
            'asset-maintenances',
            businessId,
            userId,
            newAssetMaintenance.id,
          );
        }
      }

      // Log the asset maintenance creation activity
      await this.activityLogService.logCreate(
        newAssetMaintenance.id,
        EntityType.ASSET,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
        },
      );

      return { id: newAssetMaintenance.id };
    } catch (error) {
      if (
        error instanceof ConflictException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create asset maintenance: ${error.message}`,
      );
    }
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    assetMaintenancesData: CreateAssetMaintenanceDto[],
    attachmentFiles?: Express.Multer.File[],
    metadata?: ActivityMetadata,
  ): Promise<{ ids: string[]; count: number }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const createdIds: string[] = [];
    const errors: string[] = [];

    for (let i = 0; i < assetMaintenancesData.length; i++) {
      try {
        const assetMaintenanceData = assetMaintenancesData[i];

        // Handle attachment files for this specific asset maintenance
        let relevantFiles: Express.Multer.File[] | undefined;
        if (attachmentFiles && assetMaintenanceData.attachmentIndexes) {
          relevantFiles = assetMaintenanceData.attachmentIndexes
            .map((index) => attachmentFiles[index])
            .filter(Boolean);
        }

        const result = await this.create(
          userId,
          businessId,
          assetMaintenanceData,
          relevantFiles,
          metadata,
        );
        createdIds.push(result.id);
      } catch (error) {
        errors.push(`Asset maintenance ${i + 1}: ${error.message}`);
      }
    }

    if (errors.length > 0 && createdIds.length === 0) {
      throw new BadRequestException(
        `Failed to create any asset maintenances: ${errors.join(', ')}`,
      );
    }

    if (errors.length > 0) {
      console.warn('Some asset maintenances failed to create:', errors);
    }

    // Log bulk create operation if any were created
    if (createdIds.length > 0) {
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_CREATE,
        EntityType.ASSET,
        createdIds,
        {
          createdCount: createdIds.length,
          totalRequested: assetMaintenancesData.length,
        },
        userId,
        businessId,
        {
          filterCriteria: { count: assetMaintenancesData.length },
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
        },
      );
    }

    return { ids: createdIds, count: createdIds.length };
  }

  async findAll(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
  ): Promise<{
    data: AssetMaintenanceDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(assetMaintenances.isDeleted, false),
      eq(assetMaintenances.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(assetMaintenances.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        // Add one day to include the entire 'to' date
        toDate.setDate(toDate.getDate() + 1);
        whereConditions.push(lte(assetMaintenances.createdAt, toDate));
      }
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: count() })
      .from(assetMaintenances)
      .where(and(...whereConditions));

    const total = totalResult[0]?.count || 0;
    const totalPages = Math.ceil(total / limit);

    // Get paginated data
    const assetMaintenanceRecords = await this.db
      .select()
      .from(assetMaintenances)
      .where(and(...whereConditions))
      .orderBy(desc(assetMaintenances.createdAt))
      .limit(limit)
      .offset(offset);

    // Map to DTOs
    const data = await Promise.all(
      assetMaintenanceRecords.map((assetMaintenance) =>
        this.mapToAssetMaintenanceDto(assetMaintenance),
      ),
    );

    return {
      data,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findAllOptimized(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    title?: string,
    workOrderNumber?: string,
    status?: string,
    priority?: string,
    assetId?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: AssetMaintenanceListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      eq(assetMaintenances.isDeleted, false),
      eq(assetMaintenances.businessId, businessId),
    ];

    // Add date range filtering
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(assetMaintenances.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        toDate.setDate(toDate.getDate() + 1);
        whereConditions.push(lte(assetMaintenances.createdAt, toDate));
      }
    }

    // Build search conditions
    const searchConditions = [];

    if (title) {
      searchConditions.push(ilike(assetMaintenances.title, `%${title}%`));
    }

    if (workOrderNumber) {
      searchConditions.push(
        ilike(assetMaintenances.workOrderNumber, `%${workOrderNumber}%`),
      );
    }

    if (status) {
      searchConditions.push(
        eq(assetMaintenances.status, status as MaintenanceStatus),
      );
    }

    if (priority) {
      searchConditions.push(
        eq(assetMaintenances.priority, priority as MaintenancePriority),
      );
    }

    if (assetId) {
      searchConditions.push(eq(assetMaintenances.assetId, assetId));
    }

    // Handle advanced filters
    if (filters) {
      try {
        const parsedFilters = JSON.parse(filters);
        for (const filter of parsedFilters) {
          const { field, operator, value } = filter;

          switch (field) {
            case 'title':
              if (operator === 'contains') {
                searchConditions.push(
                  ilike(assetMaintenances.title, `%${value}%`),
                );
              } else if (operator === 'equals') {
                searchConditions.push(eq(assetMaintenances.title, value));
              }
              break;
            case 'status':
              searchConditions.push(eq(assetMaintenances.status, value));
              break;
            case 'priority':
              searchConditions.push(eq(assetMaintenances.priority, value));
              break;
            case 'total':
              if (operator === 'gte') {
                searchConditions.push(gte(assetMaintenances.total, value));
              } else if (operator === 'lte') {
                searchConditions.push(lte(assetMaintenances.total, value));
              }
              break;
          }
        }
      } catch (error) {
        console.warn('Failed to parse filters:', error);
      }
    }

    // Combine conditions
    let finalConditions = and(...whereConditions);
    if (searchConditions.length > 0) {
      const searchQuery =
        joinOperator === 'or'
          ? or(...searchConditions)
          : and(...searchConditions);
      finalConditions = and(finalConditions, searchQuery);
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: count() })
      .from(assetMaintenances)
      .leftJoin(assets, eq(assetMaintenances.assetId, assets.id))
      .leftJoin(suppliers, eq(assetMaintenances.supplierId, suppliers.id))
      .leftJoin(staffMembers, eq(assetMaintenances.assignedTo, staffMembers.id))
      .leftJoin(
        paymentMethods,
        eq(assetMaintenances.paymentMethodId, paymentMethods.id),
      )
      .leftJoin(taxes, eq(assetMaintenances.taxId, taxes.id))
      .where(finalConditions);

    const total = totalResult[0]?.count || 0;
    const totalPages = Math.ceil(total / limit);

    // Build order by clause
    let orderBy;
    if (sort) {
      const [field, direction] = sort.split(':');
      const isDesc = direction?.toLowerCase() === 'desc';

      switch (field) {
        case 'title':
          orderBy = isDesc
            ? desc(assetMaintenances.title)
            : asc(assetMaintenances.title);
          break;
        case 'status':
          orderBy = isDesc
            ? desc(assetMaintenances.status)
            : asc(assetMaintenances.status);
          break;
        case 'priority':
          orderBy = isDesc
            ? desc(assetMaintenances.priority)
            : asc(assetMaintenances.priority);
          break;
        case 'total':
          orderBy = isDesc
            ? desc(assetMaintenances.total)
            : asc(assetMaintenances.total);
          break;
        case 'createdAt':
          orderBy = isDesc
            ? desc(assetMaintenances.createdAt)
            : asc(assetMaintenances.createdAt);
          break;
        default:
          orderBy = desc(assetMaintenances.createdAt);
      }
    } else {
      orderBy = desc(assetMaintenances.createdAt);
    }

    // Get paginated data with joins
    const assetMaintenanceRecords = await this.db
      .select({
        assetMaintenance: assetMaintenances,
        asset: assets,
        supplier: suppliers,
        assignedStaff: staffMembers,
        paymentMethod: paymentMethods,
        tax: taxes,
      })
      .from(assetMaintenances)
      .leftJoin(assets, eq(assetMaintenances.assetId, assets.id))
      .leftJoin(suppliers, eq(assetMaintenances.supplierId, suppliers.id))
      .leftJoin(staffMembers, eq(assetMaintenances.assignedTo, staffMembers.id))
      .leftJoin(
        paymentMethods,
        eq(assetMaintenances.paymentMethodId, paymentMethods.id),
      )
      .leftJoin(taxes, eq(assetMaintenances.taxId, taxes.id))
      .where(finalConditions)
      .orderBy(orderBy)
      .limit(limit)
      .offset(offset);

    // Map to list DTOs
    const data = await Promise.all(
      assetMaintenanceRecords.map((record) =>
        this.mapToAssetMaintenanceListDto(record),
      ),
    );

    return {
      data,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findOne(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<AssetMaintenanceDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const assetMaintenance = await this.db
      .select()
      .from(assetMaintenances)
      .where(
        and(
          eq(assetMaintenances.id, id),
          eq(assetMaintenances.businessId, businessId),
          eq(assetMaintenances.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!assetMaintenance) {
      throw new NotFoundException('Asset maintenance not found');
    }

    return this.mapToAssetMaintenanceDto(assetMaintenance);
  }

  async findAllSlim(
    userId: string,
    businessId: string | null,
  ): Promise<AssetMaintenanceSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const assetMaintenanceRecords = await this.db
      .select({
        assetMaintenance: assetMaintenances,
        asset: assets,
      })
      .from(assetMaintenances)
      .leftJoin(assets, eq(assetMaintenances.assetId, assets.id))
      .where(
        and(
          eq(assetMaintenances.businessId, businessId),
          eq(assetMaintenances.isDeleted, false),
        ),
      )
      .orderBy(desc(assetMaintenances.createdAt));

    return assetMaintenanceRecords.map((record) => ({
      id: record.assetMaintenance.id,
      assetId: record.assetMaintenance.assetId,
      workOrderNumber: record.assetMaintenance.workOrderNumber,
      title: record.assetMaintenance.title,
      status: record.assetMaintenance.status,
      priority: record.assetMaintenance.priority,
      total: record.assetMaintenance.total,
      assetName: record.asset?.name,
      assetCode: record.asset?.assetCode,
      createdAt: record.assetMaintenance.createdAt.toISOString(),
    }));
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateAssetMaintenanceDto: UpdateAssetMaintenanceDto,
    attachmentFiles?: Express.Multer.File[],
    metadata?: ActivityMetadata,
  ): Promise<AssetMaintenanceDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if asset maintenance exists
    const existingAssetMaintenance = await this.db
      .select()
      .from(assetMaintenances)
      .where(
        and(
          eq(assetMaintenances.id, id),
          eq(assetMaintenances.businessId, businessId),
          eq(assetMaintenances.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingAssetMaintenance) {
      throw new NotFoundException('Asset maintenance not found');
    }

    // Check for title conflicts if title is being updated
    if (
      updateAssetMaintenanceDto.title &&
      updateAssetMaintenanceDto.title !== existingAssetMaintenance.title
    ) {
      const assetIdToCheck =
        updateAssetMaintenanceDto.assetId || existingAssetMaintenance.assetId;

      const conflictingAssetMaintenance = await this.db
        .select()
        .from(assetMaintenances)
        .where(
          and(
            eq(assetMaintenances.businessId, businessId),
            eq(assetMaintenances.assetId, assetIdToCheck),
            ilike(assetMaintenances.title, updateAssetMaintenanceDto.title),
            eq(assetMaintenances.isDeleted, false),
            sql`${assetMaintenances.id} != ${id}`,
          ),
        )
        .then((results) => results[0]);

      if (conflictingAssetMaintenance) {
        throw new ConflictException(
          `Asset maintenance with title "${updateAssetMaintenanceDto.title}" already exists for this asset`,
        );
      }
    }

    // Verify asset exists if assetId is being updated
    if (
      updateAssetMaintenanceDto.assetId &&
      updateAssetMaintenanceDto.assetId !== existingAssetMaintenance.assetId
    ) {
      const asset = await this.db
        .select()
        .from(assets)
        .where(
          and(
            eq(assets.id, updateAssetMaintenanceDto.assetId),
            eq(assets.businessId, businessId),
            eq(assets.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!asset) {
        throw new NotFoundException(
          'Asset not found or does not belong to this business',
        );
      }
    }

    // Prepare update data with proper date conversion
    const updateData = {
      ...updateAssetMaintenanceDto,
      updatedBy: userId,
      updatedAt: new Date(),
      // Convert date strings to Date objects
      targetCompletionDate: updateAssetMaintenanceDto.targetCompletionDate
        ? new Date(updateAssetMaintenanceDto.targetCompletionDate)
        : undefined,
      actualStartDate: updateAssetMaintenanceDto.actualStartDate
        ? new Date(updateAssetMaintenanceDto.actualStartDate)
        : undefined,
      // Set completedAt if status is being changed to completed
      completedAt:
        updateAssetMaintenanceDto.status === MaintenanceStatus.COMPLETED
          ? new Date()
          : updateAssetMaintenanceDto.status === MaintenanceStatus.PENDING ||
              updateAssetMaintenanceDto.status ===
                MaintenanceStatus.IN_PROGRESS ||
              updateAssetMaintenanceDto.status === MaintenanceStatus.CANCELLED
            ? null
            : existingAssetMaintenance.completedAt,
    };

    // Update the asset maintenance
    const [updatedAssetMaintenance] = await this.db
      .update(assetMaintenances)
      .set(updateData)
      .where(eq(assetMaintenances.id, id))
      .returning();

    // Handle file attachments if provided
    if (attachmentFiles && attachmentFiles.length > 0) {
      const filesToUpload = updateAssetMaintenanceDto.attachmentIndexes
        ? updateAssetMaintenanceDto.attachmentIndexes
            .map((index) => attachmentFiles[index])
            .filter(Boolean)
        : attachmentFiles;

      if (filesToUpload.length > 0) {
        // Update media for this asset maintenance (replaces existing files)
        await this.mediaService.updateMediaForReference(
          id,
          filesToUpload,
          'asset-maintenances',
          businessId,
          userId,
        );
      }
    }

    // Log the update activity
    await this.activityLogService.logUpdate(
      id,
      EntityType.ASSET,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
      },
    );

    return this.mapToAssetMaintenanceDto(updatedAssetMaintenance);
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string; message: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const assetMaintenance = await this.db
      .select()
      .from(assetMaintenances)
      .where(
        and(
          eq(assetMaintenances.id, id),
          eq(assetMaintenances.businessId, businessId),
          eq(assetMaintenances.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!assetMaintenance) {
      throw new NotFoundException('Asset maintenance not found');
    }

    // Soft delete the asset maintenance
    await this.db
      .update(assetMaintenances)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(assetMaintenances.id, id));

    // Delete associated media files
    try {
      await this.mediaService.deleteMediaByReferenceId(
        id,
        businessId,
        'asset-maintenances',
      );
    } catch (error) {
      console.warn('Failed to delete media files:', error.message);
    }

    // Log the deletion activity
    await this.activityLogService.logDelete(
      id,
      EntityType.ASSET,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
      },
    );

    return {
      id,
      message: 'Asset maintenance deleted successfully',
    };
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    assetMaintenanceIds: string[],
    metadata?: ActivityMetadata,
  ): Promise<{
    deletedIds: string[];
    count: number;
    failedIds: string[];
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const deletedIds: string[] = [];
    const failedIds: string[] = [];

    for (const id of assetMaintenanceIds) {
      try {
        await this.remove(userId, businessId, id, metadata);
        deletedIds.push(id);
      } catch (error) {
        failedIds.push(id);
        console.warn(
          `Failed to delete asset maintenance ${id}:`,
          error.message,
        );
      }
    }

    // Log bulk delete operation if any were deleted
    if (deletedIds.length > 0) {
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_DELETE,
        EntityType.ASSET,
        deletedIds,
        { isDeleted: true, updatedBy: userId },
        userId,
        businessId,
        {
          filterCriteria: { assetMaintenanceIds },
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
        },
      );
    }

    return {
      deletedIds,
      count: deletedIds.length,
      failedIds,
    };
  }

  async checkTitleAvailability(
    businessId: string | null,
    title: string,
    assetId: string,
    excludeId?: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found');
    }

    const whereConditions = [
      eq(assetMaintenances.businessId, businessId),
      eq(assetMaintenances.assetId, assetId),
      ilike(assetMaintenances.title, title),
      eq(assetMaintenances.isDeleted, false),
    ];

    if (excludeId) {
      whereConditions.push(sql`${assetMaintenances.id} != ${excludeId}`);
    }

    const existingAssetMaintenance = await this.db
      .select()
      .from(assetMaintenances)
      .where(and(...whereConditions))
      .then((results) => results[0]);

    return { available: !existingAssetMaintenance };
  }

  private async mapToAssetMaintenanceDto(
    assetMaintenance: typeof assetMaintenances.$inferSelect,
  ): Promise<AssetMaintenanceDto> {
    // Get user names for createdBy and updatedBy
    const createdByName = await this.usersService.getUserName(
      assetMaintenance.createdBy.toString(),
    );
    let updatedByName: string | undefined;
    if (assetMaintenance.updatedBy) {
      updatedByName = await this.usersService.getUserName(
        assetMaintenance.updatedBy.toString(),
      );
    }

    // Get related entity names
    const [
      asset,
      supplier,
      assignedStaff,
      qualityCheckStaff,
      expenseAccount,
      paymentAccount,
      paymentMethod,
      tax,
    ] = await Promise.all([
      assetMaintenance.assetId
        ? this.getAssetInfo(assetMaintenance.assetId)
        : null,
      assetMaintenance.supplierId
        ? this.getSupplierName(assetMaintenance.supplierId)
        : null,
      assetMaintenance.assignedTo
        ? this.getStaffMemberName(assetMaintenance.assignedTo)
        : null,
      assetMaintenance.qualityCheckBy
        ? this.getStaffMemberName(assetMaintenance.qualityCheckBy)
        : null,
      this.getAccountName(assetMaintenance.expenseAccountId),
      this.getAccountName(assetMaintenance.paymentAccountId),
      this.getPaymentMethodName(assetMaintenance.paymentMethodId),
      assetMaintenance.taxId ? this.getTaxName(assetMaintenance.taxId) : null,
    ]);

    // Get attachments
    const attachments = await this.getAttachments(
      assetMaintenance.id,
      assetMaintenance.businessId,
    );

    return {
      id: assetMaintenance.id,
      businessId: assetMaintenance.businessId,
      assetId: assetMaintenance.assetId,
      workOrderNumber: assetMaintenance.workOrderNumber,
      title: assetMaintenance.title,
      description: assetMaintenance.description,
      instructions: assetMaintenance.instructions,
      status: assetMaintenance.status,
      priority: assetMaintenance.priority,
      supplierId: assetMaintenance.supplierId,
      supplierName: supplier,
      assignedTo: assetMaintenance.assignedTo,
      assignedToName: assignedStaff,
      qualityCheckBy: assetMaintenance.qualityCheckBy,
      qualityCheckByName: qualityCheckStaff,
      targetCompletionDate:
        assetMaintenance.targetCompletionDate?.toISOString(),
      actualStartDate: assetMaintenance.actualStartDate?.toISOString(),
      completedAt: assetMaintenance.completedAt?.toISOString(),
      completionNotes: assetMaintenance.completionNotes,
      expenseAccountId: assetMaintenance.expenseAccountId,
      expenseAccountName: expenseAccount,
      paymentAccountId: assetMaintenance.paymentAccountId,
      paymentAccountName: paymentAccount,
      paymentDate: assetMaintenance.paymentDate,
      paymentMethodId: assetMaintenance.paymentMethodId,
      paymentMethodName: paymentMethod,
      paymentReferenceNumber: assetMaintenance.paymentReferenceNumber,
      amountType: assetMaintenance.amountType,
      subtotal: assetMaintenance.subtotal,
      total: assetMaintenance.total,
      taxId: assetMaintenance.taxId,
      taxName: tax,
      assetName: asset?.name,
      assetCode: asset?.assetCode,
      createdAt: assetMaintenance.createdAt.toISOString(),
      updatedAt: assetMaintenance.updatedAt.toISOString(),
      createdBy: assetMaintenance.createdBy,
      createdByName,
      updatedBy: assetMaintenance.updatedBy,
      updatedByName,
      attachments,
    };
  }

  private async mapToAssetMaintenanceListDto(
    record: any,
  ): Promise<AssetMaintenanceListDto> {
    const assetMaintenance = record.assetMaintenance;
    const asset = record.asset;
    const supplier = record.supplier;
    const assignedStaff = record.assignedStaff;
    const paymentMethod = record.paymentMethod;
    const tax = record.tax;

    // Get user names for createdBy and updatedBy
    const createdByName = await this.usersService.getUserName(
      assetMaintenance.createdBy.toString(),
    );
    let updatedByName: string | undefined;
    if (assetMaintenance.updatedBy) {
      updatedByName = await this.usersService.getUserName(
        assetMaintenance.updatedBy.toString(),
      );
    }

    // Get quality check staff name if exists
    let qualityCheckByName: string | undefined;
    if (assetMaintenance.qualityCheckBy) {
      qualityCheckByName = await this.getStaffMemberName(
        assetMaintenance.qualityCheckBy,
      );
    }

    return {
      id: assetMaintenance.id,
      assetId: assetMaintenance.assetId,
      workOrderNumber: assetMaintenance.workOrderNumber,
      title: assetMaintenance.title,
      description: assetMaintenance.description,
      status: assetMaintenance.status,
      priority: assetMaintenance.priority,
      supplierName: supplier?.name,
      assignedToName:
        assignedStaff?.firstName && assignedStaff?.lastName
          ? `${assignedStaff.firstName} ${assignedStaff.lastName}`
          : assignedStaff?.firstName || assignedStaff?.lastName,
      qualityCheckByName,
      completedAt: assetMaintenance.completedAt?.toISOString(),
      paymentDate: assetMaintenance.paymentDate,
      paymentMethodName: paymentMethod?.name,
      paymentReferenceNumber: assetMaintenance.paymentReferenceNumber,
      amountType: assetMaintenance.amountType,
      subtotal: assetMaintenance.subtotal,
      total: assetMaintenance.total,
      taxName: tax?.name,
      assetName: asset?.name,
      assetCode: asset?.assetCode,
      createdAt: assetMaintenance.createdAt.toISOString(),
      updatedAt: assetMaintenance.updatedAt.toISOString(),
      createdByName,
      updatedByName,
    };
  }

  private async getAssetInfo(
    assetId: string,
  ): Promise<{ name?: string; assetCode?: string } | null> {
    try {
      const asset = await this.db
        .select({
          name: assets.name,
          assetCode: assets.assetCode,
        })
        .from(assets)
        .where(eq(assets.id, assetId))
        .then((results) => results[0]);

      return asset || null;
    } catch (error) {
      console.warn(`Failed to get asset info for ${assetId}:`, error.message);
      return null;
    }
  }

  private async getSupplierName(supplierId: string): Promise<string | null> {
    try {
      const supplier = await this.db
        .select({ name: suppliers.companyName })
        .from(suppliers)
        .where(eq(suppliers.id, supplierId))
        .then((results) => results[0]);

      return supplier?.name || null;
    } catch (error) {
      console.warn(
        `Failed to get supplier name for ${supplierId}:`,
        error.message,
      );
      return null;
    }
  }

  private async getStaffMemberName(staffId: string): Promise<string | null> {
    try {
      const staff = await this.db
        .select({
          firstName: staffMembers.firstName,
          lastName: staffMembers.lastName,
        })
        .from(staffMembers)
        .where(eq(staffMembers.id, staffId))
        .then((results) => results[0]);

      if (staff) {
        return staff.firstName && staff.lastName
          ? `${staff.firstName} ${staff.lastName}`
          : staff.firstName || staff.lastName || null;
      }
      return null;
    } catch (error) {
      console.warn(
        `Failed to get staff member name for ${staffId}:`,
        error.message,
      );
      return null;
    }
  }

  private async getAccountName(accountId: string): Promise<string | null> {
    try {
      const account = await this.db
        .select({ name: accounts.name })
        .from(accounts)
        .where(eq(accounts.id, accountId))
        .then((results) => results[0]);

      return account?.name || null;
    } catch (error) {
      console.warn(
        `Failed to get account name for ${accountId}:`,
        error.message,
      );
      return null;
    }
  }

  private async getPaymentMethodName(
    paymentMethodId: string,
  ): Promise<string | null> {
    try {
      const paymentMethod = await this.db
        .select({ name: paymentMethods.name })
        .from(paymentMethods)
        .where(eq(paymentMethods.id, paymentMethodId))
        .then((results) => results[0]);

      return paymentMethod?.name || null;
    } catch (error) {
      console.warn(
        `Failed to get payment method name for ${paymentMethodId}:`,
        error.message,
      );
      return null;
    }
  }

  private async getTaxName(taxId: string): Promise<string | null> {
    try {
      const tax = await this.db
        .select({ name: taxes.taxName })
        .from(taxes)
        .where(eq(taxes.id, taxId))
        .then((results) => results[0]);

      return tax?.name || null;
    } catch (error) {
      console.warn(`Failed to get tax name for ${taxId}:`, error.message);
      return null;
    }
  }

  private async getAttachments(
    assetMaintenanceId: string,
    businessId: string,
  ): Promise<Array<{ id: string; originalName: string; signedUrl: string }>> {
    try {
      const mediaRecords = await this.mediaService.findByReferenceId(
        assetMaintenanceId,
        businessId,
      );

      const attachmentsWithSignedUrls = await Promise.all(
        mediaRecords.map(async (mediaRecord) => {
          try {
            const signedUrl = await this.mediaService.generateSignedUrlForMedia(
              mediaRecord.id,
              businessId,
              'asset-maintenances',
            );
            return {
              id: mediaRecord.id,
              originalName: mediaRecord.originalName,
              signedUrl,
            };
          } catch (error) {
            console.warn(
              `Failed to generate signed URL for media ${mediaRecord.id}:`,
              error.message,
            );
            return {
              id: mediaRecord.id,
              originalName: mediaRecord.originalName,
              signedUrl: mediaRecord.publicUrl,
            };
          }
        }),
      );

      return attachmentsWithSignedUrls;
    } catch (error) {
      console.warn(
        `Failed to get attachments for asset maintenance ${assetMaintenanceId}:`,
        error.message,
      );
      return [];
    }
  }

  async bulkUpdateAssetMaintenanceStatus(
    userId: string,
    businessId: string | null,
    assetMaintenanceIds: string[],
    status: MaintenanceStatus,
    metadata?: ActivityMetadata,
  ): Promise<{
    updated: number;
    updatedIds: string[];
    failed: Array<{ assetMaintenanceId: string; error: string }>;
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!assetMaintenanceIds || assetMaintenanceIds.length === 0) {
        throw new BadRequestException(
          'No asset maintenance IDs provided for status update',
        );
      }

      let updatedCount = 0;
      const updatedIds: string[] = [];
      const failed: Array<{ assetMaintenanceId: string; error: string }> = [];

      // Process each asset maintenance ID
      await this.db.transaction(async (tx) => {
        for (const assetMaintenanceId of assetMaintenanceIds) {
          try {
            // Check if asset maintenance exists and belongs to the business
            const existingAssetMaintenance = await tx
              .select()
              .from(assetMaintenances)
              .where(
                and(
                  eq(assetMaintenances.id, assetMaintenanceId),
                  eq(assetMaintenances.businessId, businessId),
                  eq(assetMaintenances.isDeleted, false),
                ),
              )
              .then((results) => results[0]);

            if (!existingAssetMaintenance) {
              failed.push({
                assetMaintenanceId,
                error: 'Asset maintenance not found or access denied',
              });
              continue;
            }

            // Skip if status is already the same
            if (existingAssetMaintenance.status === status) {
              failed.push({
                assetMaintenanceId,
                error: `Asset maintenance already has status: ${status}`,
              });
              continue;
            }

            // Update the asset maintenance status
            await tx
              .update(assetMaintenances)
              .set({
                status,
                updatedAt: new Date(),
                updatedBy: userId,
              })
              .where(
                and(
                  eq(assetMaintenances.id, assetMaintenanceId),
                  eq(assetMaintenances.businessId, businessId),
                  eq(assetMaintenances.isDeleted, false),
                ),
              );

            updatedCount++;
            updatedIds.push(assetMaintenanceId);

            // Log the status update activity
            await this.activityLogService.logUpdate(
              assetMaintenanceId,
              EntityType.ASSET,
              userId,
              businessId,
              {
                source: ActivitySource.WEB,
                ipAddress: undefined,
                userAgent: undefined,
              },
            );
          } catch (error) {
            failed.push({
              assetMaintenanceId,
              error: `Failed to update: ${error.message}`,
            });
          }
        }
      });

      // Log bulk status update operation if any were updated
      if (updatedIds.length > 0) {
        await this.activityLogService.logBulkOperation(
          ActivityType.BULK_STATUS_CHANGE,
          EntityType.ASSET,
          updatedIds,
          { status },
          userId,
          businessId,
          {
            filterCriteria: { assetMaintenanceIds, targetStatus: status },
            failures: failed.map((f) => ({
              id: f.assetMaintenanceId,
              error: f.error,
            })),
            executionStrategy: ExecutionStrategy.SEQUENTIAL,
            source: ActivitySource.WEB,
            ipAddress: undefined,
            userAgent: undefined,
          },
        );
      }

      return {
        updated: updatedCount,
        updatedIds,
        failed,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk update asset maintenance status: ${error.message}`,
      );
    }
  }
}
