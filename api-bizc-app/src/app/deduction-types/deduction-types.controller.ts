import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  Query,
  UseGuards,
} from '@nestjs/common';
import { DeductionTypesService } from './deduction-types.service';
import { CreateDeductionTypeDto } from './dto/create-deduction-type.dto';
import { UpdateDeductionTypeDto } from './dto/update-deduction-type.dto';
import { DeductionTypeDto } from './dto/deduction-type.dto';
import { DeductionTypeSlimDto } from './dto/deduction-type-slim.dto';
import { DeductionTypeIdResponseDto } from './dto/deduction-type-id-response.dto';
import { DeleteDeductionTypeResponseDto } from './dto/delete-deduction-type-response.dto';
import { PaginatedDeductionTypesListResponseDto } from './dto/paginated-deduction-types-list-response.dto';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiParam,
  ApiBearerAuth,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { DeductionTypeCodeAvailabilityResponseDto } from './dto/check-deduction-type-code.dto';
import { BulkCreateDeductionTypeDto } from './dto/bulk-create-deduction-type.dto';
import { BulkDeductionTypeIdsResponseDto } from './dto/bulk-deduction-type-ids-response.dto';
import { BulkDeleteDeductionTypeDto } from './dto/bulk-delete-deduction-type.dto';
import { BulkDeleteDeductionTypeResponseDto } from './dto/bulk-delete-deduction-type-response.dto';
import {
  BulkUpdateDeductionTypeStatusDto,
  BulkUpdateDeductionTypeStatusResponseDto,
} from './dto/bulk-update-deduction-type-status.dto';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { ActivityMetadata } from '../auth/decorators/activity-metadata.decorator';
import { Permission } from '../shared/types/permission.enum';
import type { ActivityMetadata as ActivityMetadataType } from '../shared/types/activity-metadata.type';

@ApiTags('deduction-types')
@Controller('deduction-types')
@UseGuards(PermissionsGuard)
export class DeductionTypesController {
  constructor(private readonly deductionTypesService: DeductionTypesService) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.DEDUCTION_TYPE_CREATE)
  @ApiOperation({ summary: 'Create a new deduction type' })
  @ApiResponse({
    status: 201,
    description: 'The deduction type has been successfully created',
    type: DeductionTypeIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Deduction type code already exists',
  })
  create(
    @Request() req,
    @Body() createDeductionTypeDto: CreateDeductionTypeDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<DeductionTypeIdResponseDto> {
    return this.deductionTypesService.createAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      createDeductionTypeDto,
      metadata,
    );
  }

  @Post('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.DEDUCTION_TYPE_CREATE)
  @ApiOperation({ summary: 'Bulk create deduction types' })
  @ApiBody({
    description: 'Bulk create deduction types data',
    type: BulkCreateDeductionTypeDto,
  })
  @ApiResponse({
    status: 201,
    description: 'The deduction types have been successfully created',
    type: BulkDeductionTypeIdsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Deduction type codes already exist',
  })
  bulkCreate(
    @Request() req,
    @Body() bulkCreateDeductionTypeDto: BulkCreateDeductionTypeDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkDeductionTypeIdsResponseDto> {
    return this.deductionTypesService.bulkCreateAndReturnIds(
      req.user.id,
      req.user.activeBusinessId,
      bulkCreateDeductionTypeDto.deductionTypes,
      metadata,
    );
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.DEDUCTION_TYPE_READ)
  @ApiOperation({
    summary: 'Get all deduction types for the active business',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'from',
    description: 'Filter from date (YYYY-MM-DD format)',
    required: false,
    type: String,
    example: '2025-01-01',
  })
  @ApiQuery({
    name: 'to',
    description: 'Filter to date (YYYY-MM-DD format)',
    required: false,
    type: String,
    example: '2025-12-31',
  })
  @ApiQuery({
    name: 'deductionName',
    description: 'Filter by deduction name',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'deductionCode',
    description: 'Filter by deduction code',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'calculationMethod',
    description: 'Filter by calculation method',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'isSystemDefined',
    description: 'Filter by system defined status (true/false)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'isActive',
    description: 'Filter by active status (true/false)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'appliesTo',
    description: 'Filter by applies to',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'filters',
    description: 'Advanced filters in JSON format',
    required: false,
    type: String,
    example: '[{"id":"deductionName","value":"Salary","operator":"iLike"}]',
  })
  @ApiQuery({
    name: 'joinOperator',
    description: 'Join operator for filters (and/or)',
    required: false,
    enum: ['and', 'or'],
  })
  @ApiQuery({
    name: 'sort',
    description: 'Sort configuration in JSON format',
    required: false,
    type: String,
    example: '[{"id":"deductionName","desc":false}]',
  })
  @ApiResponse({
    status: 200,
    description:
      "Returns all deduction types for the user's active business with pagination",
    type: PaginatedDeductionTypesListResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Request() req,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('deductionName') deductionName?: string,
    @Query('deductionCode') deductionCode?: string,
    @Query('calculationMethod') calculationMethod?: string,
    @Query('isSystemDefined') isSystemDefined?: string,
    @Query('isActive') isActive?: string,
    @Query('appliesTo') appliesTo?: string,
    @Query('filters') filters?: string,
    @Query('joinOperator') joinOperator?: 'and' | 'or',
    @Query('sort') sort?: string,
  ): Promise<PaginatedDeductionTypesListResponseDto> {
    return this.deductionTypesService.findAllOptimized(
      req.user.id,
      req.user.activeBusinessId,
      page ? parseInt(page.toString()) : undefined,
      limit ? parseInt(limit.toString()) : undefined,
      from,
      to,
      deductionName,
      deductionCode,
      calculationMethod,
      isSystemDefined,
      isActive,
      appliesTo,
      filters,
      joinOperator,
      sort,
    );
  }

  @Get('check-availability')
  @ApiBearerAuth()
  @RequirePermissions(Permission.DEDUCTION_TYPE_READ)
  @ApiOperation({ summary: 'Check if a deduction type code is available' })
  @ApiQuery({
    name: 'deductionCode',
    description: 'Deduction type code to check',
    required: true,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Returns whether the deduction type code is available',
    type: DeductionTypeCodeAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkDeductionCodeAvailability(
    @Request() req,
    @Query('deductionCode') deductionCode: string,
  ): Promise<{ available: boolean }> {
    return this.deductionTypesService.checkDeductionCodeAvailability(
      req.user.id,
      req.user.activeBusinessId,
      deductionCode,
    );
  }

  @Get('slim')
  @ApiBearerAuth()
  @RequirePermissions(Permission.DEDUCTION_TYPE_READ)
  @ApiOperation({ summary: 'Get all deduction types in slim format' })
  @ApiResponse({
    status: 200,
    description: 'All deduction types returned successfully',
    type: [DeductionTypeSlimDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllSlim(@Request() req): Promise<DeductionTypeSlimDto[]> {
    return this.deductionTypesService.findAllSlim(
      req.user.id,
      req.user.activeBusinessId,
    );
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.DEDUCTION_TYPE_READ)
  @ApiOperation({ summary: 'Get a deduction type by ID' })
  @ApiParam({
    name: 'id',
    description: 'Deduction type ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the deduction type',
    type: DeductionTypeDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Deduction type not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to this deduction type',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findOne(@Request() req, @Param('id') id: string): Promise<DeductionTypeDto> {
    return this.deductionTypesService.findOne(req.user.id, id);
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.DEDUCTION_TYPE_UPDATE)
  @ApiOperation({ summary: 'Update a deduction type' })
  @ApiParam({
    name: 'id',
    description: 'Deduction type ID',
  })
  @ApiResponse({
    status: 200,
    description: 'The deduction type has been successfully updated',
    type: DeductionTypeIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 404,
    description: 'Deduction type not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to this deduction type',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Deduction type code already exists',
  })
  update(
    @Request() req,
    @Param('id') id: string,
    @Body() updateDeductionTypeDto: UpdateDeductionTypeDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<DeductionTypeIdResponseDto> {
    return this.deductionTypesService.updateAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateDeductionTypeDto,
      metadata,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.DEDUCTION_TYPE_DELETE)
  @ApiOperation({ summary: 'Delete a deduction type' })
  @ApiParam({
    name: 'id',
    description: 'Deduction type ID',
  })
  @ApiResponse({
    status: 200,
    description: 'The deduction type has been successfully deleted',
    type: DeleteDeductionTypeResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Deduction type not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to delete this deduction type',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  remove(
    @Request() req,
    @Param('id') id: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<DeleteDeductionTypeResponseDto> {
    return this.deductionTypesService.remove(
      req.user.id,
      req.user.activeBusinessId,
      id,
      metadata,
    );
  }

  @Delete('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.DEDUCTION_TYPE_DELETE)
  @ApiOperation({ summary: 'Bulk delete deduction types' })
  @ApiBody({
    description: 'Array of deduction type IDs to delete',
    type: BulkDeleteDeductionTypeDto,
  })
  @ApiResponse({
    status: 200,
    description: 'The deduction types have been successfully deleted',
    type: BulkDeleteDeductionTypeResponseDto,
  })
  @ApiResponse({
    status: 400,
    description:
      'Bad request - Invalid data or deduction types have employee deductions',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'No valid deduction types found for deletion',
  })
  async bulkDelete(
    @Request() req,
    @Body() bulkDeleteDeductionTypeDto: BulkDeleteDeductionTypeDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkDeleteDeductionTypeResponseDto> {
    const result = await this.deductionTypesService.bulkDelete(
      req.user.id,
      req.user.activeBusinessId,
      bulkDeleteDeductionTypeDto.deductionTypeIds,
      metadata,
    );

    return {
      deleted: result.deleted,
      message: result.message,
      deletedIds: result.deletedIds,
    };
  }

  @Patch('bulk/status')
  @ApiBearerAuth()
  @RequirePermissions(Permission.DEDUCTION_TYPE_UPDATE)
  @ApiOperation({ summary: 'Bulk update deduction type status' })
  @ApiBody({
    description: 'Bulk update deduction type status data',
    type: BulkUpdateDeductionTypeStatusDto,
  })
  @ApiResponse({
    status: 200,
    description: 'The deduction type statuses have been successfully updated',
    type: BulkUpdateDeductionTypeStatusResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async bulkUpdateStatus(
    @Request() req,
    @Body() bulkUpdateStatusDto: BulkUpdateDeductionTypeStatusDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkUpdateDeductionTypeStatusResponseDto> {
    const result =
      await this.deductionTypesService.bulkUpdateDeductionTypeStatus(
        req.user.id,
        req.user.activeBusinessId,
        bulkUpdateStatusDto.deductionTypeIds,
        bulkUpdateStatusDto.isActive,
        metadata,
      );

    return {
      updated: result.updated,
      message: `Successfully updated status for ${result.updated} deduction types`,
      updatedIds: result.updatedIds,
      failed: result.failed.length > 0 ? result.failed : undefined,
    };
  }
}
