import {
  Injectable,
  Inject,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { AuthService } from '../auth/auth.service';
import { MediaService } from '../media/media.service';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { business } from '../drizzle/schema/business.schema';
import { users } from '../drizzle/schema/users.schema';
import { addresses } from '../drizzle/schema/address.schema';
import { media } from '../drizzle/schema/media.schema';
import { businessUsers } from '../drizzle/schema/business-users.schema';
import { businessModules } from '../drizzle/schema/modules.schema';
import { businessRewardSettings } from '../drizzle/schema/business-reward-settings.schema';
import {
  workingHours,
  DEFAULT_WORKING_HOURS,
} from '../drizzle/schema/working-hours.schema';
import {
  businessAccountingSettings,
  DEFAULT_BUSINESS_ACCOUNTING_SETTINGS,
} from '../drizzle/schema/business-accounting-settings.schema';
import { businessAmenities } from '../drizzle/schema/amenities.schema';
import { eq, and } from 'drizzle-orm';
import { RegisterBusinessDto } from './dto/register-business.dto';
import {
  UpdateBusinessSettingsWithLogoDto,
  BusinessSettingsResponseDto,
} from './dto/business-settings.dto';
import {
  UpdateBusinessRewardSettingsDto,
  BusinessRewardSettingsResponseDto,
} from './dto/business-reward-settings.dto';
import {
  UpdateWorkingHoursDto,
  WorkingHoursResponseDto,
} from './dto/working-hours.dto';
import {
  UpdateBusinessAccountingSettingsDto,
  BusinessAccountingSettingsResponseDto,
} from './dto/business-accounting-settings.dto';
import {
  UpdateBusinessThemeSettingsDto,
  BusinessThemeSettingsResponseDto,
  ThemeSettingsData,
} from './dto/business-theme-settings.dto';
import {
  UpdateBusinessStandardUnitsSettingsDto,
  BusinessStandardUnitsSettingsResponseDto,
} from './dto/business-standard-units-settings.dto';
import {
  UpdateBusinessStandardPaymentMethodsSettingsDto,
  BusinessStandardPaymentMethodsSettingsResponseDto,
} from './dto/business-standard-payment-methods-settings.dto';
import {
  UpdateBusinessAmenitiesSettingsDto,
  BusinessAmenitiesSettingsResponseDto,
} from './dto/business-amenities-settings.dto';
import {
  AddressType,
  BusinessStatus,
  BusinessType,
  ModuleType,
  UserRole,
  ActivityLogName,
  LocationType,
  StandardUnitOfMeasure,
  StandardPaymentMethod,
  BusinessUserRole,
  BusinessUserStatus,
} from '../shared/types';
import { Amenity } from '../shared/types/amenity.enum';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { locations } from '../drizzle/schema/locations.schema';
import { userLocations } from '../drizzle/schema/user-locations.schema';
import { AuthProfileResponseDto } from '../shared/dto/auth-profile-response.dto';
import type { ActivityMetadata } from '../shared/types/activity-metadata.type';

@Injectable()
export class BusinessService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly authService: AuthService,
    private readonly activityLogService: ActivityLogService,
    private readonly mediaService: MediaService,
  ) {}

  async registerBusiness(
    registerBusinessDto: RegisterBusinessDto,
  ): Promise<AuthProfileResponseDto> {
    try {
      // First, register the user
      const { user, business: businessInfo } = registerBusinessDto;

      // Set role to 'shop' for business users
      user.role = UserRole.SHOP;

      // Register the user
      const userRegistration = await this.authService.register(user);

      let businessData = null;

      console.log('businessInfo :::: ', businessInfo);

      // Only create business if businessInfo is provided
      if (businessInfo) {
        // Get modules to assign based on business type
        const modulesToAssign = this.getModulesForBusinessType(
          businessInfo.businessType,
        );

        // Create the business
        const [newBusiness] = await this.db
          .insert(business)
          .values({
            ownerId: userRegistration.user._id,
            name: businessInfo.businessName,
            phone: businessInfo.businessPhone,
            logo: businessInfo.businessLogo,
            businessType: businessInfo.businessType,
            status: BusinessStatus.PENDING_APPROVAL,
          })
          .returning();

        // Create address for the business
        const [businessAddress] = await this.db
          .insert(addresses)
          .values({
            businessId: newBusiness.id,
            street: businessInfo.streetAddress,
            city: businessInfo.city,
            state: businessInfo.state,
            zipCode: businessInfo.postalCode,
            country: businessInfo.country,
            addressType: AddressType.BUSINESS,
            isDefault: true,
          })
          .returning();

        // Create default location for the business
        const [newLocation] = await this.db
          .insert(locations)
          .values({
            businessId: newBusiness.id,
            name: businessInfo.businessName,
            code: 'LOC-1',
            addressId: businessAddress.id,
            type: LocationType.MAINSITE,
            createdBy: userRegistration.user._id,
          })
          .returning();

        // Assign user to this location
        await this.db.insert(userLocations).values({
          userId: userRegistration.user._id,
          locationId: newLocation.id,
          businessId: newBusiness.id,
          status: 'active',
        });

        // Create business user with ADMIN role for the business owner
        await this.createAdminBusinessUser(
          newBusiness.id,
          userRegistration.user._id,
        );

        // Update the user with the activeBusinessId
        await this.db
          .update(users)
          .set({ activeBusinessId: newBusiness.id })
          .where(eq(users.id, userRegistration.user._id));

        // Assign business modules
        await this.assignBusinessModules(
          newBusiness.id,
          businessInfo.businessType,
          userRegistration.user._id,
        );

        // Fetch the assigned business modules
        const moduleTypes = await this.getBusinessModules(newBusiness.id);

        businessData = {
          businessId: newBusiness.id.toString(),
          businessStatus: newBusiness.status || BusinessStatus.PENDING_APPROVAL,
          businessModules: moduleTypes,
          currency: 'USD',
          allowedLocations: [
            // Default location now using the actual location data
            {
              id: newLocation.id,
              name: newLocation.name,
            },
          ],
        };

        // Log business registration activity
        await this.activityLogService.log(
          ActivityLogName.CREATE,
          `Business registered: ${businessInfo.businessName}`,
          { id: newBusiness.id.toString(), type: 'business' },
          { id: userRegistration.user._id, type: 'user' },
          {
            businessType: businessInfo.businessType,
            modules: modulesToAssign,
          },
        );
      }

      // Return response with user and business information
      return {
        user: userRegistration.user,
        business: businessData,
        tokens: userRegistration.tokens,
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        'Failed to register business: ' + error.message,
      );
    }
  }

  private getModulesForBusinessType(businessType: BusinessType): ModuleType[] {
    // Define modules based on business type
    const moduleTypesToAssign: ModuleType[] = [
      // Common modules for all business types
      ModuleType.FINANCE,
      ModuleType.CRM,
      ModuleType.SUPPORT,
      ModuleType.PAYMENT_ACCOUNT,
      ModuleType.BANK_ACCOUNTS,
      ModuleType.PAYMENT_GATEWAYS,
      ModuleType.TRANSACTIONS,
    ];

    // Add business-type specific modules
    switch (businessType) {
      case BusinessType.RETAIL:
        moduleTypesToAssign.push(
          ModuleType.INVENTORY,
          ModuleType.MARKETING,
          ModuleType.RETAIL_POS,
          ModuleType.SALES,
        );
        break;
      case BusinessType.RESTAURANT:
        moduleTypesToAssign.push(
          ModuleType.INVENTORY,
          ModuleType.MARKETING,
          ModuleType.RESTAURANT_POS,
        );
        break;
      case BusinessType.SERVICE_PROVIDER:
        moduleTypesToAssign.push(
          ModuleType.HRM,
          ModuleType.WORKORDERS,
          ModuleType.VEHICLES,
          ModuleType.VEHICLE_RESERVATIONS,
          ModuleType.ATTENDANCE,
          ModuleType.PAYROLL,
        );
        break;
      default:
        // For OTHER business type
        moduleTypesToAssign.push(ModuleType.MARKETING, ModuleType.INVENTORY);
        break;
    }

    return moduleTypesToAssign;
  }

  private async assignBusinessModules(
    businessId: string,
    businessType: BusinessType,
    causerId?: string,
  ): Promise<void> {
    console.log('businessId :::: ', businessId);

    // Get modules to assign based on business type
    const modulesToAssign = this.getModulesForBusinessType(businessType);

    // Insert modules into the junction table
    const moduleInserts = modulesToAssign.map((moduleType) => ({
      businessId: businessId,
      moduleType: moduleType as any,
      createdBy: causerId || businessId, // Use businessId as fallback if causerId is undefined
    }));

    await this.db.insert(businessModules).values(moduleInserts as any);

    // Log module assignment if causerId is provided
    if (causerId) {
      await this.activityLogService.log(
        ActivityLogName.UPDATE,
        `Modules assigned to business ID: ${businessId}`,
        { id: businessId, type: 'business' },
        { id: causerId, type: 'user' },
        {
          businessType,
          modules: modulesToAssign,
        },
      );
    }
  }

  async getAllBusinesses(): Promise<BusinessSettingsResponseDto[]> {
    try {
      // Get all businesses with their basic information
      const businessesData = await this.db
        .select({
          id: business.id,
          name: business.name,
          email: business.email,
          phone: business.phone,
          website: business.website,
          businessType: business.businessType,
          status: business.status,
          addressId: business.addressId,
          logoId: business.logo,
          createdAt: business.createdAt,
          updatedAt: business.updatedAt,
        })
        .from(business);

      // Process each business to get complete information
      const businessesWithDetails = await Promise.all(
        businessesData.map(async (businessData) => {
          // Get address information if addressId exists
          let addressData = null;
          if (businessData.addressId) {
            addressData = await this.db
              .select()
              .from(addresses)
              .where(eq(addresses.id, businessData.addressId))
              .then((results) => results[0]);
          }

          // Get logo information if logoId exists
          let logoUrl = null;
          if (businessData.logoId) {
            const logoData = await this.db
              .select()
              .from(media)
              .where(eq(media.id, businessData.logoId))
              .then((results) => results[0]);

            if (logoData) {
              logoUrl = logoData.publicUrl;
            }
          }

          return {
            id: businessData.id,
            businessName: businessData.name,
            businessEmail: businessData.email || undefined,
            businessPhone: businessData.phone || undefined,
            businessType: businessData.businessType,
            status: businessData.status,
            regNumber: undefined, // Not stored in current schema
            website: businessData.website || undefined,
            address: addressData
              ? {
                  street: addressData.street,
                  city: addressData.city,
                  state: addressData.state,
                  zipCode: addressData.zipCode,
                  country: addressData.country,
                }
              : undefined,
            businessLogo: logoUrl || undefined,
            createdAt: businessData.createdAt,
            updatedAt: businessData.updatedAt,
          };
        }),
      );

      return businessesWithDetails;
    } catch (error) {
      throw new BadRequestException(
        'Failed to get all businesses: ' + error.message,
      );
    }
  }

  async getBusinessModules(businessId: string): Promise<ModuleType[]> {
    // Get the business modules from the junction table
    const businessModuleData = await this.db
      .select({ moduleType: businessModules.moduleType })
      .from(businessModules)
      .where(eq(businessModules.businessId, businessId));

    // Return just the module types
    return businessModuleData.map((item) => item.moduleType);
  }

  async updateBusinessModules(
    businessId: string,
    modules: string[],
    causerId?: string,
    metadata?: ActivityMetadata,
  ): Promise<{ success: boolean; modules: ModuleType[] }> {
    // Validate that the business exists
    const businessExists = await this.db
      .select({ id: business.id })
      .from(business)
      .where(eq(business.id, businessId));

    if (!businessExists.length) {
      throw new NotFoundException(`Business with ID ${businessId} not found`);
    }

    // Convert string array to ModuleType array and validate each module
    const validModules: ModuleType[] = [];

    for (const module of modules) {
      // Check if the module is a valid ModuleType
      if (Object.values(ModuleType).includes(module as ModuleType)) {
        validModules.push(module as ModuleType);
      }
    }

    // Delete existing modules for this business
    await this.db
      .delete(businessModules)
      .where(eq(businessModules.businessId, businessId));

    // Insert new modules
    if (validModules.length > 0) {
      const moduleInserts = validModules.map((moduleType) => ({
        businessId: businessId,
        moduleType: moduleType as any,
        createdBy: causerId || businessId,
      }));

      await this.db.insert(businessModules).values(moduleInserts as any);
    }

    // Log modules update activity
    if (causerId) {
      await this.activityLogService.log(
        ActivityLogName.UPDATE,
        `Business modules updated for ID: ${businessId}`,
        { id: businessId, type: 'business' },
        { id: causerId, type: 'user' },
        {
          updatedModules: validModules,
        },
      );
    }

    return {
      success: true,
      modules: validModules,
    };
  }

  async addBusinessModule(
    businessId: string,
    module: string,
    causerId?: string,
    metadata?: ActivityMetadata,
  ): Promise<{ success: boolean; modules: ModuleType[] }> {
    // Validate that the module is a valid ModuleType
    if (!Object.values(ModuleType).includes(module as ModuleType)) {
      throw new BadRequestException(`Invalid module type: ${module}`);
    }

    // Check if the business exists
    const businessExists = await this.db
      .select({ id: business.id })
      .from(business)
      .where(eq(business.id, businessId));

    if (!businessExists.length) {
      throw new NotFoundException(`Business with ID ${businessId} not found`);
    }

    // Check if module already exists for this business
    const existingModule = await this.db
      .select({ id: businessModules.id })
      .from(businessModules)
      .where(
        and(
          eq(businessModules.businessId, businessId),
          eq(businessModules.moduleType, module as any),
        ),
      );

    if (existingModule.length > 0) {
      // Module already exists, return current modules
      const currentModules = await this.getBusinessModules(businessId);
      return {
        success: true,
        modules: currentModules,
      };
    }

    // Add the new module
    await this.db.insert(businessModules).values({
      businessId: businessId,
      moduleType: module as any,
      createdBy: causerId || businessId,
    } as any);

    // Get updated modules list
    const updatedModules = await this.getBusinessModules(businessId);

    // Log module addition activity
    if (causerId) {
      await this.activityLogService.log(
        ActivityLogName.UPDATE,
        `Module added to business ID: ${businessId}`,
        { id: businessId, type: 'business' },
        { id: causerId, type: 'user' },
        {
          addedModule: module,
          allModules: updatedModules,
        },
      );
    }

    return {
      success: true,
      modules: updatedModules,
    };
  }

  async removeBusinessModule(
    businessId: string,
    module: string,
    causerId?: string,
    metadata?: ActivityMetadata,
  ): Promise<{ success: boolean; modules: ModuleType[] }> {
    // Validate that the module is a valid ModuleType
    if (!Object.values(ModuleType).includes(module as ModuleType)) {
      throw new BadRequestException(`Invalid module type: ${module}`);
    }

    // Check if the business exists
    const businessExists = await this.db
      .select({ id: business.id })
      .from(business)
      .where(eq(business.id, businessId));

    if (!businessExists.length) {
      throw new NotFoundException(`Business with ID ${businessId} not found`);
    }

    // Delete the module from the junction table
    await this.db
      .delete(businessModules)
      .where(
        and(
          eq(businessModules.businessId, businessId),
          eq(businessModules.moduleType, module as any),
        ),
      );

    // Get updated modules list
    const updatedModules = await this.getBusinessModules(businessId);

    // Log module removal activity only if something was actually deleted
    if (causerId) {
      await this.activityLogService.log(
        ActivityLogName.DELETE,
        `Module removed from business ID: ${businessId}`,
        { id: businessId, type: 'business' },
        { id: causerId, type: 'user' },
        {
          removedModule: module,
          remainingModules: updatedModules,
        },
      );
    }

    return {
      success: true,
      modules: updatedModules,
    };
  }

  /**
   * Creates business user with ADMIN role for the business owner
   * @param businessId The business ID
   * @param ownerId The owner's user ID
   */
  private async createAdminBusinessUser(
    businessId: string,
    ownerId: string,
  ): Promise<void> {
    try {
      // Create business user record with ADMIN role (no business role needed)
      await this.db.insert(businessUsers).values({
        businessId: businessId,
        userId: ownerId,
        role: BusinessUserRole.ADMIN,
        businessRoleId: null, // ADMIN users don't need a business role
        isActiveBusiness: true,
        isAllowedAllLocation: true,
        joinedAt: new Date(),
        lastActiveAt: new Date(),
        status: BusinessUserStatus.ACTIVE,
        createdBy: ownerId,
      });

      // Log activity
      // await this.activityLogService.log(
      //   ActivityLogName.CREATE,
      //   `Admin role created and assigned to owner`,
      //   { id: Number(businessId), type: 'business' },
      //   { id: Number(ownerId), type: 'user' },
      //   {
      //     roleId: adminRole.id,
      //   },
      // );
    } catch (error) {
      throw new BadRequestException(
        'Failed to create admin role: ' + error.message,
      );
    }
  }

  async getBusinessSettings(
    businessId: string,
    userId: string,
  ): Promise<BusinessSettingsResponseDto> {
    try {
      // Get business with address and logo information
      const businessData = await this.db
        .select({
          id: business.id,
          name: business.name,
          email: business.email,
          phone: business.phone,
          website: business.website,
          businessType: business.businessType,
          addressId: business.addressId,
          logoId: business.logo,
          createdAt: business.createdAt,
          updatedAt: business.updatedAt,
        })
        .from(business)
        .where(eq(business.id, businessId))
        .then((results) => results[0]);

      if (!businessData) {
        throw new NotFoundException('Business not found');
      }

      // Get address information if addressId exists
      let addressData = null;
      if (businessData.addressId) {
        addressData = await this.db
          .select()
          .from(addresses)
          .where(eq(addresses.id, businessData.addressId))
          .then((results) => results[0]);
      }

      // Get logo information if logoId exists
      let logoUrl = null;
      if (businessData.logoId) {
        const logoData = await this.db
          .select()
          .from(media)
          .where(eq(media.id, businessData.logoId))
          .then((results) => results[0]);

        if (logoData) {
          logoUrl = logoData.publicUrl;
        }
      }

      // Log activity
      await this.activityLogService.log(
        ActivityLogName.VIEW,
        `Viewed business settings`,
        { id: businessId, type: 'business' },
        { id: userId, type: 'user' },
        { businessId },
      );

      return {
        id: businessData.id,
        businessName: businessData.name,
        businessEmail: businessData.email || undefined,
        businessPhone: businessData.phone || undefined,
        businessType: businessData.businessType,
        regNumber: undefined, // Not stored in current schema
        website: businessData.website || undefined,
        address: addressData
          ? {
              street: addressData.street,
              city: addressData.city,
              state: addressData.state,
              zipCode: addressData.zipCode,
              country: addressData.country,
            }
          : undefined,
        businessLogo: logoUrl || undefined,
        createdAt: businessData.createdAt,
        updatedAt: businessData.updatedAt,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(
        'Failed to get business settings: ' + error.message,
      );
    }
  }

  async updateBusinessSettingsWithLogo(
    businessId: string,
    userId: string,
    updateDto: UpdateBusinessSettingsWithLogoDto,
    logoFile?: Express.Multer.File,
    metadata?: ActivityMetadata,
  ): Promise<BusinessSettingsResponseDto> {
    try {
      // Check if business exists
      const existingBusiness = await this.db
        .select()
        .from(business)
        .where(eq(business.id, businessId))
        .then((results) => results[0]);

      if (!existingBusiness) {
        throw new NotFoundException('Business not found');
      }

      let addressId = existingBusiness.addressId;
      let logoId = existingBusiness.logo;

      // Handle logo upload if provided
      if (logoFile) {
        const uploadedMedia = await this.mediaService.uploadMedia(
          logoFile,
          'business-logos',
          businessId,
          userId,
        );
        logoId = uploadedMedia.id;
      }

      // Handle address update/creation
      if (updateDto.address) {
        if (addressId) {
          // Update existing address
          await this.db
            .update(addresses)
            .set({
              street: updateDto.address.street,
              city: updateDto.address.city,
              state: updateDto.address.state,
              zipCode: updateDto.address.zipCode,
              country: updateDto.address.country,
              updatedAt: new Date(),
            })
            .where(eq(addresses.id, addressId));
        } else {
          // Create new address
          const [newAddress] = await this.db
            .insert(addresses)
            .values({
              street: updateDto.address.street,
              city: updateDto.address.city,
              state: updateDto.address.state,
              zipCode: updateDto.address.zipCode,
              country: updateDto.address.country,
              addressType: AddressType.BUSINESS,
              businessId: businessId,
            })
            .returning({ id: addresses.id });

          addressId = newAddress.id;
        }
      }

      // Update business information
      await this.db
        .update(business)
        .set({
          name: updateDto.businessName,
          email: updateDto.businessEmail,
          phone: updateDto.businessPhone,
          businessType: updateDto.businessType,
          website: updateDto.website,
          logo: logoId,
          addressId: addressId,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(business.id, businessId));

      // Log activity
      await this.activityLogService.log(
        ActivityLogName.UPDATE,
        `Updated business settings${logoFile ? ' with logo' : ''}`,
        { id: businessId, type: 'business' },
        { id: userId, type: 'user' },
        {
          businessId,
          updateData: updateDto,
          logoUploaded: !!logoFile,
        },
      );

      // Return updated business settings
      return this.getBusinessSettings(businessId, userId);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(
        'Failed to update business settings: ' + error.message,
      );
    }
  }

  /**
   * Get business reward settings
   */
  async getBusinessRewardSettings(
    businessId: string,
  ): Promise<BusinessRewardSettingsResponseDto> {
    try {
      const rewardSettings = await this.db
        .select()
        .from(businessRewardSettings)
        .where(eq(businessRewardSettings.businessId, businessId))
        .limit(1);

      if (rewardSettings.length === 0) {
        // Create default reward settings if none exist
        const [newSettings] = await this.db
          .insert(businessRewardSettings)
          .values({
            businessId,
            isEnabled: false,
            displayName: 'Customer Reward',
            amountSpendForUnitPoint: '1.00',
            minimumOrderTotalToEarnReward: '1000.00',
            redeemAmountPerUnitPoint: '0.01',
            minimumOrderTotalToRedeemPoints: '1000.00',
            rewardPointExpiryPeriod: 1,
            rewardPointExpiryPeriodUnit: 'years',
            createdBy: businessId, // Using businessId as default creator
          })
          .returning();

        return {
          id: newSettings.id,
          businessId: newSettings.businessId,
          isEnabled: newSettings.isEnabled,
          displayName: newSettings.displayName,
          amountSpendForUnitPoint: newSettings.amountSpendForUnitPoint,
          minimumOrderTotalToEarnReward:
            newSettings.minimumOrderTotalToEarnReward,
          maximumPointsPerOrder: newSettings.maximumPointsPerOrder,
          redeemAmountPerUnitPoint: newSettings.redeemAmountPerUnitPoint,
          minimumOrderTotalToRedeemPoints:
            newSettings.minimumOrderTotalToRedeemPoints,
          minimumRedeemPoint: newSettings.minimumRedeemPoint,
          maximumRedeemPointPerOrder: newSettings.maximumRedeemPointPerOrder,
          rewardPointExpiryPeriod: newSettings.rewardPointExpiryPeriod,
          rewardPointExpiryPeriodUnit:
            newSettings.rewardPointExpiryPeriodUnit as any,
          createdAt: newSettings.createdAt,
          updatedAt: newSettings.updatedAt,
        };
      }

      const settings = rewardSettings[0];
      return {
        id: settings.id,
        businessId: settings.businessId,
        isEnabled: settings.isEnabled,
        displayName: settings.displayName,
        amountSpendForUnitPoint: settings.amountSpendForUnitPoint,
        minimumOrderTotalToEarnReward: settings.minimumOrderTotalToEarnReward,
        maximumPointsPerOrder: settings.maximumPointsPerOrder,
        redeemAmountPerUnitPoint: settings.redeemAmountPerUnitPoint,
        minimumOrderTotalToRedeemPoints:
          settings.minimumOrderTotalToRedeemPoints,
        minimumRedeemPoint: settings.minimumRedeemPoint,
        maximumRedeemPointPerOrder: settings.maximumRedeemPointPerOrder,
        rewardPointExpiryPeriod: settings.rewardPointExpiryPeriod,
        rewardPointExpiryPeriodUnit:
          settings.rewardPointExpiryPeriodUnit as any,
        createdAt: settings.createdAt,
        updatedAt: settings.updatedAt,
      };
    } catch (error) {
      throw new BadRequestException(
        'Failed to get business reward settings: ' + error.message,
      );
    }
  }

  /**
   * Update business reward settings
   */
  async updateBusinessRewardSettings(
    businessId: string,
    userId: string,
    updateDto: UpdateBusinessRewardSettingsDto,
  ): Promise<BusinessRewardSettingsResponseDto> {
    try {
      // Check if reward settings exist
      const existingSettings = await this.db
        .select()
        .from(businessRewardSettings)
        .where(eq(businessRewardSettings.businessId, businessId))
        .limit(1);

      if (existingSettings.length === 0) {
        // Create new reward settings
        const [newSettings] = await this.db
          .insert(businessRewardSettings)
          .values({
            businessId,
            isEnabled: updateDto.isEnabled,
            displayName: updateDto.displayName,
            amountSpendForUnitPoint:
              updateDto.amountSpendForUnitPoint.toString(),
            minimumOrderTotalToEarnReward:
              updateDto.minimumOrderTotalToEarnReward.toString(),
            maximumPointsPerOrder: updateDto.maximumPointsPerOrder,
            redeemAmountPerUnitPoint:
              updateDto.redeemAmountPerUnitPoint.toString(),
            minimumOrderTotalToRedeemPoints:
              updateDto.minimumOrderTotalToRedeemPoints.toString(),
            minimumRedeemPoint: updateDto.minimumRedeemPoint,
            maximumRedeemPointPerOrder: updateDto.maximumRedeemPointPerOrder,
            rewardPointExpiryPeriod: updateDto.rewardPointExpiryPeriod,
            rewardPointExpiryPeriodUnit: updateDto.rewardPointExpiryPeriodUnit,
            createdBy: userId,
            updatedBy: userId,
          })
          .returning();

        // Log activity
        await this.activityLogService.log(
          ActivityLogName.CREATE,
          'Created business reward settings',
          { id: businessId, type: 'business' },
          { id: userId, type: 'user' },
          { businessId, rewardSettings: updateDto },
        );

        return {
          id: newSettings.id,
          businessId: newSettings.businessId,
          isEnabled: newSettings.isEnabled,
          displayName: newSettings.displayName,
          amountSpendForUnitPoint: newSettings.amountSpendForUnitPoint,
          minimumOrderTotalToEarnReward:
            newSettings.minimumOrderTotalToEarnReward,
          maximumPointsPerOrder: newSettings.maximumPointsPerOrder,
          redeemAmountPerUnitPoint: newSettings.redeemAmountPerUnitPoint,
          minimumOrderTotalToRedeemPoints:
            newSettings.minimumOrderTotalToRedeemPoints,
          minimumRedeemPoint: newSettings.minimumRedeemPoint,
          maximumRedeemPointPerOrder: newSettings.maximumRedeemPointPerOrder,
          rewardPointExpiryPeriod: newSettings.rewardPointExpiryPeriod,
          rewardPointExpiryPeriodUnit:
            newSettings.rewardPointExpiryPeriodUnit as any,
          createdAt: newSettings.createdAt,
          updatedAt: newSettings.updatedAt,
        };
      } else {
        // Update existing reward settings
        const [updatedSettings] = await this.db
          .update(businessRewardSettings)
          .set({
            isEnabled: updateDto.isEnabled,
            displayName: updateDto.displayName,
            amountSpendForUnitPoint:
              updateDto.amountSpendForUnitPoint.toString(),
            minimumOrderTotalToEarnReward:
              updateDto.minimumOrderTotalToEarnReward.toString(),
            maximumPointsPerOrder: updateDto.maximumPointsPerOrder,
            redeemAmountPerUnitPoint:
              updateDto.redeemAmountPerUnitPoint.toString(),
            minimumOrderTotalToRedeemPoints:
              updateDto.minimumOrderTotalToRedeemPoints.toString(),
            minimumRedeemPoint: updateDto.minimumRedeemPoint,
            maximumRedeemPointPerOrder: updateDto.maximumRedeemPointPerOrder,
            rewardPointExpiryPeriod: updateDto.rewardPointExpiryPeriod,
            rewardPointExpiryPeriodUnit: updateDto.rewardPointExpiryPeriodUnit,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(eq(businessRewardSettings.businessId, businessId))
          .returning();

        // Log activity
        await this.activityLogService.log(
          ActivityLogName.UPDATE,
          'Updated business reward settings',
          { id: businessId, type: 'business' },
          { id: userId, type: 'user' },
          { businessId, rewardSettings: updateDto },
        );

        return {
          id: updatedSettings.id,
          businessId: updatedSettings.businessId,
          isEnabled: updatedSettings.isEnabled,
          displayName: updatedSettings.displayName,
          amountSpendForUnitPoint: updatedSettings.amountSpendForUnitPoint,
          minimumOrderTotalToEarnReward:
            updatedSettings.minimumOrderTotalToEarnReward,
          maximumPointsPerOrder: updatedSettings.maximumPointsPerOrder,
          redeemAmountPerUnitPoint: updatedSettings.redeemAmountPerUnitPoint,
          minimumOrderTotalToRedeemPoints:
            updatedSettings.minimumOrderTotalToRedeemPoints,
          minimumRedeemPoint: updatedSettings.minimumRedeemPoint,
          maximumRedeemPointPerOrder:
            updatedSettings.maximumRedeemPointPerOrder,
          rewardPointExpiryPeriod: updatedSettings.rewardPointExpiryPeriod,
          rewardPointExpiryPeriodUnit:
            updatedSettings.rewardPointExpiryPeriodUnit as any,
          createdAt: updatedSettings.createdAt,
          updatedAt: updatedSettings.updatedAt,
        };
      }
    } catch (error) {
      throw new BadRequestException(
        'Failed to update business reward settings: ' + error.message,
      );
    }
  }

  /**
   * Get working hours for a business
   */
  async getWorkingHours(
    businessId: string,
    userId: string,
  ): Promise<WorkingHoursResponseDto> {
    try {
      // Check if business exists and user has access
      const businessRecord = await this.db
        .select()
        .from(business)
        .where(eq(business.id, businessId))
        .limit(1);

      if (!businessRecord.length) {
        throw new NotFoundException('Business not found');
      }

      // Get working hours for the business
      const workingHoursRecord = await this.db
        .select()
        .from(workingHours)
        .where(eq(workingHours.businessId, businessId))
        .limit(1);

      if (!workingHoursRecord.length) {
        // Create default working hours if none exist
        const [newWorkingHours] = await this.db
          .insert(workingHours)
          .values({
            businessId,
            weeklySchedule: DEFAULT_WORKING_HOURS,
            createdBy: userId,
            updatedBy: userId,
          })
          .returning();

        // Log activity
        await this.activityLogService.log(
          ActivityLogName.CREATE,
          'Created default working hours',
          { id: newWorkingHours.id, type: 'working_hours' },
          { id: userId, type: 'user' },
          { businessId },
        );

        return {
          id: newWorkingHours.id,
          businessId: newWorkingHours.businessId,
          weeklySchedule: newWorkingHours.weeklySchedule as any,
          createdAt: newWorkingHours.createdAt,
          updatedAt: newWorkingHours.updatedAt,
        };
      }

      return {
        id: workingHoursRecord[0].id,
        businessId: workingHoursRecord[0].businessId,
        weeklySchedule: workingHoursRecord[0].weeklySchedule as any,
        createdAt: workingHoursRecord[0].createdAt,
        updatedAt: workingHoursRecord[0].updatedAt,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(
        'Failed to get working hours: ' + error.message,
      );
    }
  }

  /**
   * Update working hours for a business
   */
  async updateWorkingHours(
    businessId: string,
    userId: string,
    updateDto: UpdateWorkingHoursDto,
  ): Promise<WorkingHoursResponseDto> {
    try {
      // Check if business exists and user has access
      const businessRecord = await this.db
        .select()
        .from(business)
        .where(eq(business.id, businessId))
        .limit(1);

      if (!businessRecord.length) {
        throw new NotFoundException('Business not found');
      }

      // Validate working hours data
      this.validateWorkingHours(updateDto.weeklySchedule);

      // Check if working hours record exists
      const existingWorkingHours = await this.db
        .select()
        .from(workingHours)
        .where(eq(workingHours.businessId, businessId))
        .limit(1);

      let updatedWorkingHours;

      if (existingWorkingHours.length) {
        // Update existing working hours
        [updatedWorkingHours] = await this.db
          .update(workingHours)
          .set({
            weeklySchedule: updateDto.weeklySchedule,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(eq(workingHours.businessId, businessId))
          .returning();
      } else {
        // Create new working hours record
        [updatedWorkingHours] = await this.db
          .insert(workingHours)
          .values({
            businessId,
            weeklySchedule: updateDto.weeklySchedule,
            createdBy: userId,
            updatedBy: userId,
          })
          .returning();
      }

      // Log activity
      await this.activityLogService.log(
        existingWorkingHours.length
          ? ActivityLogName.UPDATE
          : ActivityLogName.CREATE,
        `${existingWorkingHours.length ? 'Updated' : 'Created'} working hours`,
        { id: updatedWorkingHours.id, type: 'working_hours' },
        { id: userId, type: 'user' },
        { businessId, weeklySchedule: updateDto.weeklySchedule },
      );

      return {
        id: updatedWorkingHours.id,
        businessId: updatedWorkingHours.businessId,
        weeklySchedule: updatedWorkingHours.weeklySchedule,
        createdAt: updatedWorkingHours.createdAt,
        updatedAt: updatedWorkingHours.updatedAt,
      };
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        'Failed to update working hours: ' + error.message,
      );
    }
  }

  /**
   * Validate working hours data
   */
  private validateWorkingHours(weeklySchedule: any): void {
    const days = [
      'monday',
      'tuesday',
      'wednesday',
      'thursday',
      'friday',
      'saturday',
      'sunday',
    ];

    for (const day of days) {
      if (!weeklySchedule[day]) {
        throw new BadRequestException(`Missing working hours for ${day}`);
      }

      const daySchedule = weeklySchedule[day];

      // Validate working day
      if (typeof daySchedule.isWorkingDay !== 'boolean') {
        throw new BadRequestException(`Invalid isWorkingDay value for ${day}`);
      }

      // Validate working hours if it's a working day
      if (daySchedule.isWorkingDay) {
        if (
          !daySchedule.workingHours ||
          !daySchedule.workingHours.start ||
          !daySchedule.workingHours.end
        ) {
          throw new BadRequestException(`Missing working hours for ${day}`);
        }

        // Validate time format
        const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
        if (!timeRegex.test(daySchedule.workingHours.start)) {
          throw new BadRequestException(`Invalid start time format for ${day}`);
        }
        if (!timeRegex.test(daySchedule.workingHours.end)) {
          throw new BadRequestException(`Invalid end time format for ${day}`);
        }

        // Validate that end time is after start time
        const startTime = this.parseTime(daySchedule.workingHours.start);
        const endTime = this.parseTime(daySchedule.workingHours.end);
        if (endTime <= startTime) {
          throw new BadRequestException(
            `End time must be after start time for ${day}`,
          );
        }
      }

      // Validate breaks
      if (!Array.isArray(daySchedule.breaks)) {
        throw new BadRequestException(`Invalid breaks format for ${day}`);
      }

      for (const breakItem of daySchedule.breaks) {
        if (!breakItem.name || !breakItem.start || !breakItem.end) {
          throw new BadRequestException(`Invalid break data for ${day}`);
        }

        const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
        if (
          !timeRegex.test(breakItem.start) ||
          !timeRegex.test(breakItem.end)
        ) {
          throw new BadRequestException(`Invalid break time format for ${day}`);
        }

        const breakStart = this.parseTime(breakItem.start);
        const breakEnd = this.parseTime(breakItem.end);
        if (breakEnd <= breakStart) {
          throw new BadRequestException(
            `Break end time must be after start time for ${day}`,
          );
        }

        if (typeof breakItem.isPaid !== 'boolean') {
          throw new BadRequestException(
            `Invalid isPaid value for break in ${day}`,
          );
        }
      }

      // Validate total working hours
      if (
        typeof daySchedule.totalWorkingHours !== 'number' ||
        daySchedule.totalWorkingHours < 0 ||
        daySchedule.totalWorkingHours > 24
      ) {
        throw new BadRequestException(`Invalid total working hours for ${day}`);
      }
    }
  }

  /**
   * Get business accounting settings
   */
  async getBusinessAccountingSettings(
    businessId: string,
    userId: string,
  ): Promise<BusinessAccountingSettingsResponseDto> {
    try {
      // Check if business exists and user has access

      // Get existing accounting settings
      const existingSettings = await this.db
        .select()
        .from(businessAccountingSettings)
        .where(eq(businessAccountingSettings.businessId, businessId))
        .limit(1);

      if (existingSettings.length) {
        const settings = existingSettings[0];
        return {
          id: settings.id,
          businessId: settings.businessId,
          defaultTaxRateSelection: settings.defaultTaxRateSelection,
          accountingMethod: settings.accountingMethod,
          firstMonthOfFinancialYear: settings.firstMonthOfFinancialYear,
          firstMonthOfTaxYear: settings.firstMonthOfTaxYear,
          createdAt: settings.createdAt,
          updatedAt: settings.updatedAt,
        };
      } else {
        // Create default accounting settings if none exist
        const [newSettings] = await this.db
          .insert(businessAccountingSettings)
          .values({
            businessId,
            ...DEFAULT_BUSINESS_ACCOUNTING_SETTINGS,
            createdBy: userId,
            updatedBy: userId,
          })
          .returning();

        return {
          id: newSettings.id,
          businessId: newSettings.businessId,
          defaultTaxRateSelection: newSettings.defaultTaxRateSelection,
          accountingMethod: newSettings.accountingMethod,
          firstMonthOfFinancialYear: newSettings.firstMonthOfFinancialYear,
          firstMonthOfTaxYear: newSettings.firstMonthOfTaxYear,
          createdAt: newSettings.createdAt,
          updatedAt: newSettings.updatedAt,
        };
      }
    } catch (error) {
      console.error('Error getting business accounting settings:', error);
      throw error;
    }
  }

  /**
   * Update business accounting settings
   */
  async updateBusinessAccountingSettings(
    businessId: string,
    userId: string,
    updateDto: UpdateBusinessAccountingSettingsDto,
  ): Promise<BusinessAccountingSettingsResponseDto> {
    try {
      // Check if business exists and user has access

      // Get existing accounting settings
      const existingSettings = await this.db
        .select()
        .from(businessAccountingSettings)
        .where(eq(businessAccountingSettings.businessId, businessId))
        .limit(1);

      let updatedSettings;

      if (existingSettings.length) {
        // Update existing accounting settings
        [updatedSettings] = await this.db
          .update(businessAccountingSettings)
          .set({
            ...updateDto,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(eq(businessAccountingSettings.businessId, businessId))
          .returning();
      } else {
        // Create new accounting settings with provided values
        [updatedSettings] = await this.db
          .insert(businessAccountingSettings)
          .values({
            businessId,
            ...DEFAULT_BUSINESS_ACCOUNTING_SETTINGS,
            ...updateDto,
            createdBy: userId,
            updatedBy: userId,
          })
          .returning();
      }

      // Log the activity
      // await this.activityLogService.logActivity({
      //   businessId,
      //   userId,
      //   action: ActivityLogName.BUSINESS_ACCOUNTING_SETTINGS_UPDATED,
      //   description: 'Business accounting settings updated',
      //   metadata: {
      //     updatedFields: Object.keys(updateDto),
      //     newValues: updateDto,
      //   },
      // });

      return {
        id: updatedSettings.id,
        businessId: updatedSettings.businessId,
        defaultTaxRateSelection: updatedSettings.defaultTaxRateSelection,
        accountingMethod: updatedSettings.accountingMethod,
        firstMonthOfFinancialYear: updatedSettings.firstMonthOfFinancialYear,
        firstMonthOfTaxYear: updatedSettings.firstMonthOfTaxYear,
        createdAt: updatedSettings.createdAt,
        updatedAt: updatedSettings.updatedAt,
      };
    } catch (error) {
      console.error('Error updating business accounting settings:', error);
      throw error;
    }
  }

  /**
   * Get business theme settings
   */
  async getBusinessThemeSettings(
    businessId: string,
    userId: string,
  ): Promise<BusinessThemeSettingsResponseDto> {
    try {
      // Check if business exists and user has access
      const businessRecord = await this.db
        .select({
          id: business.id,
          themeSettings: business.themeSettings,
          createdAt: business.createdAt,
          updatedAt: business.updatedAt,
        })
        .from(business)
        .where(eq(business.id, businessId))
        .limit(1);

      if (!businessRecord.length) {
        throw new NotFoundException('Business not found');
      }

      const businessData = businessRecord[0];
      const themeSettings =
        businessData.themeSettings as ThemeSettingsData | null;

      // Return theme settings with default primary color if not set
      return {
        businessId: businessData.id,
        primaryColor: themeSettings?.primaryColor || '#ff8a00',
        createdAt: businessData.createdAt,
        updatedAt: businessData.updatedAt,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(
        'Failed to get business theme settings: ' + error.message,
      );
    }
  }

  /**
   * Update business theme settings
   */
  async updateBusinessThemeSettings(
    businessId: string,
    userId: string,
    updateDto: UpdateBusinessThemeSettingsDto,
  ): Promise<BusinessThemeSettingsResponseDto> {
    try {
      // Check if business exists and user has access
      const businessRecord = await this.db
        .select()
        .from(business)
        .where(eq(business.id, businessId))
        .limit(1);

      if (!businessRecord.length) {
        throw new NotFoundException('Business not found');
      }

      // Prepare theme settings data
      const themeSettingsData: ThemeSettingsData = {
        primaryColor: updateDto.primaryColor,
      };

      // Update business theme settings
      const [updatedBusiness] = await this.db
        .update(business)
        .set({
          themeSettings: themeSettingsData,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(business.id, businessId))
        .returning({
          id: business.id,
          themeSettings: business.themeSettings,
          createdAt: business.createdAt,
          updatedAt: business.updatedAt,
        });

      // Log activity
      await this.activityLogService.log(
        ActivityLogName.UPDATE,
        'Updated business theme settings',
        { id: businessId, type: 'business' },
        { id: userId, type: 'user' },
        {
          businessId,
          themeSettings: themeSettingsData,
        },
      );

      return {
        businessId: updatedBusiness.id,
        primaryColor: updateDto.primaryColor,
        createdAt: updatedBusiness.createdAt,
        updatedAt: updatedBusiness.updatedAt,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(
        'Failed to update business theme settings: ' + error.message,
      );
    }
  }

  /**
   * Get business standard units of measure settings
   */
  async getBusinessStandardUnitsSettings(
    businessId: string,
    userId: string,
  ): Promise<BusinessStandardUnitsSettingsResponseDto> {
    try {
      // Check if business exists and user has access
      const businessRecord = await this.db
        .select({
          id: business.id,
          standardUnitsOfMeasure: business.standardUnitsOfMeasure,
          updatedAt: business.updatedAt,
        })
        .from(business)
        .where(eq(business.id, businessId))
        .limit(1);

      if (!businessRecord.length) {
        throw new NotFoundException('Business not found');
      }

      const businessData = businessRecord[0];

      return {
        businessId: businessData.id,
        standardUnitsOfMeasure: businessData.standardUnitsOfMeasure || [],
        updatedAt: businessData.updatedAt,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(
        'Failed to get business standard units settings: ' + error.message,
      );
    }
  }

  /**
   * Update business standard units of measure settings
   */
  async updateBusinessStandardUnitsSettings(
    businessId: string,
    userId: string,
    updateDto: UpdateBusinessStandardUnitsSettingsDto,
  ): Promise<BusinessStandardUnitsSettingsResponseDto> {
    try {
      // Check if business exists
      const businessRecord = await this.db
        .select({ id: business.id })
        .from(business)
        .where(eq(business.id, businessId))
        .limit(1);

      if (!businessRecord.length) {
        throw new NotFoundException('Business not found');
      }

      // Update business standard units settings
      const [updatedBusiness] = await this.db
        .update(business)
        .set({
          standardUnitsOfMeasure: updateDto.standardUnitsOfMeasure || [],
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(business.id, businessId))
        .returning({
          id: business.id,
          standardUnitsOfMeasure: business.standardUnitsOfMeasure,
          updatedAt: business.updatedAt,
        });

      // Log activity
      await this.activityLogService.log(
        ActivityLogName.UPDATE,
        'Updated business standard units of measure settings',
        { id: businessId, type: 'business' },
        { id: userId, type: 'user' },
        {
          businessId,
          standardUnitsOfMeasure: updateDto.standardUnitsOfMeasure,
        },
      );

      return {
        businessId: updatedBusiness.id,
        standardUnitsOfMeasure: updatedBusiness.standardUnitsOfMeasure || [],
        updatedAt: updatedBusiness.updatedAt,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(
        'Failed to update business standard units settings: ' + error.message,
      );
    }
  }

  /**
   * Get business standard payment methods settings
   */
  async getBusinessStandardPaymentMethodsSettings(
    businessId: string,
    userId: string,
  ): Promise<BusinessStandardPaymentMethodsSettingsResponseDto> {
    try {
      // Check if business exists and user has access
      const businessRecord = await this.db
        .select({
          id: business.id,
          standardPaymentMethods: business.standardPaymentMethods,
          updatedAt: business.updatedAt,
        })
        .from(business)
        .where(eq(business.id, businessId))
        .limit(1);

      if (!businessRecord.length) {
        throw new NotFoundException('Business not found');
      }

      const businessData = businessRecord[0];

      return {
        businessId: businessData.id,
        standardPaymentMethods: businessData.standardPaymentMethods || [],
        updatedAt: businessData.updatedAt,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(
        'Failed to get business standard payment methods settings: ' +
          error.message,
      );
    }
  }

  /**
   * Update business standard payment methods settings
   */
  async updateBusinessStandardPaymentMethodsSettings(
    businessId: string,
    userId: string,
    updateDto: UpdateBusinessStandardPaymentMethodsSettingsDto,
  ): Promise<BusinessStandardPaymentMethodsSettingsResponseDto> {
    try {
      // Check if business exists
      const businessRecord = await this.db
        .select({ id: business.id })
        .from(business)
        .where(eq(business.id, businessId))
        .limit(1);

      if (!businessRecord.length) {
        throw new NotFoundException('Business not found');
      }

      // Update business standard payment methods settings
      const [updatedBusiness] = await this.db
        .update(business)
        .set({
          standardPaymentMethods: updateDto.standardPaymentMethods || [],
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(business.id, businessId))
        .returning({
          id: business.id,
          standardPaymentMethods: business.standardPaymentMethods,
          updatedAt: business.updatedAt,
        });

      // Log activity
      await this.activityLogService.log(
        ActivityLogName.UPDATE,
        'Updated business standard payment methods settings',
        { id: businessId, type: 'business' },
        { id: userId, type: 'user' },
        {
          businessId,
          standardPaymentMethods: updateDto.standardPaymentMethods,
        },
      );

      return {
        businessId: updatedBusiness.id,
        standardPaymentMethods: updatedBusiness.standardPaymentMethods || [],
        updatedAt: updatedBusiness.updatedAt,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(
        'Failed to update business standard payment methods settings: ' +
          error.message,
      );
    }
  }

  /**
   * Get business amenities settings
   */
  async getBusinessAmenitiesSettings(
    businessId: string,
    userId: string,
  ): Promise<BusinessAmenitiesSettingsResponseDto> {
    try {
      // Check if business exists and user has access
      const businessRecord = await this.db
        .select({ id: business.id })
        .from(business)
        .where(eq(business.id, businessId))
        .limit(1);

      if (!businessRecord.length) {
        throw new NotFoundException('Business not found');
      }

      // Get business amenities from the dedicated table
      const amenitiesRecord = await this.db
        .select()
        .from(businessAmenities)
        .where(eq(businessAmenities.businessId, businessId))
        .limit(1);

      if (!amenitiesRecord.length) {
        // Create default amenities record if none exists
        const [newAmenities] = await this.db
          .insert(businessAmenities)
          .values({
            businessId,
            amenities: [],
            createdBy: userId,
            updatedBy: userId,
          })
          .returning();

        return {
          businessId: newAmenities.businessId,
          businessAmenities: newAmenities.amenities || [],
          updatedAt: newAmenities.updatedAt,
        };
      }

      const amenitiesData = amenitiesRecord[0];

      return {
        businessId: amenitiesData.businessId,
        businessAmenities: amenitiesData.amenities || [],
        updatedAt: amenitiesData.updatedAt,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(
        'Failed to get business amenities settings: ' + error.message,
      );
    }
  }

  /**
   * Update business amenities settings
   */
  async updateBusinessAmenitiesSettings(
    businessId: string,
    userId: string,
    updateDto: UpdateBusinessAmenitiesSettingsDto,
  ): Promise<BusinessAmenitiesSettingsResponseDto> {
    try {
      // Check if business exists
      const businessRecord = await this.db
        .select({ id: business.id })
        .from(business)
        .where(eq(business.id, businessId))
        .limit(1);

      if (!businessRecord.length) {
        throw new NotFoundException('Business not found');
      }

      // Check if amenities record exists
      const existingAmenities = await this.db
        .select()
        .from(businessAmenities)
        .where(eq(businessAmenities.businessId, businessId))
        .limit(1);

      let updatedAmenities;

      if (existingAmenities.length) {
        // Update existing amenities record
        [updatedAmenities] = await this.db
          .update(businessAmenities)
          .set({
            amenities: updateDto.businessAmenities || [],
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(eq(businessAmenities.businessId, businessId))
          .returning();
      } else {
        // Create new amenities record
        [updatedAmenities] = await this.db
          .insert(businessAmenities)
          .values({
            businessId,
            amenities: updateDto.businessAmenities || [],
            createdBy: userId,
            updatedBy: userId,
          })
          .returning();
      }

      // Log activity
      await this.activityLogService.log(
        ActivityLogName.UPDATE,
        'Updated business amenities settings',
        { id: businessId, type: 'business' },
        { id: userId, type: 'user' },
        {
          businessId,
          businessAmenities: updateDto.businessAmenities,
        },
      );

      return {
        businessId: updatedAmenities.businessId,
        businessAmenities: updatedAmenities.amenities || [],
        updatedAt: updatedAmenities.updatedAt,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(
        'Failed to update business amenities settings: ' + error.message,
      );
    }
  }

  /**
   * Parse time string to minutes for comparison
   */
  private parseTime(timeString: string): number {
    const [hours, minutes] = timeString.split(':').map(Number);
    return hours * 60 + minutes;
  }
}
