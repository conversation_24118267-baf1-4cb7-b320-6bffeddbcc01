import {
  Controller,
  Post,
  Body,
  Get,
  Param,
  UseGuards,
  Patch,
  Request,
  UseInterceptors,
  UploadedFiles,
} from '@nestjs/common';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { BusinessService } from './business.service';
import { RegisterBusinessDto } from './dto/register-business.dto';
import {
  UpdateBusinessModulesDto,
  AddRemoveModuleDto,
  BusinessModulesResponseDto,
} from './dto/business-modules.dto';
import {
  UpdateBusinessSettingsWithLogoDto,
  BusinessSettingsResponseDto,
} from './dto/business-settings.dto';
import {
  UpdateBusinessRewardSettingsDto,
  BusinessRewardSettingsResponseDto,
} from './dto/business-reward-settings.dto';
import {
  UpdateWorkingHoursDto,
  WorkingHoursResponseDto,
} from './dto/working-hours.dto';
import {
  UpdateBusinessAccountingSettingsDto,
  BusinessAccountingSettingsResponseDto,
} from './dto/business-accounting-settings.dto';
import {
  UpdateBusinessThemeSettingsDto,
  BusinessThemeSettingsResponseDto,
} from './dto/business-theme-settings.dto';
import {
  UpdateBusinessStandardUnitsSettingsDto,
  BusinessStandardUnitsSettingsResponseDto,
} from './dto/business-standard-units-settings.dto';
import {
  UpdateBusinessStandardPaymentMethodsSettingsDto,
  BusinessStandardPaymentMethodsSettingsResponseDto,
} from './dto/business-standard-payment-methods-settings.dto';
import {
  UpdateBusinessAmenitiesSettingsDto,
  BusinessAmenitiesSettingsResponseDto,
} from './dto/business-amenities-settings.dto';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiConsumes,
  ApiBody,
} from '@nestjs/swagger';
import { SkipAuthentication } from '../auth/decorators/skip-authentication.decorator';
import { Throttle } from '@nestjs/throttler';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole, ModuleType, BusinessType } from '../shared/types';
import { AuthProfileResponseDto } from '../shared/dto/auth-profile-response.dto';
import { ActivityMetadata } from '../auth/decorators/activity-metadata.decorator';
import type { ActivityMetadata as ActivityMetadataType } from '../shared/types/activity-metadata.type';

@ApiTags('business')
@Controller('business')
export class BusinessController {
  constructor(private readonly businessService: BusinessService) {}

  @ApiOperation({ summary: 'Register new business with user' })
  @ApiResponse({
    status: 201,
    description: 'Business and user successfully registered',
    type: AuthProfileResponseDto,
  })
  @ApiResponse({
    status: 400,
    description:
      'Bad request - Invalid registration data or user/business already exists',
  })
  @ApiResponse({
    status: 429,
    description: 'Too Many Requests - Rate limit exceeded',
  })
  @Throttle({ default: { limit: 3, ttl: 60000 } }) // 3 requests per minute
  @SkipAuthentication()
  @Post('register')
  registerBusiness(
    @Body() registerBusinessDto: RegisterBusinessDto,
  ): Promise<AuthProfileResponseDto> {
    return this.businessService.registerBusiness(registerBusinessDto);
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all businesses (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'Returns all businesses',
    type: [BusinessSettingsResponseDto],
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @Get('admin/all')
  getAllBusinesses(): Promise<BusinessSettingsResponseDto[]> {
    return this.businessService.getAllBusinesses();
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get modules for a business (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'Returns business modules',
    type: [String],
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Business not found' })
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @Get(':id/modules')
  getBusinessModules(@Param('id') id: string): Promise<ModuleType[]> {
    return this.businessService.getBusinessModules(id);
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update modules for a business (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'Business modules updated successfully',
    type: BusinessModulesResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Business not found' })
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @Patch(':id/modules')
  updateBusinessModules(
    @Request() req,
    @Param('id') id: string,
    @Body() updateDto: UpdateBusinessModulesDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BusinessModulesResponseDto> {
    return this.businessService.updateBusinessModules(
      id,
      updateDto.modules,
      req.user.id,
      metadata,
    );
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Add a module to a business (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'Module added successfully',
    type: BusinessModulesResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Business not found' })
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @Patch(':id/modules/add')
  addBusinessModule(
    @Request() req,
    @Param('id') id: string,
    @Body() moduleDto: AddRemoveModuleDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BusinessModulesResponseDto> {
    return this.businessService.addBusinessModule(
      id,
      moduleDto.module,
      req.user.id,
      metadata,
    );
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Remove a module from a business (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'Module removed successfully',
    type: BusinessModulesResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Business not found' })
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @Patch(':id/modules/remove')
  removeBusinessModule(
    @Request() req,
    @Param('id') id: string,
    @Body() moduleDto: AddRemoveModuleDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BusinessModulesResponseDto> {
    return this.businessService.removeBusinessModule(
      id,
      moduleDto.module,
      req.user.id,
      metadata,
    );
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get business settings' })
  @ApiResponse({
    status: 200,
    description: 'Returns business settings',
    type: BusinessSettingsResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Business not found' })
  @Get('settings')
  getBusinessSettings(@Request() req): Promise<BusinessSettingsResponseDto> {
    return this.businessService.getBusinessSettings(
      req.user.activeBusinessId,
      req.user.id,
    );
  }

  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Update business settings with optional logo upload',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Business settings data with optional logo file',
    schema: {
      type: 'object',
      properties: {
        businessName: { type: 'string' },
        businessEmail: { type: 'string' },
        businessPhone: { type: 'string' },
        businessType: { type: 'string', enum: Object.values(BusinessType) },
        regNumber: { type: 'string' },
        website: { type: 'string' },
        address: {
          type: 'string',
          description: 'JSON string of address object',
        },
        logo: { type: 'string', format: 'binary' },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Business settings updated successfully',
    type: BusinessSettingsResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Business not found' })
  @UseInterceptors(FileFieldsInterceptor([{ name: 'logo', maxCount: 1 }]))
  @Patch('settings')
  updateBusinessSettings(
    @Request() req,
    @Body() updateDto: UpdateBusinessSettingsWithLogoDto,
    @UploadedFiles() files?: { logo?: Express.Multer.File[] },
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BusinessSettingsResponseDto> {
    // Extract logo file from the files object
    const logoFile = files?.logo?.[0];

    return this.businessService.updateBusinessSettingsWithLogo(
      req.user.activeBusinessId,
      req.user.id,
      updateDto,
      logoFile,
      metadata,
    );
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get business reward settings' })
  @ApiResponse({
    status: 200,
    description: 'Returns business reward settings',
    type: BusinessRewardSettingsResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Business not found' })
  @Get('reward-settings')
  getBusinessRewardSettings(
    @Request() req,
  ): Promise<BusinessRewardSettingsResponseDto> {
    return this.businessService.getBusinessRewardSettings(
      req.user.activeBusinessId,
    );
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update business reward settings' })
  @ApiConsumes('application/json')
  @ApiBody({ type: UpdateBusinessRewardSettingsDto })
  @ApiResponse({
    status: 200,
    description: 'Business reward settings updated successfully',
    type: BusinessRewardSettingsResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid data' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Business not found' })
  @Patch('reward-settings')
  updateBusinessRewardSettings(
    @Request() req,
    @Body() updateDto: UpdateBusinessRewardSettingsDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BusinessRewardSettingsResponseDto> {
    return this.businessService.updateBusinessRewardSettings(
      req.user.activeBusinessId,
      req.user.id,
      updateDto,
      metadata,
    );
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get business working hours' })
  @ApiResponse({
    status: 200,
    description: 'Returns business working hours',
    type: WorkingHoursResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Business not found' })
  @Get('working-hours')
  getWorkingHours(@Request() req): Promise<WorkingHoursResponseDto> {
    return this.businessService.getWorkingHours(
      req.user.activeBusinessId,
      req.user.id,
    );
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update business working hours' })
  @ApiConsumes('application/json')
  @ApiBody({
    type: UpdateWorkingHoursDto,
    description: 'Working hours configuration',
    examples: {
      'default-schedule': {
        summary: 'Default working schedule',
        description: 'Standard Monday-Friday 9-5 schedule with lunch break',
        value: {
          weeklySchedule: {
            monday: {
              isWorkingDay: true,
              workingHours: { start: '09:00', end: '17:00' },
              breaks: [
                {
                  name: 'Lunch Break',
                  start: '12:00',
                  end: '13:00',
                  isPaid: false,
                },
              ],
              totalWorkingHours: 7.0,
            },
            tuesday: {
              isWorkingDay: true,
              workingHours: { start: '09:00', end: '17:00' },
              breaks: [
                {
                  name: 'Lunch Break',
                  start: '12:00',
                  end: '13:00',
                  isPaid: false,
                },
              ],
              totalWorkingHours: 7.0,
            },
            wednesday: {
              isWorkingDay: true,
              workingHours: { start: '09:00', end: '17:00' },
              breaks: [
                {
                  name: 'Lunch Break',
                  start: '12:00',
                  end: '13:00',
                  isPaid: false,
                },
              ],
              totalWorkingHours: 7.0,
            },
            thursday: {
              isWorkingDay: true,
              workingHours: { start: '09:00', end: '17:00' },
              breaks: [
                {
                  name: 'Lunch Break',
                  start: '12:00',
                  end: '13:00',
                  isPaid: false,
                },
              ],
              totalWorkingHours: 7.0,
            },
            friday: {
              isWorkingDay: true,
              workingHours: { start: '09:00', end: '17:00' },
              breaks: [
                {
                  name: 'Lunch Break',
                  start: '12:00',
                  end: '13:00',
                  isPaid: false,
                },
              ],
              totalWorkingHours: 7.0,
            },
            saturday: {
              isWorkingDay: false,
              breaks: [],
              totalWorkingHours: 0,
            },
            sunday: {
              isWorkingDay: false,
              breaks: [],
              totalWorkingHours: 0,
            },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Working hours updated successfully',
    type: WorkingHoursResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid data' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Business not found' })
  @Patch('working-hours')
  updateWorkingHours(
    @Request() req,
    @Body() updateDto: UpdateWorkingHoursDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<WorkingHoursResponseDto> {
    return this.businessService.updateWorkingHours(
      req.user.activeBusinessId,
      req.user.id,
      updateDto,
      metadata,
    );
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get business accounting settings' })
  @ApiResponse({
    status: 200,
    description: 'Returns business accounting settings',
    type: BusinessAccountingSettingsResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Business not found' })
  @Get('accounting-settings')
  getBusinessAccountingSettings(
    @Request() req,
  ): Promise<BusinessAccountingSettingsResponseDto> {
    return this.businessService.getBusinessAccountingSettings(
      req.user.activeBusinessId,
      req.user.id,
    );
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update business accounting settings' })
  @ApiBody({
    description: 'Accounting settings data',
    type: UpdateBusinessAccountingSettingsDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Accounting settings updated successfully',
    type: BusinessAccountingSettingsResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid data' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Business not found' })
  @Patch('accounting-settings')
  updateBusinessAccountingSettings(
    @Request() req,
    @Body() updateDto: UpdateBusinessAccountingSettingsDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BusinessAccountingSettingsResponseDto> {
    return this.businessService.updateBusinessAccountingSettings(
      req.user.activeBusinessId,
      req.user.id,
      updateDto,
      metadata,
    );
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get business theme settings' })
  @ApiResponse({
    status: 200,
    description: 'Returns business theme settings',
    type: BusinessThemeSettingsResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Business not found' })
  @Get('theme-settings')
  getBusinessThemeSettings(
    @Request() req,
  ): Promise<BusinessThemeSettingsResponseDto> {
    return this.businessService.getBusinessThemeSettings(
      req.user.activeBusinessId,
      req.user.id,
    );
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update business theme settings' })
  @ApiBody({
    description: 'Theme settings data',
    type: UpdateBusinessThemeSettingsDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Theme settings updated successfully',
    type: BusinessThemeSettingsResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid data' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Business not found' })
  @Patch('theme-settings')
  updateBusinessThemeSettings(
    @Request() req,
    @Body() updateDto: UpdateBusinessThemeSettingsDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BusinessThemeSettingsResponseDto> {
    return this.businessService.updateBusinessThemeSettings(
      req.user.activeBusinessId,
      req.user.id,
      updateDto,
      metadata,
    );
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get business standard units of measure settings' })
  @ApiResponse({
    status: 200,
    description: 'Returns business standard units of measure settings',
    type: BusinessStandardUnitsSettingsResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Business not found' })
  @Get('standard-units-settings')
  getBusinessStandardUnitsSettings(
    @Request() req,
  ): Promise<BusinessStandardUnitsSettingsResponseDto> {
    return this.businessService.getBusinessStandardUnitsSettings(
      req.user.activeBusinessId,
      req.user.id,
    );
  }

  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Update business standard units of measure settings',
  })
  @ApiBody({
    description: 'Standard units of measure settings data',
    type: UpdateBusinessStandardUnitsSettingsDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Standard units of measure settings updated successfully',
    type: BusinessStandardUnitsSettingsResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid data' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Business not found' })
  @Patch('standard-units-settings')
  updateBusinessStandardUnitsSettings(
    @Request() req,
    @Body() updateDto: UpdateBusinessStandardUnitsSettingsDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BusinessStandardUnitsSettingsResponseDto> {
    return this.businessService.updateBusinessStandardUnitsSettings(
      req.user.activeBusinessId,
      req.user.id,
      updateDto,
      metadata,
    );
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get business standard payment methods settings' })
  @ApiResponse({
    status: 200,
    description: 'Returns business standard payment methods settings',
    type: BusinessStandardPaymentMethodsSettingsResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Business not found' })
  @Get('standard-payment-methods-settings')
  getBusinessStandardPaymentMethodsSettings(
    @Request() req,
  ): Promise<BusinessStandardPaymentMethodsSettingsResponseDto> {
    return this.businessService.getBusinessStandardPaymentMethodsSettings(
      req.user.activeBusinessId,
      req.user.id,
    );
  }

  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Update business standard payment methods settings',
  })
  @ApiBody({
    description: 'Standard payment methods settings data',
    type: UpdateBusinessStandardPaymentMethodsSettingsDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Standard payment methods settings updated successfully',
    type: BusinessStandardPaymentMethodsSettingsResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid data' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Business not found' })
  @Patch('standard-payment-methods-settings')
  updateBusinessStandardPaymentMethodsSettings(
    @Request() req,
    @Body() updateDto: UpdateBusinessStandardPaymentMethodsSettingsDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BusinessStandardPaymentMethodsSettingsResponseDto> {
    return this.businessService.updateBusinessStandardPaymentMethodsSettings(
      req.user.activeBusinessId,
      req.user.id,
      updateDto,
      metadata,
    );
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get business amenities settings' })
  @ApiResponse({
    status: 200,
    description: 'Returns business amenities settings',
    type: BusinessAmenitiesSettingsResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Business not found' })
  @Get('amenities-settings')
  getBusinessAmenitiesSettings(
    @Request() req,
  ): Promise<BusinessAmenitiesSettingsResponseDto> {
    return this.businessService.getBusinessAmenitiesSettings(
      req.user.activeBusinessId,
      req.user.id,
    );
  }

  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Update business amenities settings',
  })
  @ApiBody({
    description: 'Amenities settings data',
    type: UpdateBusinessAmenitiesSettingsDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Amenities settings updated successfully',
    type: BusinessAmenitiesSettingsResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid data' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Business not found' })
  @Patch('amenities-settings')
  updateBusinessAmenitiesSettings(
    @Request() req,
    @Body() updateDto: UpdateBusinessAmenitiesSettingsDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BusinessAmenitiesSettingsResponseDto> {
    return this.businessService.updateBusinessAmenitiesSettings(
      req.user.activeBusinessId,
      req.user.id,
      updateDto,
      metadata,
    );
  }
}
