import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  Query,
  UseGuards,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { CommentsService } from './comments.service';
import { CreateCommentDto } from './dto/create-comment.dto';
import { UpdateCommentDto } from './dto/update-comment.dto';
import { CommentDto } from './dto/comment.dto';
import { CommentIdResponseDto } from './dto/comment-id-response.dto';
import { PaginatedCommentsResponseDto } from './dto/paginated-comments-response.dto';
import { DeleteCommentResponseDto } from './dto/delete-comment-response.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { Permission } from '../shared/types';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiConsumes,
  ApiBody,
} from '@nestjs/swagger';
import { RequirePermissions } from '@app/auth/decorators/permissions.decorator';
import { ActivityMetadata } from '../auth/decorators/activity-metadata.decorator';
import type { ActivityMetadata as ActivityMetadataType } from '../shared/types/activity-metadata.type';

@ApiTags('Comments')
@Controller('comments')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class CommentsController {
  constructor(private readonly commentsService: CommentsService) {}

  @Post()
  @RequirePermissions(Permission.COMMENT_CREATE)
  @UseInterceptors(FileInterceptor('attachment'))
  @ApiOperation({ summary: 'Create a new comment' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Comment data with optional attachment',
    schema: {
      type: 'object',
      properties: {
        referenceId: {
          type: 'string',
          format: 'uuid',
          description: 'Reference ID of the entity this comment belongs to',
        },
        parentId: {
          type: 'string',
          format: 'uuid',
          description: 'Parent comment ID for replies',
          nullable: true,
        },
        content: {
          type: 'string',
          description: 'Comment content',
          maxLength: 2000,
        },
        authorId: {
          type: 'string',
          format: 'uuid',
          description: 'Author ID (staff member)',
          nullable: true,
        },
        attachment: {
          type: 'string',
          format: 'binary',
          description: 'Optional file attachment',
        },
      },
      required: ['referenceId', 'content'],
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Comment created successfully',
    type: CommentIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  create(
    @Request() req,
    @Body() createCommentDto: CreateCommentDto,
    @UploadedFile() attachment?: Express.Multer.File,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<CommentIdResponseDto> {
    return this.commentsService.create(
      req.user.id,
      req.user.activeBusinessId,
      createCommentDto,
      attachment,
      metadata,
    );
  }

  @Get()
  @RequirePermissions(Permission.COMMENT_READ)
  @ApiOperation({
    summary: 'Get all comments for the active business',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'referenceId',
    description: 'Filter by reference ID',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'authorId',
    description: 'Filter by author ID',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'parentId',
    description: 'Filter by parent comment ID',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'content',
    description: 'Search in comment content',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'status',
    description: 'Filter by status',
    required: false,
    enum: ['active', 'inactive'],
  })
  @ApiResponse({
    status: 200,
    description: 'Comments retrieved successfully',
    type: PaginatedCommentsResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Request() req,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('referenceId') referenceId?: string,
    @Query('authorId') authorId?: string,
    @Query('parentId') parentId?: string,
    @Query('content') content?: string,
    @Query('status') status?: string,
  ): Promise<PaginatedCommentsResponseDto> {
    const pageNum = page ? parseInt(page, 10) : 1;
    const limitNum = limit ? parseInt(limit, 10) : 10;

    return this.commentsService.findAllOptimized(
      req.user.id,
      req.user.activeBusinessId,
      pageNum,
      limitNum,
      referenceId,
      authorId,
      parentId,
      content,
      status,
    );
  }

  @Get(':id')
  @RequirePermissions(Permission.COMMENT_READ)
  @ApiOperation({ summary: 'Get a comment by ID' })
  @ApiResponse({
    status: 200,
    description: 'Comment retrieved successfully',
    type: CommentDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Comment not found',
  })
  findOne(@Request() req, @Param('id') id: string): Promise<CommentDto> {
    return this.commentsService.findOne(
      req.user.id,
      req.user.activeBusinessId,
      id,
    );
  }

  @Patch(':id')
  @RequirePermissions(Permission.COMMENT_UPDATE)
  @UseInterceptors(FileInterceptor('attachment'))
  @ApiOperation({ summary: 'Update a comment' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Comment update data with optional attachment',
    schema: {
      type: 'object',
      properties: {
        content: {
          type: 'string',
          description: 'Comment content',
          maxLength: 2000,
        },
        attachment: {
          type: 'string',
          format: 'binary',
          description: 'Optional file attachment',
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Comment updated successfully',
    type: CommentIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Comment not found',
  })
  update(
    @Request() req,
    @Param('id') id: string,
    @Body() updateCommentDto: UpdateCommentDto,
    @UploadedFile() attachment?: Express.Multer.File,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<CommentIdResponseDto> {
    return this.commentsService.update(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateCommentDto,
      attachment,
      metadata,
    );
  }

  @Delete(':id')
  @RequirePermissions(Permission.COMMENT_DELETE)
  @ApiOperation({ summary: 'Delete a comment' })
  @ApiResponse({
    status: 200,
    description: 'Comment deleted successfully',
    type: DeleteCommentResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Comment not found',
  })
  remove(
    @Request() req,
    @Param('id') id: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<DeleteCommentResponseDto> {
    return this.commentsService.remove(
      req.user.id,
      req.user.activeBusinessId,
      id,
      metadata,
    );
  }
}
