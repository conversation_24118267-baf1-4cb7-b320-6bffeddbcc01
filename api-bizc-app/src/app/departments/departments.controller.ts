import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  Query,
  UseGuards,
} from '@nestjs/common';
import { DepartmentsService } from './departments.service';
import { CreateDepartmentDto } from './dto/create-department.dto';
import { UpdateDepartmentDto } from './dto/update-department.dto';
import { DepartmentDto } from './dto/department.dto';
import { DepartmentSlimDto } from './dto/department-slim.dto';
import { DepartmentIdResponseDto } from './dto/department-id-response.dto';
import { BulkDepartmentIdsResponseDto } from './dto/bulk-department-ids-response.dto';
import { BulkCreateDepartmentDto } from './dto/bulk-create-department.dto';
import { DeleteDepartmentResponseDto } from './dto/delete-department-response.dto';
import { BulkDeleteDepartmentDto } from './dto/bulk-delete-department.dto';
import { BulkDeleteDepartmentResponseDto } from './dto/bulk-delete-department-response.dto';
import { PaginatedDepartmentsResponseDto } from './dto/paginated-departments-response.dto';
import {
  BulkUpdateDepartmentHierarchyDto,
  BulkUpdateDepartmentHierarchyResponseDto,
} from './dto/bulk-update-department-hierarchy.dto';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiParam,
  ApiBearerAuth,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { ActivityMetadata } from '../auth/decorators/activity-metadata.decorator';
import { Permission } from '../shared/types/permission.enum';
import type { ActivityMetadata as ActivityMetadataType } from '../shared/types/activity-metadata.type';

@ApiTags('departments')
@Controller('departments')
@UseGuards(PermissionsGuard)
export class DepartmentsController {
  constructor(private readonly departmentsService: DepartmentsService) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.DEPARTMENT_CREATE)
  @ApiOperation({ summary: 'Create a new department' })
  @ApiBody({
    description: 'Department creation data',
    type: CreateDepartmentDto,
  })
  @ApiResponse({
    status: 201,
    description: 'The department has been successfully created',
    type: DepartmentIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Department with same name already exists',
  })
  create(
    @Request() req,
    @Body() createDepartmentDto: CreateDepartmentDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<DepartmentIdResponseDto> {
    return this.departmentsService.createAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      createDepartmentDto,
      metadata,
    );
  }

  @Post('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.DEPARTMENT_CREATE)
  @ApiOperation({ summary: 'Bulk create departments' })
  @ApiBody({
    description: 'Bulk department creation data',
    type: BulkCreateDepartmentDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Departments have been successfully created',
    type: BulkDepartmentIdsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  bulkCreate(
    @Request() req,
    @Body() bulkCreateDepartmentDto: BulkCreateDepartmentDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkDepartmentIdsResponseDto> {
    return this.departmentsService.bulkCreateAndReturnIds(
      req.user.id,
      req.user.activeBusinessId,
      bulkCreateDepartmentDto.departments,
      metadata,
    );
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.DEPARTMENT_READ)
  @ApiOperation({
    summary: 'Get all departments with filtering and pagination',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page',
    example: 10,
  })
  @ApiQuery({
    name: 'from',
    required: false,
    type: String,
    description: 'Filter from date (ISO string)',
    example: '2023-01-01T00:00:00Z',
  })
  @ApiQuery({
    name: 'to',
    required: false,
    type: String,
    description: 'Filter to date (ISO string)',
    example: '2023-12-31T23:59:59Z',
  })
  @ApiQuery({
    name: 'name',
    required: false,
    type: String,
    description: 'Filter by department name (partial match)',
    example: 'Human',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    type: String,
    description: 'Filter by status (comma-separated for multiple)',
    example: 'active,inactive',
  })
  @ApiQuery({
    name: 'filters',
    required: false,
    type: String,
    description: 'Advanced filters as JSON string',
    example:
      '[{"id":"name","value":"HR","operator":"iLike"},{"id":"status","value":"active","operator":"eq"}]',
  })
  @ApiQuery({
    name: 'joinOperator',
    required: false,
    enum: ['and', 'or'],
    description: 'Join operator for advanced filters',
    example: 'and',
  })
  @ApiQuery({
    name: 'sort',
    required: false,
    type: String,
    description: 'Sort configuration as JSON string',
    example: '[{"id":"name","desc":false},{"id":"updatedAt","desc":true}]',
  })
  @ApiResponse({
    status: 200,
    description: 'Departments retrieved successfully',
    type: PaginatedDepartmentsResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Request() req,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('name') name?: string,
    @Query('status') status?: string,
    @Query('filters') filters?: string,
    @Query('joinOperator') joinOperator?: 'and' | 'or',
    @Query('sort') sort?: string,
  ): Promise<PaginatedDepartmentsResponseDto> {
    return this.departmentsService.findAllOptimized(
      req.user.id,
      req.user.activeBusinessId,
      page,
      limit,
      from,
      to,
      name,
      status,
      filters,
      joinOperator,
      sort,
    );
  }

  @Get('check-name-availability')
  @ApiBearerAuth()
  @RequirePermissions(Permission.DEPARTMENT_READ)
  @ApiOperation({ summary: 'Check if department name is available' })
  @ApiQuery({
    name: 'name',
    required: true,
    type: String,
    description: 'Department name to check',
    example: 'Human Resources',
  })
  @ApiResponse({
    status: 200,
    description: 'Name availability checked successfully',
    schema: {
      type: 'object',
      properties: {
        available: {
          type: 'boolean',
          description: 'Whether the name is available',
          example: true,
        },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkNameAvailability(
    @Request() req,
    @Query('name') name: string,
  ): Promise<{ available: boolean }> {
    return this.departmentsService.checkNameAvailability(
      req.user.id,
      req.user.activeBusinessId,
      name,
    );
  }

  @Get('slim')
  @ApiBearerAuth()
  @RequirePermissions(Permission.DEPARTMENT_READ)
  @ApiOperation({ summary: 'Get departments in slim format for dropdowns' })
  @ApiResponse({
    status: 200,
    description: 'Slim departments retrieved successfully',
    type: [DepartmentSlimDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllSlim(@Request() req): Promise<DepartmentSlimDto[]> {
    return this.departmentsService.findAllSlim(
      req.user.id,
      req.user.activeBusinessId,
    );
  }

  @Get('hierarchy')
  @ApiBearerAuth()
  @RequirePermissions(Permission.DEPARTMENT_READ)
  @ApiOperation({ summary: 'Get department hierarchy data' })
  @ApiResponse({
    status: 200,
    description: 'Department hierarchy retrieved successfully',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string', description: 'Department ID' },
          name: { type: 'string', description: 'Department name' },
          parentId: {
            type: 'string',
            nullable: true,
            description: 'Parent department ID',
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllHierarchy(@Request() req) {
    return this.departmentsService.findAllHierarchy(
      req.user.id,
      req.user.activeBusinessId,
    );
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.DEPARTMENT_READ)
  @ApiOperation({ summary: 'Get a specific department by ID' })
  @ApiParam({
    name: 'id',
    description: 'Department ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiResponse({
    status: 200,
    description: 'Department retrieved successfully',
    type: DepartmentDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Department not found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findOne(@Request() req, @Param('id') id: string): Promise<DepartmentDto> {
    return this.departmentsService.findOne(req.user.id, id);
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.DEPARTMENT_UPDATE)
  @ApiOperation({ summary: 'Update a specific department' })
  @ApiParam({
    name: 'id',
    description: 'Department ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiBody({
    description: 'Department update data',
    type: UpdateDepartmentDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Department updated successfully',
    type: DepartmentIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Department not found',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Department with same name already exists',
  })
  update(
    @Request() req,
    @Param('id') id: string,
    @Body() updateDepartmentDto: UpdateDepartmentDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<DepartmentIdResponseDto> {
    return this.departmentsService.updateAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateDepartmentDto,
      metadata,
    );
  }

  @Delete('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.DEPARTMENT_DELETE)
  @ApiOperation({ summary: 'Bulk delete departments' })
  @ApiBody({
    description: 'Bulk department deletion data',
    type: BulkDeleteDepartmentDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Departments deleted successfully',
    type: BulkDeleteDepartmentResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or departments have children',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'No valid departments found to delete',
  })
  async bulkDelete(
    @Request() req,
    @Body() bulkDeleteDepartmentDto: BulkDeleteDepartmentDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkDeleteDepartmentResponseDto> {
    return this.departmentsService.bulkDelete(
      req.user.id,
      req.user.activeBusinessId,
      bulkDeleteDepartmentDto.departmentIds,
      metadata,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.DEPARTMENT_DELETE)
  @ApiOperation({ summary: 'Delete a specific department' })
  @ApiParam({
    name: 'id',
    description: 'Department ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiResponse({
    status: 200,
    description: 'Department deleted successfully',
    type: DeleteDepartmentResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Department has children',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Department not found',
  })
  remove(
    @Request() req,
    @Param('id') id: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<DeleteDepartmentResponseDto> {
    return this.departmentsService.remove(
      req.user.id,
      req.user.activeBusinessId,
      id,
      metadata,
    );
  }

  @Patch('hierarchy/update')
  @ApiBearerAuth()
  @RequirePermissions(Permission.DEPARTMENT_UPDATE)
  @ApiOperation({ summary: 'Bulk update department hierarchy' })
  @ApiBody({
    description: 'Department hierarchy updates',
    type: BulkUpdateDepartmentHierarchyDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Department hierarchy updated successfully',
    type: BulkUpdateDepartmentHierarchyResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or circular references',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async updateHierarchy(
    @Request() req,
    @Body() bulkUpdateHierarchyDto: BulkUpdateDepartmentHierarchyDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkUpdateDepartmentHierarchyResponseDto> {
    return this.departmentsService.bulkUpdateDepartmentHierarchy(
      req.user.id,
      req.user.activeBusinessId,
      bulkUpdateHierarchyDto.updates,
      metadata,
    );
  }
}
