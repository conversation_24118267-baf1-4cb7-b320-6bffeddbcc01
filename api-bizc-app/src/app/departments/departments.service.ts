import {
  Injectable,
  Inject,
  NotFoundException,
  BadRequestException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { departments } from '../drizzle/schema/departments.schema';
import { staffMembers } from '../drizzle/schema/staff.schema';
import { CreateDepartmentDto } from './dto/create-department.dto';
import { UpdateDepartmentDto } from './dto/update-department.dto';
import { DepartmentDto } from './dto/department.dto';
import {
  and,
  eq,
  isNull,
  ilike,
  sql,
  gte,
  lte,
  desc,
  asc,
  or,
  inArray,
} from 'drizzle-orm';
import { alias } from 'drizzle-orm/pg-core';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { DepartmentStatus } from '../shared/types';
import { ActivityMetadata } from '../shared/types/activity-metadata.type';
import {
  EntityType,
  ActivityType,
  ActivitySource,
  ExecutionStrategy,
} from '../shared/types/activity.enum';
import { DepartmentSlimDto } from './dto/department-slim.dto';
import { DepartmentListDto } from './dto/department-list.dto';

@Injectable()
export class DepartmentsService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createDepartmentDto: CreateDepartmentDto,
    metadata?: ActivityMetadata,
  ): Promise<DepartmentDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a department with the same name already exists for this business
      const existingDepartment = await this.db
        .select()
        .from(departments)
        .where(
          and(
            eq(departments.businessId, businessId),
            ilike(departments.name, createDepartmentDto.name),
            eq(departments.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingDepartment) {
        throw new ConflictException(
          `A department with the name '${createDepartmentDto.name}' already exists for this business`,
        );
      }

      // Create the department
      const [newDepartment] = await this.db
        .insert(departments)
        .values({
          businessId,
          name: createDepartmentDto.name,
          parentId: createDepartmentDto.parentId,
          description: createDepartmentDto.description,
          status: createDepartmentDto.status || DepartmentStatus.ACTIVE,
          createdBy: userId,
        })
        .returning();

      // Log the activity
      await this.activityLogService.logCreate(
        newDepartment.id,
        EntityType.DEPARTMENT,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      // Map to DTO
      return this.mapToDepartmentDto(newDepartment);
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create department: ${error.message}`,
      );
    }
  }

  async findAllOptimized(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    name?: string,
    status?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: DepartmentListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const offset = (page - 1) * limit;
      const baseConditions = [
        eq(departments.businessId, businessId),
        eq(departments.isDeleted, false),
      ];

      // Add simple filter conditions
      if (from) {
        baseConditions.push(gte(departments.createdAt, new Date(from)));
      }
      if (to) {
        baseConditions.push(lte(departments.createdAt, new Date(to)));
      }
      if (name) {
        baseConditions.push(ilike(departments.name, `%${name}%`));
      }
      if (status) {
        const statusArray = status.split(',').map((s) => s.trim());
        if (statusArray.length === 1) {
          baseConditions.push(
            eq(departments.status, statusArray[0] as DepartmentStatus),
          );
        } else {
          baseConditions.push(
            or(
              ...statusArray.map((s) =>
                eq(departments.status, s as DepartmentStatus),
              ),
            ),
          );
        }
      }

      // Handle advanced filters
      let advancedFilters = [];
      if (filters) {
        try {
          const parsedFilters = JSON.parse(filters);
          if (Array.isArray(parsedFilters)) {
            advancedFilters = parsedFilters
              .map((filter) => {
                const { id, value, operator } = filter;

                switch (id) {
                  case 'name':
                    if (operator === 'iLike')
                      return ilike(departments.name, `%${value}%`);
                    if (operator === 'notILike')
                      return sql`NOT ${ilike(departments.name, `%${value}%`)}`;
                    if (operator === 'eq') return eq(departments.name, value);
                    if (operator === 'ne')
                      return sql`${departments.name} != ${value}`;
                    if (operator === 'isEmpty') return eq(departments.name, '');
                    if (operator === 'isNotEmpty')
                      return sql`${departments.name} != ''`;
                    break;
                  case 'status':
                    if (operator === 'eq')
                      return eq(departments.status, value as DepartmentStatus);
                    if (operator === 'ne')
                      return sql`${departments.status} != ${value}`;
                    break;
                  case 'description':
                    if (operator === 'iLike')
                      return ilike(departments.description, `%${value}%`);
                    if (operator === 'notILike')
                      return sql`NOT ${ilike(departments.description, `%${value}%`)}`;
                    if (operator === 'isEmpty')
                      return isNull(departments.description);
                    if (operator === 'isNotEmpty')
                      return sql`${departments.description} IS NOT NULL`;
                    break;
                }
                return null;
              })
              .filter(Boolean);
          }
        } catch {
          // Invalid JSON, ignore advanced filters
        }
      }

      // Combine conditions
      let whereCondition;
      if (advancedFilters.length > 0) {
        const combinedAdvanced =
          joinOperator === 'or'
            ? or(...advancedFilters)
            : and(...advancedFilters);
        whereCondition = and(...baseConditions, combinedAdvanced);
      } else {
        whereCondition = and(...baseConditions);
      }

      // Handle sorting
      let orderBy = [desc(departments.updatedAt)]; // default sort
      if (sort) {
        try {
          const parsedSort = JSON.parse(sort);
          if (Array.isArray(parsedSort) && parsedSort.length > 0) {
            orderBy = parsedSort.map((sortItem) => {
              const { id, desc: isDesc } = sortItem;
              let column;
              switch (id) {
                case 'name':
                  column = departments.name;
                  break;
                case 'createdAt':
                  column = departments.createdAt;
                  break;
                case 'updatedAt':
                  column = departments.updatedAt;
                  break;
                case 'status':
                  column = departments.status;
                  break;
                default:
                  column = departments.updatedAt;
              }
              return isDesc ? desc(column) : asc(column);
            });
          }
        } catch {
          // Invalid JSON, use default sort
        }
      }

      // Get total count
      const totalResult = await this.db
        .select({ count: sql<number>`count(*)` })
        .from(departments)
        .where(whereCondition);

      const total = Number(totalResult[0].count);
      const totalPages = Math.ceil(total / limit);

      // Get departments with parent information
      const parentDepartments = alias(departments, 'parent_departments');
      const departmentsList = await this.db
        .select({
          id: departments.id,
          name: departments.name,
          description: departments.description,
          status: departments.status,
          parentId: departments.parentId,
          parentName: parentDepartments.name,
          businessId: departments.businessId,
          createdAt: departments.createdAt,
          updatedAt: departments.updatedAt,
        })
        .from(departments)
        .leftJoin(
          parentDepartments as any,
          eq(departments.parentId, parentDepartments.id),
        )
        .where(whereCondition)
        .orderBy(...orderBy)
        .limit(limit)
        .offset(offset);

      const mappedDepartments = await Promise.all(
        departmentsList.map((dept) =>
          this.mapToDepartmentListDto(dept, dept.parentName),
        ),
      );

      return {
        data: mappedDepartments,
        meta: { total, page, totalPages },
      };
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to fetch departments: ${error.message}`,
      );
    }
  }

  async findOne(userId: string, id: string): Promise<DepartmentDto> {
    try {
      const department = await this.db
        .select()
        .from(departments)
        .where(and(eq(departments.id, id), eq(departments.isDeleted, false)))
        .then((results) => results[0]);

      if (!department) {
        throw new NotFoundException('Department not found');
      }

      return this.mapToDepartmentDto(department);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to fetch department: ${error.message}`,
      );
    }
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateDepartmentDto: UpdateDepartmentDto,
    metadata?: ActivityMetadata,
  ): Promise<DepartmentDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if department exists
      const existingDepartment = await this.db
        .select()
        .from(departments)
        .where(
          and(
            eq(departments.id, id),
            eq(departments.businessId, businessId),
            eq(departments.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingDepartment) {
        throw new NotFoundException('Department not found');
      }

      // Check for name conflicts if name is being updated
      if (
        updateDepartmentDto.name &&
        updateDepartmentDto.name !== existingDepartment.name
      ) {
        const conflictingDepartment = await this.db
          .select()
          .from(departments)
          .where(
            and(
              eq(departments.businessId, businessId),
              ilike(departments.name, updateDepartmentDto.name),
              sql`${departments.id} != ${id}`,
              eq(departments.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (conflictingDepartment) {
          throw new ConflictException(
            `A department with the name '${updateDepartmentDto.name}' already exists for this business`,
          );
        }
      }

      // Handle parent department if provided
      if (updateDepartmentDto.parentId) {
        const parentDepartment = await this.db
          .select()
          .from(departments)
          .where(
            and(
              eq(departments.id, updateDepartmentDto.parentId),
              eq(departments.businessId, businessId),
              eq(departments.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!parentDepartment) {
          throw new BadRequestException('Parent department not found');
        }

        // Check for circular reference
        if (updateDepartmentDto.parentId === id) {
          throw new BadRequestException(
            'A department cannot be its own parent',
          );
        }

        // Check if the new parent would create a circular reference
        const wouldCreateCircle = await this.checkCircularReference(
          updateDepartmentDto.parentId,
          id,
        );
        if (wouldCreateCircle) {
          throw new BadRequestException(
            'This update would create a circular reference in the department hierarchy',
          );
        }
      }

      // Update the department
      const [updatedDepartment] = await this.db
        .update(departments)
        .set({
          ...updateDepartmentDto,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(departments.id, id))
        .returning();

      // Log the activity
      await this.activityLogService.logUpdate(
        updatedDepartment.id,
        EntityType.DEPARTMENT,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return this.mapToDepartmentDto(updatedDepartment);
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof ConflictException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update department: ${error.message}`,
      );
    }
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ success: boolean; message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if department exists
      const existingDepartment = await this.db
        .select()
        .from(departments)
        .where(
          and(
            eq(departments.id, id),
            eq(departments.businessId, businessId),
            eq(departments.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingDepartment) {
        throw new NotFoundException('Department not found');
      }

      // Check if department has children
      const childrenDepartments = await this.db
        .select()
        .from(departments)
        .where(
          and(
            eq(departments.parentId, id),
            eq(departments.businessId, businessId),
            eq(departments.isDeleted, false),
          ),
        );

      if (childrenDepartments.length > 0) {
        throw new BadRequestException(
          'Cannot delete department that has child departments',
        );
      }

      // Soft delete the department
      await this.db
        .update(departments)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(departments.id, id));

      // Log the activity
      await this.activityLogService.logDelete(
        existingDepartment.id,
        EntityType.DEPARTMENT,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        success: true,
        message: 'Department has been successfully deleted',
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to delete department: ${error.message}`,
      );
    }
  }

  async checkNameAvailability(
    userId: string,
    businessId: string | null,
    name: string,
  ): Promise<{ available: boolean }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const existingDepartment = await this.db
        .select()
        .from(departments)
        .where(
          and(
            eq(departments.businessId, businessId),
            ilike(departments.name, name),
            eq(departments.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      return { available: !existingDepartment };
    } catch (error) {
      throw new BadRequestException(
        `Failed to check name availability: ${error.message}`,
      );
    }
  }

  async findAllSlim(
    userId: string,
    businessId: string | null,
  ): Promise<DepartmentSlimDto[]> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const departmentsList = await this.db
        .select({
          id: departments.id,
          name: departments.name,
        })
        .from(departments)
        .where(
          and(
            eq(departments.businessId, businessId),
            eq(departments.status, DepartmentStatus.ACTIVE),
            eq(departments.isDeleted, false),
          ),
        )
        .orderBy(asc(departments.name));

      return departmentsList.map((dept) => this.mapToDepartmentSlimDto(dept));
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to fetch departments: ${error.message}`,
      );
    }
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createDepartmentDto: CreateDepartmentDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    const department = await this.create(
      userId,
      businessId,
      createDepartmentDto,
      metadata,
    );
    return { id: department.id };
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateDepartmentDto: UpdateDepartmentDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    const department = await this.update(
      userId,
      businessId,
      id,
      updateDepartmentDto,
      metadata,
    );
    return { id: department.id };
  }

  async bulkCreate(
    userId: string,
    businessId: string | null,
    createDepartmentsDto: CreateDepartmentDto[],
    metadata?: ActivityMetadata,
  ): Promise<DepartmentDto[]> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!createDepartmentsDto || createDepartmentsDto.length === 0) {
        throw new BadRequestException('No departments provided');
      }

      const createdDepartments: DepartmentDto[] = [];

      // Create departments one by one to handle validations
      for (const createDepartmentDto of createDepartmentsDto) {
        try {
          const department = await this.create(
            userId,
            businessId,
            createDepartmentDto,
          );
          createdDepartments.push(department);
        } catch (error) {
          // Skip failed departments but continue with others
          console.error(
            `Failed to create department "${createDepartmentDto.name}":`,
            error.message,
          );
        }
      }

      return createdDepartments;
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk create departments: ${error.message}`,
      );
    }
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createDepartmentsDto: CreateDepartmentDto[],
    metadata?: ActivityMetadata,
  ): Promise<{ ids: string[] }> {
    const departments = await this.bulkCreate(
      userId,
      businessId,
      createDepartmentsDto,
      metadata,
    );
    return { ids: departments.map((dept) => dept.id) };
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    departmentIds: string[],
    metadata?: ActivityMetadata,
  ): Promise<{
    deleted: number;
    message: string;
    deletedIds: string[];
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!departmentIds || departmentIds.length === 0) {
        throw new BadRequestException('No department IDs provided');
      }

      // Check if departments exist and belong to the business
      const existingDepartments = await this.db
        .select()
        .from(departments)
        .where(
          and(
            inArray(departments.id, departmentIds),
            eq(departments.businessId, businessId),
            eq(departments.isDeleted, false),
          ),
        );

      if (existingDepartments.length === 0) {
        throw new NotFoundException('No valid departments found to delete');
      }

      // Check for departments with children
      const departmentsWithChildren = await this.db
        .select({ parentId: departments.parentId })
        .from(departments)
        .where(
          and(
            inArray(departments.parentId, departmentIds),
            eq(departments.businessId, businessId),
            eq(departments.isDeleted, false),
          ),
        );

      if (departmentsWithChildren.length > 0) {
        throw new BadRequestException(
          'Cannot delete departments that have child departments',
        );
      }

      const idsToDelete = existingDepartments.map((dept) => dept.id);
      const failedIds = departmentIds.filter((id) => !idsToDelete.includes(id));

      // Bulk soft delete
      await this.db
        .update(departments)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(inArray(departments.id, idsToDelete));

      // Log bulk delete activity
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_DELETE,
        EntityType.DEPARTMENT,
        idsToDelete,
        { isDeleted: true },
        userId,
        businessId,
        {
          filterCriteria: { departmentIds },
          failures: failedIds.map((id: string) => ({
            id,
            error: 'Not found or already deleted',
          })),
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        deleted: idsToDelete.length,
        message: `${idsToDelete.length} department(s) have been successfully deleted`,
        deletedIds: idsToDelete,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk delete departments: ${error.message}`,
      );
    }
  }

  async findAllHierarchy(
    userId: string,
    businessId: string | null,
  ): Promise<{ id: string; name: string; parentId: string | null }[]> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const departmentsList = await this.db
        .select({
          id: departments.id,
          name: departments.name,
          parentId: departments.parentId,
        })
        .from(departments)
        .where(
          and(
            eq(departments.businessId, businessId),
            eq(departments.status, DepartmentStatus.ACTIVE),
            eq(departments.isDeleted, false),
          ),
        )
        .orderBy(asc(departments.name));

      return departmentsList;
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to fetch department hierarchy: ${error.message}`,
      );
    }
  }

  async bulkUpdateDepartmentHierarchy(
    userId: string,
    businessId: string | null,
    updates: { id: string; parentId: string | null }[],
    metadata?: ActivityMetadata,
  ): Promise<{
    updated: number;
    failed: Array<{ id: string; error: string }>;
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!updates || updates.length === 0) {
        throw new BadRequestException('No hierarchy updates provided');
      }

      let updated = 0;
      const failed: Array<{ id: string; error: string }> = [];

      for (const update of updates) {
        try {
          // Verify department exists and belongs to business
          const department = await this.db
            .select()
            .from(departments)
            .where(
              and(
                eq(departments.id, update.id),
                eq(departments.businessId, businessId),
                eq(departments.isDeleted, false),
              ),
            )
            .then((results) => results[0]);

          if (!department) {
            failed.push({ id: update.id, error: 'Department not found' });
            continue;
          }

          // Validate parent if provided
          if (update.parentId) {
            const parent = await this.db
              .select()
              .from(departments)
              .where(
                and(
                  eq(departments.id, update.parentId),
                  eq(departments.businessId, businessId),
                  eq(departments.isDeleted, false),
                ),
              )
              .then((results) => results[0]);

            if (!parent) {
              failed.push({
                id: update.id,
                error: 'Parent department not found',
              });
              continue;
            }

            // Check for circular reference
            if (update.parentId === update.id) {
              failed.push({
                id: update.id,
                error: 'A department cannot be its own parent',
              });
              continue;
            }

            const wouldCreateCircle = await this.checkCircularReference(
              update.parentId,
              update.id,
            );
            if (wouldCreateCircle) {
              failed.push({
                id: update.id,
                error: 'Would create circular reference',
              });
              continue;
            }
          }

          // Update the department
          await this.db
            .update(departments)
            .set({
              parentId: update.parentId,
              updatedBy: userId,
              updatedAt: new Date(),
            })
            .where(eq(departments.id, update.id));

          updated++;
        } catch (error) {
          failed.push({ id: update.id, error: error.message });
        }
      }

      return { updated, failed };
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update department hierarchy: ${error.message}`,
      );
    }
  }

  private async checkCircularReference(
    parentId: string,
    childId: string,
  ): Promise<boolean> {
    // Traverse up the parent chain to see if we encounter the child
    let currentParentId = parentId;
    const visited = new Set<string>();

    while (currentParentId && !visited.has(currentParentId)) {
      if (currentParentId === childId) {
        return true; // Circular reference found
      }

      visited.add(currentParentId);

      const parent = await this.db
        .select({ parentId: departments.parentId })
        .from(departments)
        .where(
          and(
            eq(departments.id, currentParentId),
            eq(departments.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      currentParentId = parent?.parentId || null;
    }

    return false;
  }

  private mapToDepartmentDto(
    department: typeof departments.$inferSelect,
  ): DepartmentDto {
    return {
      id: department.id,
      businessId: department.businessId,
      name: department.name,
      parentId: department.parentId,
      description: department.description,
      status: department.status,
      createdBy: department.createdBy,
      updatedBy: department.updatedBy,
      createdAt: department.createdAt,
      updatedAt: department.updatedAt,
    };
  }

  private async mapToDepartmentListDto(
    department: any,
    parentName?: string | null,
    subTypesCount?: number,
    staffMembersCount?: number,
  ): Promise<DepartmentListDto> {
    // If subTypesCount is not provided, fetch it
    const subCount =
      subTypesCount ??
      (await this.getSubTypesCountForDepartment(
        department.id,
        department.businessId,
      ));

    // If staffMembersCount is not provided, fetch it
    const staffCount =
      staffMembersCount ??
      (await this.getStaffMembersCountForDepartment(
        department.id,
        department.businessId,
      ));

    return {
      id: department.id,
      name: department.name,
      parentName: parentName || null,
      description: department.description,
      status: department.status,
      subTypesCount: subCount,
      staffMembersCount: staffCount,
    };
  }

  private mapToDepartmentSlimDto(department: any): DepartmentSlimDto {
    return {
      id: department.id,
      name: department.name,
    };
  }

  private async getStaffMembersCountForDepartment(
    departmentId: string,
    businessId: string,
  ): Promise<number> {
    try {
      const result = await this.db
        .select({ count: sql<number>`count(*)` })
        .from(staffMembers)
        .where(
          and(
            eq(staffMembers.businessId, businessId),
            eq(staffMembers.departmentId, departmentId),
            eq(staffMembers.isDeleted, false),
          ),
        );

      return Number(result[0].count);
    } catch (error) {
      console.warn(
        `Failed to get staff members count for department ${departmentId}:`,
        error.message,
      );
      return 0;
    }
  }

  private async getSubTypesCountForDepartment(
    departmentId: string,
    businessId: string,
  ): Promise<number> {
    try {
      const result = await this.db
        .select({ count: sql<number>`count(*)` })
        .from(departments)
        .where(
          and(
            eq(departments.businessId, businessId),
            eq(departments.parentId, departmentId),
            eq(departments.isDeleted, false),
          ),
        );

      return Number(result[0].count);
    } catch (error) {
      console.warn(
        `Failed to get sub types count for department ${departmentId}:`,
        error.message,
      );
      return 0;
    }
  }
}
