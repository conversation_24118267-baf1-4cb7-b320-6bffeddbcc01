import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateAccommodationReservationDto } from './dto/create-accommodation-reservation.dto';
import { UpdateAccommodationReservationDto } from './dto/update-accommodation-reservation.dto';
import { AccommodationReservationDto } from './dto/accommodation-reservation.dto';

import { AccommodationReservationListDto } from './dto/accommodation-reservation-list.dto';
import {
  CheckAvailabilityResponseDto,
  AvailableUnitDto,
} from './dto/check-availability.dto';
import {
  ConfirmReservationDto,
  ConfirmReservationResponseDto,
} from './dto/confirm-reservation.dto';
import {
  CancelReservationDto,
  CancelReservationResponseDto,
} from './dto/cancel-reservation.dto';
import {
  CalculateAccommodationPricingDto,
  CalculateAccommodationPricingResponseDto,
  UnitPricingBreakdownDto,
} from './dto/calculate-pricing.dto';
import { CheckInDto, CheckInResponseDto } from './dto/check-in.dto';
import { CheckOutDto, CheckOutResponseDto } from './dto/check-out.dto';
import {
  AddGuestToReservationDto,
  AddGuestToReservationResponseDto,
  AddMultipleGuestsToReservationDto,
  AddMultipleGuestsResponseDto,
  RemoveGuestResponseDto,
  GuestAddedResponseDto,
} from './dto/guest-management.dto';
import {
  SendConfirmationDto,
  SendConfirmationResponseDto,
  SendReminderDto,
  SendReminderResponseDto,
  CommunicationResultDto,
} from './dto/communication.dto';
import {
  ProcessPaymentDto,
  ProcessPaymentResponseDto,
  ProcessRefundDto,
  ProcessRefundResponseDto,
  PaymentResultDto,
  RefundResultDto,
} from './dto/payment.dto';
import {
  accommodationReservations,
  accommodationReservationUnits,
  accommodationReservationGuests,
} from '../drizzle/schema/accommodation-unit-reservations.schema';
import { accommodationUnits } from '../drizzle/schema/accommodation-units.schema';
import { reservationTypes } from '../drizzle/schema/reservation-types.schema';
import { AccommodationUnitStatus } from '../shared/types';
import {
  guests,
  GuestRelationship,
  GuestType,
  GuestStatus,
} from '../drizzle/schema/guests.schema';
import { customers } from '../drizzle/schema/customers.schema';
import { CustomerStatus } from '../shared/types/customer.enum';
import { users } from '../drizzle/schema/users.schema';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { EntityType, ActivitySource } from '../shared/types/activity.enum';
import {
  and,
  eq,
  ilike,
  gte,
  lte,
  inArray,
  desc,
  asc,
  sql,
  count,
  or,
} from 'drizzle-orm';
import {
  AccommodationReservationStatus,
  ReservationSource,
  MealPeriod,
} from '../shared/types/accommodation.enum';
import { PaymentStatus } from '../shared/types/common.enum';
import { ActivityMetadata } from '../shared/types/activity-metadata.type';

@Injectable()
export class AccommodationReservationsService {
  constructor(
    @Inject(DRIZZLE) private readonly db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
  ) {}

  /**
   * Generate a unique guest number for a business
   */
  private async generateUniqueGuestNumber(businessId: string): Promise<string> {
    // Get the count of existing guests for this business
    const countResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(guests)
      .where(eq(guests.businessId, businessId));

    const count = Number(countResult[0].count) + 1;
    const currentYear = new Date().getFullYear();

    return `GUEST-${currentYear}-${count.toString().padStart(6, '0')}`;
  }

  /**
   * Generate customer display name from name parts
   */
  private generateCustomerDisplayName(guestDto: any): string {
    if (guestDto.customerDisplayName) {
      return guestDto.customerDisplayName;
    }

    const nameParts = [
      guestDto.title?.trim(),
      guestDto.firstName?.trim(),
      guestDto.middleName?.trim(),
      guestDto.lastName?.trim(),
      guestDto.suffix?.trim(),
    ].filter(Boolean);

    return nameParts.length > 0 ? nameParts.join(' ') : 'Guest Customer';
  }

  /**
   * Create a customer record from guest DTO
   */
  private async createCustomerFromDto(
    tx: any,
    businessId: string,
    userId: string,
    guestDto: any,
  ): Promise<string> {
    const customerDisplayName = this.generateCustomerDisplayName(guestDto);

    // Check if customer with same display name already exists
    const existingCustomer = await tx
      .select({ id: customers.id })
      .from(customers)
      .where(
        and(
          eq(customers.businessId, businessId),
          eq(customers.customerDisplayName, customerDisplayName),
          eq(customers.isDeleted, false),
        ),
      );

    if (existingCustomer.length > 0) {
      // If customer exists, return existing customer ID
      return existingCustomer[0].id;
    }

    const customerData = {
      businessId,
      title: guestDto.title,
      firstName: guestDto.firstName,
      middleName: guestDto.middleName,
      lastName: guestDto.lastName,
      suffix: guestDto.suffix,
      customerDisplayName,
      companyName: guestDto.companyName,
      email: guestDto.email,
      phoneNumber: guestDto.phoneNumber,
      mobileNumber: guestDto.mobileNumber,
      fax: guestDto.fax,
      website: guestDto.website,
      other: guestDto.other,
      isSubCustomer: guestDto.isSubCustomer ?? false,
      isAllocatedToAllLocations: false, // Default to false for guest-created customers
      status: guestDto.customerStatus ?? CustomerStatus.ACTIVE,
      notes: guestDto.customerNotes,
      createdBy: userId,
      updatedBy: userId,
    };

    const createdCustomer = await tx
      .insert(customers)
      .values(customerData)
      .returning();
    return createdCustomer[0].id;
  }

  /**
   * Create a guest record from guest DTO (with automatic customer creation)
   */
  private async createGuestFromDto(
    tx: any,
    businessId: string,
    userId: string,
    guestDto: any,
  ): Promise<string> {
    // First create the customer record
    const customerId = await this.createCustomerFromDto(
      tx,
      businessId,
      userId,
      guestDto,
    );

    const guestNumber = await this.generateUniqueGuestNumber(businessId);

    const guestData = {
      businessId,
      customerId,
      guestNumber,
      dateOfBirth: guestDto.dateOfBirth ? new Date(guestDto.dateOfBirth) : null,
      gender: guestDto.gender,
      nationality: guestDto.nationality,
      identificationType: guestDto.identificationType,
      identificationNumber: guestDto.identificationNumber,
      identificationIssueDate: guestDto.identificationIssueDate
        ? new Date(guestDto.identificationIssueDate)
        : null,
      identificationExpiryDate: guestDto.identificationExpiryDate
        ? new Date(guestDto.identificationExpiryDate)
        : null,
      identificationIssuingCountry: guestDto.identificationIssuingCountry,
      guestType: guestDto.guestType ?? GuestType.ACCOMMODATION,
      status: guestDto.status ?? GuestStatus.ACTIVE,
      key1: guestDto.key1,
      key2: guestDto.key2,
      key3: guestDto.key3,
      key4: guestDto.key4,
      key5: guestDto.key5,
      key6: guestDto.key6,
      key7: guestDto.key7,
      key8: guestDto.key8,
      key9: guestDto.key9,
      key10: guestDto.key10,
      lastServiceDate: guestDto.lastServiceDate
        ? new Date(guestDto.lastServiceDate)
        : null,
      lastContactDate: guestDto.lastContactDate
        ? new Date(guestDto.lastContactDate)
        : null,
      notes: guestDto.notes,
      createdBy: userId,
      updatedBy: userId,
    };

    const createdGuest = await tx.insert(guests).values(guestData).returning();
    return createdGuest[0].id;
  }

  async create(
    userId: string,
    businessId: string | null,
    createAccommodationReservationDto: CreateAccommodationReservationDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a reservation with the same number already exists for this business
      const existingReservation = await this.db
        .select()
        .from(accommodationReservations)
        .where(
          and(
            eq(accommodationReservations.businessId, businessId),
            ilike(
              accommodationReservations.reservationNumber,
              createAccommodationReservationDto.reservationNumber,
            ),
            eq(accommodationReservations.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingReservation) {
        throw new ConflictException(
          `Accommodation reservation with number "${createAccommodationReservationDto.reservationNumber}" already exists`,
        );
      }

      // Check if reference number exists (if provided)
      if (createAccommodationReservationDto.referenceNumber) {
        const existingReference = await this.db
          .select()
          .from(accommodationReservations)
          .where(
            and(
              eq(accommodationReservations.businessId, businessId),
              eq(
                accommodationReservations.referenceNumber,
                createAccommodationReservationDto.referenceNumber,
              ),
              eq(accommodationReservations.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (existingReference) {
          throw new ConflictException(
            `Accommodation reservation with reference number "${createAccommodationReservationDto.referenceNumber}" already exists`,
          );
        }
      }

      // Validate accommodation units exist
      const unitIds = createAccommodationReservationDto.accommodationUnits.map(
        (unit) => unit.accommodationUnitId,
      );
      const existingUnits = await this.db
        .select({ id: accommodationUnits.id })
        .from(accommodationUnits)
        .where(
          and(
            inArray(accommodationUnits.id, unitIds),
            eq(accommodationUnits.businessId, businessId),
            eq(accommodationUnits.isDeleted, false),
          ),
        );

      if (existingUnits.length !== unitIds.length) {
        throw new BadRequestException(
          'One or more accommodation units do not exist or do not belong to this business',
        );
      }

      // Validate guest data (if guests provided)
      if (createAccommodationReservationDto.guests?.length) {
        const guestIds = createAccommodationReservationDto.guests
          .map((guest) => guest.guestId)
          .filter((id): id is string => id !== undefined);

        // Validate existing guests exist (if any guestIds provided)
        if (guestIds.length > 0) {
          const existingGuests = await this.db
            .select({ id: guests.id })
            .from(guests)
            .where(
              and(
                inArray(guests.id, guestIds),
                eq(guests.businessId, businessId),
                eq(guests.isDeleted, false),
              ),
            );

          if (existingGuests.length !== guestIds.length) {
            throw new BadRequestException(
              'One or more guests do not exist or do not belong to this business',
            );
          }
        }

        // Validate guest data for new guest creation
        for (const guest of createAccommodationReservationDto.guests) {
          if (!guest.guestId) {
            // If no guestId provided, validate customer creation data
            if (
              !guest.customerDisplayName &&
              !guest.firstName &&
              !guest.lastName
            ) {
              throw new BadRequestException(
                'Either guestId (for existing guest) or customerDisplayName/firstName/lastName (for new guest) must be provided',
              );
            }
          }
        }
      }

      // Create reservation in transaction
      const newReservation = await this.db.transaction(async (tx) => {
        // Create main reservation
        const reservation = await tx
          .insert(accommodationReservations)
          .values({
            businessId,
            reservationNumber:
              createAccommodationReservationDto.reservationNumber,
            referenceNumber: createAccommodationReservationDto.referenceNumber,
            checkInDate: new Date(
              createAccommodationReservationDto.checkInDate,
            ),
            checkOutDate: new Date(
              createAccommodationReservationDto.checkOutDate,
            ),
            status:
              createAccommodationReservationDto.status ??
              AccommodationReservationStatus.PENDING,
            reservationSource:
              createAccommodationReservationDto.reservationSource,
            paymentStatus:
              createAccommodationReservationDto.paymentStatus ??
              PaymentStatus.PENDING,
            subtotal: createAccommodationReservationDto.subtotal ?? '0.00',
            total: createAccommodationReservationDto.total ?? '0.00',
            depositPaid: createAccommodationReservationDto.depositPaid,
            balanceDue: createAccommodationReservationDto.balanceDue,
            mealPeriod:
              createAccommodationReservationDto.mealPeriod ??
              MealPeriod.ROOM_ONLY,
            mealPeriodCost:
              createAccommodationReservationDto.mealPeriodCost ?? '0.00',
            mealPeriodNotes: createAccommodationReservationDto.mealPeriodNotes,
            customMealDetails:
              createAccommodationReservationDto.customMealDetails,
            notes: createAccommodationReservationDto.specialRequests,
            createdBy: userId,
            updatedBy: userId,
          })
          .returning();

        // Create reservation units
        const reservationUnits = await tx
          .insert(accommodationReservationUnits)
          .values(
            createAccommodationReservationDto.accommodationUnits.map(
              (unit, index) => ({
                businessId,
                reservationId: reservation[0].id,
                accommodationUnitId: unit.accommodationUnitId,
                unitRoomRate: unit.unitRoomRate,
                unitTotalCost: unit.unitTotalCost,
                unitNumberOfAdults: unit.unitNumberOfAdults,
                unitNumberOfChildren: unit.unitNumberOfChildren ?? 0,
                unitStatus:
                  unit.unitStatus ?? AccommodationReservationStatus.PENDING,
                unitSpecialRequests: unit.unitSpecialRequests,
                unitNotes: unit.unitNotes,
                unitOrder: unit.unitOrder ?? index + 1,
                unitMealPeriod: unit.unitMealPeriod,
                unitMealPeriodCost: unit.unitMealPeriodCost ?? '0.00',
                unitMealPeriodNotes: unit.unitMealPeriodNotes,
                unitCustomMealDetails: unit.unitCustomMealDetails,
                createdBy: userId,
                updatedBy: userId,
              }),
            ),
          )
          .returning();

        // Create guests and reservation guests (if provided)
        if (createAccommodationReservationDto.guests?.length) {
          const finalGuestIds: string[] = [];
          const primaryGuestMap = new Map<number, string>(); // Map index to guest ID for primary guest references

          // First pass: Handle existing guests or create new guests
          for (
            let i = 0;
            i < createAccommodationReservationDto.guests.length;
            i++
          ) {
            const guestDto = createAccommodationReservationDto.guests[i];
            let guestId: string;

            if (guestDto.guestId) {
              // Use existing guest
              guestId = guestDto.guestId;
            } else {
              // Create new guest
              guestId = await this.createGuestFromDto(
                tx,
                businessId,
                userId,
                guestDto,
              );
            }

            finalGuestIds.push(guestId);

            if (guestDto.isPrimaryGuest) {
              primaryGuestMap.set(i, guestId);
            }
          }

          // Second pass: Create reservation guest relationships
          const reservationGuestData =
            createAccommodationReservationDto.guests.map((guest, index) => {
              // Find primary guest ID if this guest is not primary
              let primaryGuestId = null;
              if (!guest.isPrimaryGuest && guest.relationshipToPrimary) {
                // For simplicity, use the first primary guest found
                const primaryGuestEntry = Array.from(
                  primaryGuestMap.entries(),
                )[0];
                if (primaryGuestEntry) {
                  primaryGuestId = primaryGuestEntry[1];
                }
              }

              return {
                businessId,
                reservationUnitId: reservationUnits[0].id, // For now, assign to first unit
                guestId: finalGuestIds[index],
                isPrimaryGuest: guest.isPrimaryGuest ?? false,
                primaryGuestId,
                relationshipToPrimary:
                  guest.relationshipToPrimary as GuestRelationship,
                checkInStatus: guest.checkInStatus ?? 'PENDING',
                createdBy: userId,
                updatedBy: userId,
              };
            });

          await tx
            .insert(accommodationReservationGuests)
            .values(reservationGuestData);
        }

        return reservation[0];
      });

      // Log the reservation creation activity
      await this.activityLogService.logCreate(
        newReservation.id,
        EntityType.RESERVATION,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id: newReservation.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        'Failed to create accommodation reservation',
      );
    }
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createAccommodationReservationDto: CreateAccommodationReservationDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    return this.create(
      userId,
      businessId,
      createAccommodationReservationDto,
      metadata,
    );
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    accommodationReservations: CreateAccommodationReservationDto[],
    _metadata?: ActivityMetadata,
  ): Promise<{ ids: string[]; count: number }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (!accommodationReservations.length) {
      throw new BadRequestException('No accommodation reservations provided');
    }

    const createdIds: string[] = [];
    const errors: string[] = [];

    for (const reservationDto of accommodationReservations) {
      try {
        const result = await this.create(userId, businessId, reservationDto);
        createdIds.push(result.id);
      } catch (error) {
        errors.push(
          `Failed to create reservation "${reservationDto.reservationNumber}": ${error.message}`,
        );
      }
    }

    if (errors.length > 0 && createdIds.length === 0) {
      throw new BadRequestException(
        `Failed to create any accommodation reservations: ${errors.join(', ')}`,
      );
    }

    return {
      ids: createdIds,
      count: createdIds.length,
    };
  }

  async findAllOptimized(
    _userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    reservationNumber?: string,
    referenceNumber?: string,
    status?: string,
    paymentStatus?: string,
    reservationSource?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: AccommodationReservationListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      eq(accommodationReservations.isDeleted, false),
      eq(accommodationReservations.businessId, businessId),
    ];

    // Add date range filters
    if (from) {
      whereConditions.push(
        gte(accommodationReservations.checkInDate, new Date(from)),
      );
    }
    if (to) {
      whereConditions.push(
        lte(accommodationReservations.checkOutDate, new Date(to)),
      );
    }

    // Add search filters
    if (reservationNumber) {
      whereConditions.push(
        ilike(
          accommodationReservations.reservationNumber,
          `%${reservationNumber}%`,
        ),
      );
    }
    if (referenceNumber) {
      whereConditions.push(
        ilike(
          accommodationReservations.referenceNumber,
          `%${referenceNumber}%`,
        ),
      );
    }
    // Handle comma-separated status values
    if (status) {
      const statusValues = status.split(',').map((s) => s.trim());
      if (statusValues.length === 1) {
        whereConditions.push(
          eq(
            accommodationReservations.status,
            statusValues[0] as AccommodationReservationStatus,
          ),
        );
      } else {
        whereConditions.push(
          inArray(
            accommodationReservations.status,
            statusValues as AccommodationReservationStatus[],
          ),
        );
      }
    }

    // Handle comma-separated payment status values
    if (paymentStatus) {
      const paymentStatusValues = paymentStatus.split(',').map((s) => s.trim());
      if (paymentStatusValues.length === 1) {
        whereConditions.push(
          eq(
            accommodationReservations.paymentStatus,
            paymentStatusValues[0] as PaymentStatus,
          ),
        );
      } else {
        whereConditions.push(
          inArray(
            accommodationReservations.paymentStatus,
            paymentStatusValues as PaymentStatus[],
          ),
        );
      }
    }

    // Handle comma-separated reservation source values
    if (reservationSource) {
      const reservationSourceValues = reservationSource
        .split(',')
        .map((s) => s.trim());
      if (reservationSourceValues.length === 1) {
        whereConditions.push(
          eq(
            accommodationReservations.reservationSource,
            reservationSourceValues[0] as ReservationSource,
          ),
        );
      } else {
        whereConditions.push(
          inArray(
            accommodationReservations.reservationSource,
            reservationSourceValues as ReservationSource[],
          ),
        );
      }
    }

    // Advanced filters
    if (filters) {
      try {
        const parsedFilters = JSON.parse(filters);
        const filterConditions: any[] = [];

        for (const filter of parsedFilters) {
          const { id, value, operator } = filter;

          // Map filter field to database column
          let column: any;
          switch (id) {
            case 'reservationNumber':
              column = accommodationReservations.reservationNumber;
              break;
            case 'referenceNumber':
              column = accommodationReservations.referenceNumber;
              break;
            case 'status':
              column = accommodationReservations.status;
              break;
            case 'paymentStatus':
              column = accommodationReservations.paymentStatus;
              break;
            case 'reservationSource':
              column = accommodationReservations.reservationSource;
              break;
            case 'total':
              column = accommodationReservations.total;
              break;
            case 'notes':
              column = accommodationReservations.notes;
              break;
            case 'checkInDate':
              column = accommodationReservations.checkInDate;
              break;
            case 'checkOutDate':
              column = accommodationReservations.checkOutDate;
              break;
            default:
              continue; // Skip unknown fields
          }

          // Apply operator
          switch (operator) {
            case 'iLike':
              filterConditions.push(ilike(column, `%${value}%`));
              break;
            case 'notILike':
              filterConditions.push(sql`NOT ${ilike(column, `%${value}%`)}`);
              break;
            case 'eq':
              filterConditions.push(eq(column, value));
              break;
            case 'ne':
              filterConditions.push(sql`${column} != ${value}`);
              break;
            case 'isEmpty':
              filterConditions.push(sql`${column} IS NULL OR ${column} = ''`);
              break;
            case 'isNotEmpty':
              filterConditions.push(
                sql`${column} IS NOT NULL AND ${column} != ''`,
              );
              break;
            default:
              filterConditions.push(eq(column, value));
          }
        }

        if (filterConditions.length > 0) {
          if (joinOperator === 'or') {
            whereConditions.push(or(...filterConditions));
          } else {
            whereConditions.push(and(...filterConditions));
          }
        }
      } catch {
        // Invalid JSON, ignore filters
      }
    }

    // Build sort conditions
    let orderBy = [desc(accommodationReservations.createdAt)]; // Default sort
    if (sort) {
      try {
        // Try to parse as JSON first (new format)
        const parsedSort = JSON.parse(sort);
        if (Array.isArray(parsedSort) && parsedSort.length > 0) {
          orderBy = parsedSort.map((sortField: any) => {
            const isDesc = sortField.desc === true;
            const direction = isDesc ? desc : asc;

            // Map sort field to database column
            switch (sortField.id) {
              case 'reservationNumber':
                return direction(accommodationReservations.reservationNumber);
              case 'checkInDate':
                return direction(accommodationReservations.checkInDate);
              case 'checkOutDate':
                return direction(accommodationReservations.checkOutDate);
              case 'status':
                return direction(accommodationReservations.status);
              case 'total':
                return direction(accommodationReservations.total);
              case 'createdAt':
                return direction(accommodationReservations.createdAt);
              case 'updatedAt':
                return direction(accommodationReservations.updatedAt);
              default:
                return direction(accommodationReservations.createdAt);
            }
          });
        }
      } catch {
        // If JSON parsing fails, try old format (field:direction)
        const [field, direction] = sort.split(':');
        const isDesc = direction?.toLowerCase() === 'desc';

        switch (field) {
          case 'reservationNumber':
            orderBy = [
              isDesc
                ? desc(accommodationReservations.reservationNumber)
                : asc(accommodationReservations.reservationNumber),
            ];
            break;
          case 'checkInDate':
            orderBy = [
              isDesc
                ? desc(accommodationReservations.checkInDate)
                : asc(accommodationReservations.checkInDate),
            ];
            break;
          case 'checkOutDate':
            orderBy = [
              isDesc
                ? desc(accommodationReservations.checkOutDate)
                : asc(accommodationReservations.checkOutDate),
            ];
            break;
          case 'status':
            orderBy = [
              isDesc
                ? desc(accommodationReservations.status)
                : asc(accommodationReservations.status),
            ];
            break;
          case 'total':
            orderBy = [
              isDesc
                ? desc(accommodationReservations.total)
                : asc(accommodationReservations.total),
            ];
            break;
          case 'createdAt':
            orderBy = [
              isDesc
                ? desc(accommodationReservations.createdAt)
                : asc(accommodationReservations.createdAt),
            ];
            break;
          default:
            orderBy = [desc(accommodationReservations.createdAt)];
        }
      }
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: count() })
      .from(accommodationReservations)
      .where(and(...whereConditions));

    const total = totalResult[0]?.count || 0;
    const totalPages = Math.ceil(total / limit);

    // Get paginated data with joins
    const reservationsData = await this.db
      .select({
        id: accommodationReservations.id,
        reservationNumber: accommodationReservations.reservationNumber,
        referenceNumber: accommodationReservations.referenceNumber,
        checkInDate: accommodationReservations.checkInDate,
        checkOutDate: accommodationReservations.checkOutDate,
        status: accommodationReservations.status,
        reservationSource: accommodationReservations.reservationSource,
        paymentStatus: accommodationReservations.paymentStatus,
        subtotal: accommodationReservations.subtotal,
        total: accommodationReservations.total,
        depositPaid: accommodationReservations.depositPaid,
        balanceDue: accommodationReservations.balanceDue,
        createdBy: users.firstName,
        updatedBy: sql<string>`COALESCE(updated_user.first_name, NULL)`,
        createdAt: accommodationReservations.createdAt,
        updatedAt: accommodationReservations.updatedAt,
      })
      .from(accommodationReservations)
      .leftJoin(users, eq(accommodationReservations.createdBy, users.id))
      .leftJoin(
        sql`${users} AS updated_user`,
        eq(accommodationReservations.updatedBy, sql`updated_user.id`),
      )
      .where(and(...whereConditions))
      .orderBy(...orderBy)
      .limit(limit)
      .offset(offset);

    // Get units and guests count for each reservation
    const reservationIds = reservationsData.map((r) => r.id);

    const unitsCount =
      reservationIds.length > 0
        ? await this.db
            .select({
              reservationId: accommodationReservationUnits.reservationId,
              count: count(),
            })
            .from(accommodationReservationUnits)
            .where(
              and(
                inArray(
                  accommodationReservationUnits.reservationId,
                  reservationIds,
                ),
                eq(accommodationReservationUnits.isDeleted, false),
              ),
            )
            .groupBy(accommodationReservationUnits.reservationId)
        : [];

    const guestsCount =
      reservationIds.length > 0
        ? await this.db
            .select({
              reservationId: sql<string>`${accommodationReservationUnits.reservationId}`,
              count: count(),
            })
            .from(accommodationReservationGuests)
            .leftJoin(
              accommodationReservationUnits,
              eq(
                accommodationReservationGuests.reservationUnitId,
                accommodationReservationUnits.id,
              ),
            )
            .where(
              and(
                inArray(
                  accommodationReservationUnits.reservationId,
                  reservationIds,
                ),
                eq(accommodationReservationGuests.isDeleted, false),
              ),
            )
            .groupBy(accommodationReservationUnits.reservationId)
        : [];

    // Get primary guest info for each reservation
    const primaryGuests =
      reservationIds.length > 0
        ? await this.db
            .select({
              reservationId: sql<string>`${accommodationReservationUnits.reservationId}`,
              guestName: sql<string>`CONCAT(${customers.firstName}, ' ', ${customers.lastName})`,
              guestEmail: customers.email,
              guestPhone: customers.phoneNumber,
            })
            .from(accommodationReservationGuests)
            .leftJoin(
              accommodationReservationUnits,
              eq(
                accommodationReservationGuests.reservationUnitId,
                accommodationReservationUnits.id,
              ),
            )
            .leftJoin(
              guests,
              eq(accommodationReservationGuests.guestId, guests.id),
            )
            .leftJoin(customers, eq(guests.customerId, customers.id))
            .where(
              and(
                inArray(
                  accommodationReservationUnits.reservationId,
                  reservationIds,
                ),
                eq(accommodationReservationGuests.isPrimaryGuest, true),
                eq(accommodationReservationGuests.isDeleted, false),
              ),
            )
        : [];

    // Map counts to reservations
    const unitsCountMap = new Map(
      unitsCount.map((u) => [u.reservationId, u.count]),
    );
    const guestsCountMap = new Map(
      guestsCount.map((g) => [g.reservationId, g.count]),
    );
    const primaryGuestMap = new Map(
      primaryGuests.map((g) => [g.reservationId, g]),
    );

    const data: AccommodationReservationListDto[] = reservationsData.map(
      (reservation) => ({
        id: reservation.id,
        reservationNumber: reservation.reservationNumber,
        referenceNumber: reservation.referenceNumber,
        checkInDate: reservation.checkInDate.toISOString(),
        checkOutDate: reservation.checkOutDate.toISOString(),
        status: reservation.status,
        reservationSource: reservation.reservationSource,
        paymentStatus: reservation.paymentStatus,
        subtotal: reservation.subtotal,
        total: reservation.total,
        depositPaid: reservation.depositPaid,
        balanceDue: reservation.balanceDue,
        unitsCount: unitsCountMap.get(reservation.id) || 0,
        guestsCount: guestsCountMap.get(reservation.id) || 0,
        primaryGuestName:
          primaryGuestMap.get(reservation.id)?.guestName || 'N/A',
        primaryGuestEmail:
          primaryGuestMap.get(reservation.id)?.guestEmail || 'N/A',
        primaryGuestPhone: primaryGuestMap.get(reservation.id)?.guestPhone,
        createdBy: reservation.createdBy || 'Unknown',
        updatedBy: reservation.updatedBy,
        createdAt: reservation.createdAt.toISOString(),
        updatedAt: reservation.updatedAt?.toISOString(),
      }),
    );

    return {
      data,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findOne(
    _userId: string,
    businessId: string | null,
    id: string,
  ): Promise<AccommodationReservationDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Get main reservation data
    const reservationData = await this.db
      .select({
        id: accommodationReservations.id,
        businessId: accommodationReservations.businessId,
        reservationNumber: accommodationReservations.reservationNumber,
        referenceNumber: accommodationReservations.referenceNumber,
        checkInDate: accommodationReservations.checkInDate,
        checkOutDate: accommodationReservations.checkOutDate,
        status: accommodationReservations.status,
        reservationSource: accommodationReservations.reservationSource,
        paymentStatus: accommodationReservations.paymentStatus,
        subtotal: accommodationReservations.subtotal,
        total: accommodationReservations.total,
        depositPaid: accommodationReservations.depositPaid,
        balanceDue: accommodationReservations.balanceDue,
        mealPeriod: accommodationReservations.mealPeriod,
        mealPeriodCost: accommodationReservations.mealPeriodCost,
        mealPeriodNotes: accommodationReservations.mealPeriodNotes,
        customMealDetails: accommodationReservations.customMealDetails,
        notes: accommodationReservations.notes,
        createdBy: users.firstName,
        updatedBy: sql<string>`COALESCE(updated_user.first_name, NULL)`,
        createdAt: accommodationReservations.createdAt,
        updatedAt: accommodationReservations.updatedAt,
      })
      .from(accommodationReservations)
      .leftJoin(users, eq(accommodationReservations.createdBy, users.id))
      .leftJoin(
        sql`${users} AS updated_user`,
        eq(accommodationReservations.updatedBy, sql`updated_user.id`),
      )
      .where(
        and(
          eq(accommodationReservations.id, id),
          eq(accommodationReservations.businessId, businessId),
          eq(accommodationReservations.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!reservationData) {
      throw new NotFoundException('Accommodation reservation not found');
    }

    // Get reservation units with accommodation unit details
    const reservationUnits = await this.db
      .select({
        id: accommodationReservationUnits.id,
        reservationId: accommodationReservationUnits.reservationId,
        accommodationUnitId: accommodationReservationUnits.accommodationUnitId,
        unitRoomRate: accommodationReservationUnits.unitRoomRate,
        unitTotalCost: accommodationReservationUnits.unitTotalCost,
        unitNumberOfAdults: accommodationReservationUnits.unitNumberOfAdults,
        unitNumberOfChildren:
          accommodationReservationUnits.unitNumberOfChildren,
        unitStatus: accommodationReservationUnits.unitStatus,
        unitNotes: accommodationReservationUnits.notes,
        unitOrder: accommodationReservationUnits.unitOrder,
        unitMealPeriod: accommodationReservationUnits.unitMealPeriod,
        unitMealPeriodCost: accommodationReservationUnits.unitMealPeriodCost,
        unitMealPeriodNotes: accommodationReservationUnits.unitMealPeriodNotes,
        unitCustomMealDetails:
          accommodationReservationUnits.unitCustomMealDetails,
        unitCreatedBy: sql<string>`unit_created_user.first_name`,
        unitUpdatedBy: sql<string>`COALESCE(unit_updated_user.first_name, NULL)`,
        unitCreatedAt: accommodationReservationUnits.createdAt,
        unitUpdatedAt: accommodationReservationUnits.updatedAt,
        // Accommodation unit details
        accommodationUnitName: accommodationUnits.name,
        accommodationUnitNumber: accommodationUnits.roomNumber,
        accommodationUnitMaxAdults: accommodationUnits.maxAdults,
        accommodationUnitMaxChildren: accommodationUnits.maxChildren,
      })
      .from(accommodationReservationUnits)
      .leftJoin(
        accommodationUnits,
        eq(
          accommodationReservationUnits.accommodationUnitId,
          accommodationUnits.id,
        ),
      )
      .leftJoin(
        sql`${users} AS unit_created_user`,
        eq(accommodationReservationUnits.createdBy, sql`unit_created_user.id`),
      )
      .leftJoin(
        sql`${users} AS unit_updated_user`,
        eq(accommodationReservationUnits.updatedBy, sql`unit_updated_user.id`),
      )
      .where(
        and(
          eq(accommodationReservationUnits.reservationId, id),
          eq(accommodationReservationUnits.isDeleted, false),
        ),
      )
      .orderBy(asc(accommodationReservationUnits.unitOrder));

    // Get reservation guests with guest and customer details
    const reservationGuests = await this.db
      .select({
        id: accommodationReservationGuests.id,
        reservationUnitId: accommodationReservationGuests.reservationUnitId,
        guestId: accommodationReservationGuests.guestId,
        isPrimaryGuest: accommodationReservationGuests.isPrimaryGuest,
        primaryGuestId: accommodationReservationGuests.primaryGuestId,
        relationshipToPrimary:
          accommodationReservationGuests.relationshipToPrimary,
        checkInStatus: accommodationReservationGuests.checkInStatus,
        checkInTime: accommodationReservationGuests.checkInTime,
        checkOutTime: accommodationReservationGuests.checkOutTime,
        guestCreatedBy: sql<string>`guest_created_user.first_name`,
        guestUpdatedBy: sql<string>`COALESCE(guest_updated_user.first_name, NULL)`,
        guestCreatedAt: accommodationReservationGuests.createdAt,
        guestUpdatedAt: accommodationReservationGuests.updatedAt,
        // Guest details
        guestNumber: guests.guestNumber,
        customerId: guests.customerId,
        guestDateOfBirth: guests.dateOfBirth,
        guestGender: guests.gender,
        guestNationality: guests.nationality,
        guestIdentificationType: guests.identificationType,
        guestIdentificationNumber: guests.identificationNumber,
        guestIdentificationIssueDate: guests.identificationIssueDate,
        guestIdentificationExpiryDate: guests.identificationExpiryDate,
        guestIdentificationIssuingCountry: guests.identificationIssuingCountry,
        guestType: guests.guestType,
        guestStatus: guests.status,
        guestKey1: guests.key1,
        guestKey2: guests.key2,
        guestKey3: guests.key3,
        guestKey4: guests.key4,
        guestKey5: guests.key5,
        guestKey6: guests.key6,
        guestKey7: guests.key7,
        guestKey8: guests.key8,
        guestKey9: guests.key9,
        guestKey10: guests.key10,
        guestLastServiceDate: guests.lastServiceDate,
        guestLastContactDate: guests.lastContactDate,
        // Customer details
        customerDisplayName: customers.customerDisplayName,
        customerTitle: customers.title,
        customerFirstName: customers.firstName,
        customerMiddleName: customers.middleName,
        customerLastName: customers.lastName,
        customerSuffix: customers.suffix,
        customerCompanyName: customers.companyName,
        customerEmail: customers.email,
        customerPhoneNumber: customers.phoneNumber,
        customerMobileNumber: customers.mobileNumber,
        customerFax: customers.fax,
        customerWebsite: customers.website,
        customerOther: customers.other,
        customerStatus: customers.status,
        customerIsSubCustomer: customers.isSubCustomer,
      })
      .from(accommodationReservationGuests)
      .leftJoin(guests, eq(accommodationReservationGuests.guestId, guests.id))
      .leftJoin(customers, eq(guests.customerId, customers.id))
      .leftJoin(
        sql`${users} AS guest_created_user`,
        eq(
          accommodationReservationGuests.createdBy,
          sql`guest_created_user.id`,
        ),
      )
      .leftJoin(
        sql`${users} AS guest_updated_user`,
        eq(
          accommodationReservationGuests.updatedBy,
          sql`guest_updated_user.id`,
        ),
      )
      .leftJoin(
        accommodationReservationUnits,
        eq(
          accommodationReservationGuests.reservationUnitId,
          accommodationReservationUnits.id,
        ),
      )
      .where(
        and(
          eq(accommodationReservationUnits.reservationId, id),
          eq(accommodationReservationGuests.isDeleted, false),
        ),
      );

    // Transform data to DTOs
    const accommodationUnitsDto = reservationUnits.map((unit) => ({
      id: unit.id,
      reservationId: unit.reservationId,
      accommodationUnitId: unit.accommodationUnitId,
      accommodationUnit: {
        id: unit.accommodationUnitId,
        name: unit.accommodationUnitName || '',
        unitNumber: unit.accommodationUnitNumber || '',
        unitType: 'Standard', // Default value since field doesn't exist
        maxAdults: unit.accommodationUnitMaxAdults || 0,
        maxChildren: unit.accommodationUnitMaxChildren || 0,
      },
      unitRoomRate: unit.unitRoomRate,
      unitTotalCost: unit.unitTotalCost,
      unitNumberOfAdults: unit.unitNumberOfAdults,
      unitNumberOfChildren: unit.unitNumberOfChildren,
      unitStatus: unit.unitStatus,
      unitSpecialRequests: unit.unitNotes, // Using notes field
      unitNotes: unit.unitNotes,
      unitOrder: unit.unitOrder,
      unitCheckInTime: undefined, // Field doesn't exist in schema
      unitCheckOutTime: undefined, // Field doesn't exist in schema
      createdBy: unit.unitCreatedBy || 'Unknown',
      updatedBy: unit.unitUpdatedBy,
      createdAt: unit.unitCreatedAt.toISOString(),
      updatedAt: unit.unitUpdatedAt?.toISOString(),
    }));

    const guestsDto = reservationGuests.map((guest) => ({
      id: guest.id,
      reservationUnitId: guest.reservationUnitId,
      guestId: guest.guestId,
      guest: {
        id: guest.guestId,
        guestNumber: guest.guestNumber || '',
        customerId: guest.customerId || '',
        dateOfBirth: guest.guestDateOfBirth
          ? String(guest.guestDateOfBirth).split('T')[0]
          : undefined,
        gender: guest.guestGender,
        nationality: guest.guestNationality,
        identificationType: guest.guestIdentificationType,
        identificationNumber: guest.guestIdentificationNumber,
        identificationIssueDate: guest.guestIdentificationIssueDate
          ? String(guest.guestIdentificationIssueDate).split('T')[0]
          : undefined,
        identificationExpiryDate: guest.guestIdentificationExpiryDate
          ? String(guest.guestIdentificationExpiryDate).split('T')[0]
          : undefined,
        identificationIssuingCountry: guest.guestIdentificationIssuingCountry,
        guestType: guest.guestType || 'ACCOMMODATION',
        status: guest.guestStatus || 'ACTIVE',
        key1: guest.guestKey1,
        key2: guest.guestKey2,
        key3: guest.guestKey3,
        key4: guest.guestKey4,
        key5: guest.guestKey5,
        key6: guest.guestKey6,
        key7: guest.guestKey7,
        key8: guest.guestKey8,
        key9: guest.guestKey9,
        key10: guest.guestKey10,
        lastServiceDate: guest.guestLastServiceDate
          ? String(guest.guestLastServiceDate).split('T')[0]
          : undefined,
        lastContactDate: guest.guestLastContactDate
          ? String(guest.guestLastContactDate).split('T')[0]
          : undefined,
        customer: {
          id: guest.customerId || '',
          customerDisplayName: guest.customerDisplayName || '',
          title: guest.customerTitle,
          firstName: guest.customerFirstName,
          middleName: guest.customerMiddleName,
          lastName: guest.customerLastName,
          suffix: guest.customerSuffix,
          companyName: guest.customerCompanyName,
          email: guest.customerEmail,
          phoneNumber: guest.customerPhoneNumber,
          mobileNumber: guest.customerMobileNumber,
          fax: guest.customerFax,
          website: guest.customerWebsite,
          other: guest.customerOther,
          status: guest.customerStatus || 'active',
          isSubCustomer: guest.customerIsSubCustomer || false,
        },
      },
      isPrimaryGuest: guest.isPrimaryGuest,
      primaryGuestId: guest.primaryGuestId,
      relationshipToPrimary: guest.relationshipToPrimary,
      checkInStatus: guest.checkInStatus,
      checkInTime: guest.checkInTime?.toISOString(),
      checkOutTime: guest.checkOutTime?.toISOString(),
      createdBy: guest.guestCreatedBy || 'Unknown',
      updatedBy: guest.guestUpdatedBy,
      createdAt: guest.guestCreatedAt.toISOString(),
      updatedAt: guest.guestUpdatedAt?.toISOString(),
    }));

    return {
      id: reservationData.id,
      businessId: reservationData.businessId,
      reservationNumber: reservationData.reservationNumber,
      referenceNumber: reservationData.referenceNumber,
      checkInDate: reservationData.checkInDate.toISOString(),
      checkOutDate: reservationData.checkOutDate.toISOString(),
      status: reservationData.status,
      reservationSource: reservationData.reservationSource,
      paymentStatus: reservationData.paymentStatus,
      subtotal: reservationData.subtotal,
      total: reservationData.total,
      depositPaid: reservationData.depositPaid,
      balanceDue: reservationData.balanceDue,
      taxAmount: undefined, // Field doesn't exist in schema
      taxType: undefined, // Field doesn't exist in schema
      discountAmount: undefined, // Field doesn't exist in schema
      discountType: undefined, // Field doesn't exist in schema
      mealPeriod: reservationData.mealPeriod || MealPeriod.ROOM_ONLY,
      mealPeriodCost: reservationData.mealPeriodCost,
      mealPeriodNotes: reservationData.mealPeriodNotes,
      customMealDetails: reservationData.customMealDetails,
      specialRequests: reservationData.notes,
      internalNotes: reservationData.notes,
      guestNotes: reservationData.notes,
      accommodationUnits: accommodationUnitsDto,
      guests: guestsDto,
      createdBy: reservationData.createdBy || 'Unknown',
      updatedBy: reservationData.updatedBy,
      createdAt: reservationData.createdAt.toISOString(),
      updatedAt: reservationData.updatedAt?.toISOString(),
    };
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateAccommodationReservationDto: UpdateAccommodationReservationDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if reservation exists
    const existingReservation = await this.db
      .select()
      .from(accommodationReservations)
      .where(
        and(
          eq(accommodationReservations.id, id),
          eq(accommodationReservations.businessId, businessId),
          eq(accommodationReservations.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingReservation) {
      throw new NotFoundException('Accommodation reservation not found');
    }

    // Check for reservation number conflicts (if being updated)
    if (updateAccommodationReservationDto.reservationNumber) {
      const conflictingReservation = await this.db
        .select()
        .from(accommodationReservations)
        .where(
          and(
            eq(accommodationReservations.businessId, businessId),
            ilike(
              accommodationReservations.reservationNumber,
              updateAccommodationReservationDto.reservationNumber,
            ),
            sql`${accommodationReservations.id} != ${id}`,
            eq(accommodationReservations.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (conflictingReservation) {
        throw new ConflictException(
          `Accommodation reservation with number "${updateAccommodationReservationDto.reservationNumber}" already exists`,
        );
      }
    }

    // Check for reference number conflicts (if being updated)
    if (updateAccommodationReservationDto.referenceNumber) {
      const conflictingReference = await this.db
        .select()
        .from(accommodationReservations)
        .where(
          and(
            eq(accommodationReservations.businessId, businessId),
            eq(
              accommodationReservations.referenceNumber,
              updateAccommodationReservationDto.referenceNumber,
            ),
            sql`${accommodationReservations.id} != ${id}`,
            eq(accommodationReservations.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (conflictingReference) {
        throw new ConflictException(
          `Accommodation reservation with reference number "${updateAccommodationReservationDto.referenceNumber}" already exists`,
        );
      }
    }

    // Validate guest data (if guests provided in update)
    if (updateAccommodationReservationDto.guests?.length) {
      const guestIds = updateAccommodationReservationDto.guests
        .map((guest) => guest.guestId)
        .filter((id): id is string => id !== undefined);

      // Validate existing guests exist (if any guestIds provided)
      if (guestIds.length > 0) {
        const existingGuests = await this.db
          .select({ id: guests.id })
          .from(guests)
          .where(
            and(
              inArray(guests.id, guestIds),
              eq(guests.businessId, businessId),
              eq(guests.isDeleted, false),
            ),
          );

        if (existingGuests.length !== guestIds.length) {
          throw new BadRequestException(
            'One or more guests do not exist or do not belong to this business',
          );
        }
      }

      // Validate guest data for new guest creation
      for (const guest of updateAccommodationReservationDto.guests) {
        if (!guest.guestId) {
          // If no guestId provided, validate customer creation data
          if (
            !guest.customerDisplayName &&
            !guest.firstName &&
            !guest.lastName
          ) {
            throw new BadRequestException(
              'Either guestId (for existing guest) or customerDisplayName/firstName/lastName (for new guest) must be provided',
            );
          }
        }
      }
    }

    // Update reservation in transaction
    await this.db.transaction(async (tx) => {
      // Update main reservation
      const updateData: any = {
        updatedBy: userId,
        updatedAt: new Date(),
      };

      // Add fields that are being updated
      if (updateAccommodationReservationDto.reservationNumber !== undefined) {
        updateData.reservationNumber =
          updateAccommodationReservationDto.reservationNumber;
      }
      if (updateAccommodationReservationDto.referenceNumber !== undefined) {
        updateData.referenceNumber =
          updateAccommodationReservationDto.referenceNumber;
      }
      if (updateAccommodationReservationDto.checkInDate !== undefined) {
        updateData.checkInDate = new Date(
          updateAccommodationReservationDto.checkInDate,
        );
      }
      if (updateAccommodationReservationDto.checkOutDate !== undefined) {
        updateData.checkOutDate = new Date(
          updateAccommodationReservationDto.checkOutDate,
        );
      }
      if (updateAccommodationReservationDto.status !== undefined) {
        updateData.status = updateAccommodationReservationDto.status;
      }
      if (updateAccommodationReservationDto.reservationSource !== undefined) {
        updateData.reservationSource =
          updateAccommodationReservationDto.reservationSource;
      }
      if (updateAccommodationReservationDto.paymentStatus !== undefined) {
        updateData.paymentStatus =
          updateAccommodationReservationDto.paymentStatus;
      }
      if (updateAccommodationReservationDto.subtotal !== undefined) {
        updateData.subtotal = updateAccommodationReservationDto.subtotal;
      }
      if (updateAccommodationReservationDto.total !== undefined) {
        updateData.total = updateAccommodationReservationDto.total;
      }
      if (updateAccommodationReservationDto.depositPaid !== undefined) {
        updateData.depositPaid = updateAccommodationReservationDto.depositPaid;
      }
      if (updateAccommodationReservationDto.balanceDue !== undefined) {
        updateData.balanceDue = updateAccommodationReservationDto.balanceDue;
      }
      if (updateAccommodationReservationDto.taxAmount !== undefined) {
        updateData.taxAmount = updateAccommodationReservationDto.taxAmount;
      }
      if (updateAccommodationReservationDto.taxType !== undefined) {
        updateData.taxType = updateAccommodationReservationDto.taxType;
      }
      if (updateAccommodationReservationDto.discountAmount !== undefined) {
        updateData.discountAmount =
          updateAccommodationReservationDto.discountAmount;
      }
      if (updateAccommodationReservationDto.specialRequests !== undefined) {
        updateData.notes = updateAccommodationReservationDto.specialRequests;
      }

      await tx
        .update(accommodationReservations)
        .set(updateData)
        .where(eq(accommodationReservations.id, id));

      // Handle accommodation units updates (if provided)
      if (updateAccommodationReservationDto.accommodationUnits) {
        // For simplicity, we'll delete existing units and recreate them
        // In a production system, you might want to handle updates more granularly
        await tx
          .update(accommodationReservationUnits)
          .set({ isDeleted: true, updatedBy: userId, updatedAt: new Date() })
          .where(eq(accommodationReservationUnits.reservationId, id));

        if (updateAccommodationReservationDto.accommodationUnits.length > 0) {
          await tx.insert(accommodationReservationUnits).values(
            updateAccommodationReservationDto.accommodationUnits.map(
              (unit, index) => ({
                businessId,
                reservationId: id,
                accommodationUnitId: unit.accommodationUnitId || '',
                unitRoomRate: unit.unitRoomRate || '0.00',
                unitTotalCost: unit.unitTotalCost || '0.00',
                unitNumberOfAdults: unit.unitNumberOfAdults || 1,
                unitNumberOfChildren: unit.unitNumberOfChildren ?? 0,
                unitStatus:
                  unit.unitStatus ?? AccommodationReservationStatus.PENDING,
                unitSpecialRequests: unit.unitSpecialRequests,
                unitNotes: unit.unitNotes,
                unitOrder: unit.unitOrder ?? index + 1,
                createdBy: userId,
                updatedBy: userId,
              }),
            ),
          );
        }
      }

      // Handle guests updates (if provided)
      if (updateAccommodationReservationDto.guests) {
        // Get current reservation units to assign guests
        const currentUnits = await tx
          .select({ id: accommodationReservationUnits.id })
          .from(accommodationReservationUnits)
          .where(
            and(
              eq(accommodationReservationUnits.reservationId, id),
              eq(accommodationReservationUnits.isDeleted, false),
            ),
          );

        if (currentUnits.length > 0) {
          // Delete existing guests
          await tx
            .update(accommodationReservationGuests)
            .set({ isDeleted: true, updatedBy: userId, updatedAt: new Date() })
            .where(
              inArray(
                accommodationReservationGuests.reservationUnitId,
                currentUnits.map((u) => u.id),
              ),
            );

          if (updateAccommodationReservationDto.guests.length > 0) {
            const finalGuestIds: string[] = [];
            const primaryGuestMap = new Map<number, string>();

            // First pass: Handle existing guests or create new guests
            for (
              let i = 0;
              i < updateAccommodationReservationDto.guests.length;
              i++
            ) {
              const guestDto = updateAccommodationReservationDto.guests[i];
              let guestId: string;

              if (guestDto.guestId) {
                // Use existing guest
                guestId = guestDto.guestId;
              } else {
                // Create new guest
                guestId = await this.createGuestFromDto(
                  tx,
                  businessId,
                  userId,
                  guestDto,
                );
              }

              finalGuestIds.push(guestId);

              if (guestDto.isPrimaryGuest) {
                primaryGuestMap.set(i, guestId);
              }
            }

            // Second pass: Create reservation guest relationships
            const reservationGuestData =
              updateAccommodationReservationDto.guests.map((guest, index) => {
                // Find primary guest ID if this guest is not primary
                let primaryGuestId = null;
                if (!guest.isPrimaryGuest && guest.relationshipToPrimary) {
                  // For simplicity, use the first primary guest found
                  const primaryGuestEntry = Array.from(
                    primaryGuestMap.entries(),
                  )[0];
                  if (primaryGuestEntry) {
                    primaryGuestId = primaryGuestEntry[1];
                  }
                }

                return {
                  businessId,
                  reservationUnitId: currentUnits[0].id, // Assign to first unit for simplicity
                  guestId: finalGuestIds[index],
                  isPrimaryGuest: guest.isPrimaryGuest ?? false,
                  primaryGuestId,
                  relationshipToPrimary:
                    guest.relationshipToPrimary as GuestRelationship,
                  checkInStatus: guest.checkInStatus ?? 'PENDING',
                  createdBy: userId,
                  updatedBy: userId,
                };
              });

            await tx
              .insert(accommodationReservationGuests)
              .values(reservationGuestData);
          }
        }
      }
    });

    // Log the update activity
    await this.activityLogService.logUpdate(
      id,
      EntityType.RESERVATION,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return { id };
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ message: string; id: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if reservation exists
    const existingReservation = await this.db
      .select()
      .from(accommodationReservations)
      .where(
        and(
          eq(accommodationReservations.id, id),
          eq(accommodationReservations.businessId, businessId),
          eq(accommodationReservations.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingReservation) {
      throw new NotFoundException('Accommodation reservation not found');
    }

    // Soft delete reservation and related records in transaction
    await this.db.transaction(async (tx) => {
      // Soft delete main reservation
      await tx
        .update(accommodationReservations)
        .set({ isDeleted: true, updatedBy: userId, updatedAt: new Date() })
        .where(eq(accommodationReservations.id, id));

      // Soft delete reservation units
      await tx
        .update(accommodationReservationUnits)
        .set({ isDeleted: true, updatedBy: userId, updatedAt: new Date() })
        .where(eq(accommodationReservationUnits.reservationId, id));

      // Soft delete reservation guests
      const unitIds = await tx
        .select({ id: accommodationReservationUnits.id })
        .from(accommodationReservationUnits)
        .where(eq(accommodationReservationUnits.reservationId, id));

      if (unitIds.length > 0) {
        await tx
          .update(accommodationReservationGuests)
          .set({ isDeleted: true, updatedBy: userId, updatedAt: new Date() })
          .where(
            inArray(
              accommodationReservationGuests.reservationUnitId,
              unitIds.map((u) => u.id),
            ),
          );
      }
    });

    // Log the deletion activity
    await this.activityLogService.logDelete(
      id,
      EntityType.RESERVATION,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return {
      message: 'Accommodation reservation deleted successfully',
      id,
    };
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    ids: string[],
  ): Promise<{ deletedIds: string[]; count: number; failedIds: string[] }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (!ids.length) {
      throw new BadRequestException(
        'No accommodation reservation IDs provided',
      );
    }

    const deletedIds: string[] = [];
    const failedIds: string[] = [];

    for (const id of ids) {
      try {
        await this.remove(userId, businessId, id);
        deletedIds.push(id);
      } catch {
        failedIds.push(id);
      }
    }

    return {
      deletedIds,
      count: deletedIds.length,
      failedIds,
    };
  }

  async bulkUpdateStatus(
    userId: string,
    businessId: string | null,
    ids: string[],
    status: AccommodationReservationStatus,
    metadata?: ActivityMetadata,
  ): Promise<{ updatedIds: string[]; count: number; failedIds: string[] }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (!ids.length) {
      throw new BadRequestException(
        'No accommodation reservation IDs provided',
      );
    }

    const updatedIds: string[] = [];
    const failedIds: string[] = [];

    for (const id of ids) {
      try {
        // Check if reservation exists
        const existingReservation = await this.db
          .select()
          .from(accommodationReservations)
          .where(
            and(
              eq(accommodationReservations.id, id),
              eq(accommodationReservations.businessId, businessId),
              eq(accommodationReservations.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!existingReservation) {
          failedIds.push(id);
          continue;
        }

        // Update status
        await this.db
          .update(accommodationReservations)
          .set({
            status,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(eq(accommodationReservations.id, id));

        // Log the update activity
        await this.activityLogService.logUpdate(
          id,
          EntityType.RESERVATION,
          userId,
          businessId,
          {
            source: metadata?.source || ActivitySource.WEB,
            ipAddress: metadata?.ipAddress,
            userAgent: metadata?.userAgent,
            sessionId: metadata?.sessionId,
          },
        );

        updatedIds.push(id);
      } catch {
        failedIds.push(id);
      }
    }

    return {
      updatedIds,
      count: updatedIds.length,
      failedIds,
    };
  }

  /**
   * Get all accommodation guests for a business with advanced filtering
   */
  async getAllAccommodationGuests(
    _userId: string,
    businessId: string,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    guestNumber?: string,
    customerName?: string,
    email?: string,
    phoneNumber?: string,
    nationality?: string,
    status?: string,
    identificationType?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
    search?: string,
  ) {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      eq(guests.businessId, businessId),
      eq(guests.guestType, GuestType.ACCOMMODATION),
      eq(guests.isDeleted, false),
    ];

    // Add date range filters
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(guests.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        // Add 23:59:59 to include the entire day
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(guests.createdAt, toDate));
      }
    }

    // Add simple field filters
    if (guestNumber) {
      whereConditions.push(ilike(guests.guestNumber, `%${guestNumber}%`));
    }

    if (customerName) {
      whereConditions.push(
        or(
          ilike(customers.customerDisplayName, `%${customerName}%`),
          ilike(customers.firstName, `%${customerName}%`),
          ilike(customers.lastName, `%${customerName}%`),
        ),
      );
    }

    if (email) {
      whereConditions.push(ilike(customers.email, `%${email}%`));
    }

    if (phoneNumber) {
      whereConditions.push(
        or(
          ilike(customers.phoneNumber, `%${phoneNumber}%`),
          ilike(customers.mobileNumber, `%${phoneNumber}%`),
        ),
      );
    }

    if (nationality) {
      whereConditions.push(ilike(guests.nationality, `%${nationality}%`));
    }

    // Handle comma-separated status values
    if (status) {
      const statusValues = status.split(',').map((s) => s.trim());
      if (statusValues.length === 1) {
        whereConditions.push(eq(guests.status, statusValues[0] as GuestStatus));
      } else {
        whereConditions.push(
          inArray(guests.status, statusValues as GuestStatus[]),
        );
      }
    }

    // Handle comma-separated identification type values
    if (identificationType) {
      const identificationTypes = identificationType
        .split(',')
        .map((t) => t.trim());
      if (identificationTypes.length === 1) {
        whereConditions.push(
          ilike(guests.identificationType, `%${identificationTypes[0]}%`),
        );
      } else {
        whereConditions.push(
          or(
            ...identificationTypes.map((type) =>
              ilike(guests.identificationType, `%${type}%`),
            ),
          ),
        );
      }
    }

    // Add legacy search conditions (for backward compatibility)
    if (search) {
      whereConditions.push(
        or(
          ilike(customers.customerDisplayName, `%${search}%`),
          ilike(customers.firstName, `%${search}%`),
          ilike(customers.lastName, `%${search}%`),
          ilike(customers.email, `%${search}%`),
          ilike(customers.phoneNumber, `%${search}%`),
          ilike(customers.mobileNumber, `%${search}%`),
          ilike(guests.guestNumber, `%${search}%`),
          ilike(guests.identificationNumber, `%${search}%`),
        ),
      );
    }

    // Advanced filters
    if (filters) {
      try {
        const parsedFilters = JSON.parse(filters);
        const filterConditions: any[] = [];

        for (const filter of parsedFilters) {
          const { id, value, operator } = filter;

          // Map filter field to database column
          let column: any;
          if (id.startsWith('customer')) {
            // Customer fields
            const customerField = id.replace('customer', '').toLowerCase();
            if (customerField === 'displayname')
              column = customers.customerDisplayName;
            else if (customerField === 'firstname')
              column = customers.firstName;
            else if (customerField === 'lastname') column = customers.lastName;
            else if (customerField === 'email') column = customers.email;
            else if (customerField === 'phonenumber')
              column = customers.phoneNumber;
            else if (customerField === 'mobilenumber')
              column = customers.mobileNumber;
            else if (customerField === 'status') column = customers.status;
            else continue; // Skip unknown customer fields
          } else {
            // Guest fields
            if (id === 'guestNumber') column = guests.guestNumber;
            else if (id === 'nationality') column = guests.nationality;
            else if (id === 'status') column = guests.status;
            else if (id === 'identificationType')
              column = guests.identificationType;
            else if (id === 'identificationNumber')
              column = guests.identificationNumber;
            else if (id === 'gender') column = guests.gender;
            else if (id === 'notes') column = guests.notes;
            else continue; // Skip unknown guest fields
          }

          // Apply operator
          switch (operator) {
            case 'iLike':
              filterConditions.push(ilike(column, `%${value}%`));
              break;
            case 'notILike':
              filterConditions.push(sql`NOT ${ilike(column, `%${value}%`)}`);
              break;
            case 'eq':
              filterConditions.push(eq(column, value));
              break;
            case 'ne':
              filterConditions.push(sql`${column} != ${value}`);
              break;
            case 'isEmpty':
              filterConditions.push(sql`${column} IS NULL OR ${column} = ''`);
              break;
            case 'isNotEmpty':
              filterConditions.push(
                sql`${column} IS NOT NULL AND ${column} != ''`,
              );
              break;
            default:
              filterConditions.push(eq(column, value));
          }
        }

        if (filterConditions.length > 0) {
          if (joinOperator === 'or') {
            whereConditions.push(or(...filterConditions));
          } else {
            whereConditions.push(and(...filterConditions));
          }
        }
      } catch {
        // Invalid JSON, ignore filters
      }
    }

    // Build sort conditions
    let orderBy = [desc(guests.createdAt)]; // Default sort
    if (sort) {
      try {
        const parsedSort = JSON.parse(sort);
        if (parsedSort.length > 0) {
          orderBy = parsedSort.map((sortField: any) => {
            const isDesc = sortField.desc === true;
            const direction = isDesc ? desc : asc;

            // Map sort field to database column
            switch (sortField.id) {
              case 'guestNumber':
                return direction(guests.guestNumber);
              case 'customerDisplayName':
                return direction(customers.customerDisplayName);
              case 'createdAt':
                return direction(guests.createdAt);
              case 'updatedAt':
                return direction(guests.updatedAt);
              case 'nationality':
                return direction(guests.nationality);
              case 'status':
                return direction(guests.status);
              default:
                return direction(guests.createdAt);
            }
          });
        }
      } catch {
        // Invalid JSON, use default sort
        orderBy = [desc(guests.createdAt)];
      }
    }

    // Build the query
    const query = this.db
      .select({
        // Guest details
        id: guests.id,
        guestNumber: guests.guestNumber,
        customerId: guests.customerId,
        dateOfBirth: guests.dateOfBirth,
        gender: guests.gender,
        nationality: guests.nationality,
        identificationType: guests.identificationType,
        identificationNumber: guests.identificationNumber,
        identificationIssueDate: guests.identificationIssueDate,
        identificationExpiryDate: guests.identificationExpiryDate,
        identificationIssuingCountry: guests.identificationIssuingCountry,
        guestType: guests.guestType,
        status: guests.status,
        key1: guests.key1,
        key2: guests.key2,
        key3: guests.key3,
        key4: guests.key4,
        key5: guests.key5,
        key6: guests.key6,
        key7: guests.key7,
        key8: guests.key8,
        key9: guests.key9,
        key10: guests.key10,
        lastServiceDate: guests.lastServiceDate,
        lastContactDate: guests.lastContactDate,
        notes: guests.notes,
        createdAt: guests.createdAt,
        updatedAt: guests.updatedAt,
        createdBy: guests.createdBy,
        updatedBy: guests.updatedBy,
        // Customer details
        customerDisplayName: customers.customerDisplayName,
        customerTitle: customers.title,
        customerFirstName: customers.firstName,
        customerMiddleName: customers.middleName,
        customerLastName: customers.lastName,
        customerSuffix: customers.suffix,
        customerCompanyName: customers.companyName,
        customerEmail: customers.email,
        customerPhoneNumber: customers.phoneNumber,
        customerMobileNumber: customers.mobileNumber,
        customerFax: customers.fax,
        customerWebsite: customers.website,
        customerOther: customers.other,
        customerStatus: customers.status,
        customerIsSubCustomer: customers.isSubCustomer,
      })
      .from(guests)
      .leftJoin(customers, eq(guests.customerId, customers.id))
      .where(and(...whereConditions));

    // Get total count for pagination
    const countQuery = this.db
      .select({ count: sql<number>`count(*)` })
      .from(guests)
      .leftJoin(customers, eq(guests.customerId, customers.id))
      .where(and(...whereConditions));

    // Execute queries
    const [guestResults, countResult] = await Promise.all([
      query
        .limit(limit)
        .offset(offset)
        .orderBy(...orderBy),
      countQuery,
    ]);

    const totalCount = Number(countResult[0].count);
    const totalPages = Math.ceil(totalCount / limit);

    // Format the response
    const formattedGuests = guestResults.map((guest) => ({
      id: guest.id,
      guestNumber: guest.guestNumber,
      customerId: guest.customerId,
      dateOfBirth: guest.dateOfBirth
        ? String(guest.dateOfBirth).split('T')[0]
        : undefined,
      gender: guest.gender,
      nationality: guest.nationality,
      identificationType: guest.identificationType,
      identificationNumber: guest.identificationNumber,
      identificationIssueDate: guest.identificationIssueDate
        ? String(guest.identificationIssueDate).split('T')[0]
        : undefined,
      identificationExpiryDate: guest.identificationExpiryDate
        ? String(guest.identificationExpiryDate).split('T')[0]
        : undefined,
      identificationIssuingCountry: guest.identificationIssuingCountry,
      guestType: guest.guestType,
      status: guest.status,
      key1: guest.key1,
      key2: guest.key2,
      key3: guest.key3,
      key4: guest.key4,
      key5: guest.key5,
      key6: guest.key6,
      key7: guest.key7,
      key8: guest.key8,
      key9: guest.key9,
      key10: guest.key10,
      lastServiceDate: guest.lastServiceDate
        ? String(guest.lastServiceDate).split('T')[0]
        : undefined,
      lastContactDate: guest.lastContactDate
        ? String(guest.lastContactDate).split('T')[0]
        : undefined,
      notes: guest.notes,
      customer: {
        id: guest.customerId || '',
        customerDisplayName: guest.customerDisplayName || '',
        title: guest.customerTitle,
        firstName: guest.customerFirstName,
        middleName: guest.customerMiddleName,
        lastName: guest.customerLastName,
        suffix: guest.customerSuffix,
        companyName: guest.customerCompanyName,
        email: guest.customerEmail,
        phoneNumber: guest.customerPhoneNumber,
        mobileNumber: guest.customerMobileNumber,
        fax: guest.customerFax,
        website: guest.customerWebsite,
        other: guest.customerOther,
        status: guest.customerStatus || 'active',
        isSubCustomer: guest.customerIsSubCustomer || false,
      },
      createdAt: guest.createdAt.toISOString(),
      updatedAt: guest.updatedAt?.toISOString(),
      createdBy: guest.createdBy,
      updatedBy: guest.updatedBy,
    }));

    return {
      data: formattedGuests,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  /**
   * Check availability of accommodation units
   */
  async checkAvailability(
    businessId: string,
    checkInDate: string,
    checkOutDate: string,
    adults: number,
    children = 0,
    accommodationUnitIds?: string[],
  ): Promise<CheckAvailabilityResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Validate dates
    const checkIn = new Date(checkInDate);
    const checkOut = new Date(checkOutDate);

    if (checkIn >= checkOut) {
      throw new BadRequestException(
        'Check-out date must be after check-in date',
      );
    }

    if (checkIn < new Date()) {
      throw new BadRequestException('Check-in date cannot be in the past');
    }

    // Calculate number of nights
    const nights = Math.ceil(
      (checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24),
    );

    // Build base query for available units
    const whereConditions = [
      eq(accommodationUnits.businessId, businessId),
      eq(accommodationUnits.systemStatus, AccommodationUnitStatus.ACTIVE),
      gte(accommodationUnits.maxAdults, adults),
      gte(accommodationUnits.maxChildren, children),
      eq(accommodationUnits.isDeleted, false),
    ];

    // Filter by specific unit IDs if provided
    if (accommodationUnitIds && accommodationUnitIds.length > 0) {
      whereConditions.push(
        inArray(accommodationUnits.id, accommodationUnitIds),
      );
    }

    // Get all potentially available units
    const potentialUnits = await this.db
      .select({
        id: accommodationUnits.id,
        roomNumber: accommodationUnits.roomNumber,
        name: accommodationUnits.name,
        maxAdults: accommodationUnits.maxAdults,
        maxChildren: accommodationUnits.maxChildren,
        basePrice: accommodationUnits.basePrice,
        features: accommodationUnits.features,
        bedType: accommodationUnits.bedType,
        viewType: accommodationUnits.viewType,
        typeId: accommodationUnits.type,
        subTypeId: accommodationUnits.subType,
      })
      .from(accommodationUnits)
      .leftJoin(
        reservationTypes,
        eq(accommodationUnits.type, reservationTypes.id),
      )
      .where(and(...whereConditions));

    // Check for conflicting reservations
    const conflictingReservations = await this.db
      .select({
        accommodationUnitId: accommodationReservationUnits.accommodationUnitId,
      })
      .from(accommodationReservationUnits)
      .innerJoin(
        accommodationReservations,
        eq(
          accommodationReservationUnits.reservationId,
          accommodationReservations.id,
        ),
      )
      .where(
        and(
          eq(accommodationReservations.businessId, businessId),
          inArray(accommodationReservations.status, [
            AccommodationReservationStatus.CONFIRMED,
            AccommodationReservationStatus.CHECKED_IN,
          ]),
          eq(accommodationReservations.isDeleted, false),
          // Check for date overlap
          or(
            and(
              lte(accommodationReservations.checkInDate, checkIn),
              gte(accommodationReservations.checkOutDate, checkIn),
            ),
            and(
              lte(accommodationReservations.checkInDate, checkOut),
              gte(accommodationReservations.checkOutDate, checkOut),
            ),
            and(
              gte(accommodationReservations.checkInDate, checkIn),
              lte(accommodationReservations.checkOutDate, checkOut),
            ),
          ),
        ),
      );

    // Get conflicting unit IDs
    const conflictingUnitIds = new Set(
      conflictingReservations.map((r) => r.accommodationUnitId),
    );

    // Filter out conflicting units
    const availableUnits: AvailableUnitDto[] = potentialUnits
      .filter((unit) => !conflictingUnitIds.has(unit.id))
      .map((unit) => {
        const totalCost = (parseFloat(unit.basePrice) * nights).toFixed(2);

        return {
          id: unit.id,
          name: unit.name || `Room ${unit.roomNumber}`,
          unitCode: unit.roomNumber || unit.id.substring(0, 8),
          unitType: 'Standard Room', // This would come from reservation types join
          maxAdults: unit.maxAdults,
          maxChildren: unit.maxChildren,
          baseRate: unit.basePrice,
          totalCost,
          nights,
          amenities: unit.features
            ? Object.keys(unit.features).filter((key) => unit.features[key])
            : [],
          images: [], // This would require joining with media table
        };
      });

    return {
      checkInDate,
      checkOutDate,
      nights,
      adults,
      children,
      availableUnits,
      totalAvailable: availableUnits.length,
    };
  }

  /**
   * Confirm accommodation reservation
   */
  async confirmReservation(
    userId: string,
    businessId: string,
    reservationId: string,
    confirmDto?: ConfirmReservationDto,
    metadata?: ActivityMetadata,
  ): Promise<ConfirmReservationResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if reservation exists and can be confirmed
    const reservation = await this.db
      .select({
        id: accommodationReservations.id,
        reservationNumber: accommodationReservations.reservationNumber,
        status: accommodationReservations.status,
      })
      .from(accommodationReservations)
      .where(
        and(
          eq(accommodationReservations.id, reservationId),
          eq(accommodationReservations.businessId, businessId),
          eq(accommodationReservations.isDeleted, false),
        ),
      )
      .limit(1);

    if (!reservation.length) {
      throw new NotFoundException('Accommodation reservation not found');
    }

    const currentReservation = reservation[0];

    // Check if reservation can be confirmed
    if (
      currentReservation.status === AccommodationReservationStatus.CONFIRMED
    ) {
      throw new BadRequestException('Reservation is already confirmed');
    }

    if (
      currentReservation.status === AccommodationReservationStatus.CANCELLED
    ) {
      throw new BadRequestException('Cannot confirm a cancelled reservation');
    }

    if (
      currentReservation.status === AccommodationReservationStatus.CHECKED_OUT
    ) {
      throw new BadRequestException('Cannot confirm a checked-out reservation');
    }

    // Update reservation status
    const updateData: any = {
      status: AccommodationReservationStatus.CONFIRMED,
      updatedBy: userId,
      updatedAt: new Date(),
    };

    if (confirmDto?.notes) {
      updateData.notes = confirmDto.notes;
    }

    await this.db
      .update(accommodationReservations)
      .set(updateData)
      .where(eq(accommodationReservations.id, reservationId));

    // Log activity
    await this.activityLogService.logUpdate(
      reservationId,
      EntityType.RESERVATION,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    // TODO: Send confirmation email/SMS if requested
    const confirmationSent = confirmDto?.sendConfirmation !== false;

    return {
      id: reservationId,
      reservationNumber: currentReservation.reservationNumber,
      status: AccommodationReservationStatus.CONFIRMED,
      confirmedAt: new Date().toISOString(),
      confirmationSent,
      message: 'Reservation confirmed successfully',
    };
  }

  /**
   * Cancel accommodation reservation
   */
  async cancelReservation(
    userId: string,
    businessId: string,
    reservationId: string,
    cancelDto: CancelReservationDto,
    metadata?: ActivityMetadata,
  ): Promise<CancelReservationResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if reservation exists and can be cancelled
    const reservation = await this.db
      .select({
        id: accommodationReservations.id,
        reservationNumber: accommodationReservations.reservationNumber,
        status: accommodationReservations.status,
      })
      .from(accommodationReservations)
      .where(
        and(
          eq(accommodationReservations.id, reservationId),
          eq(accommodationReservations.businessId, businessId),
          eq(accommodationReservations.isDeleted, false),
        ),
      )
      .limit(1);

    if (!reservation.length) {
      throw new NotFoundException('Accommodation reservation not found');
    }

    const currentReservation = reservation[0];

    // Check if reservation can be cancelled
    if (
      currentReservation.status === AccommodationReservationStatus.CANCELLED
    ) {
      throw new BadRequestException('Reservation is already cancelled');
    }

    if (
      currentReservation.status === AccommodationReservationStatus.CHECKED_OUT
    ) {
      throw new BadRequestException('Cannot cancel a checked-out reservation');
    }

    // Update reservation status
    const updateData: any = {
      status: AccommodationReservationStatus.CANCELLED,
      updatedBy: userId,
      updatedAt: new Date(),
    };

    if (cancelDto.notes) {
      updateData.notes = cancelDto.notes;
    }

    await this.db
      .update(accommodationReservations)
      .set(updateData)
      .where(eq(accommodationReservations.id, reservationId));

    // Log activity
    await this.activityLogService.logUpdate(
      reservationId,
      EntityType.RESERVATION,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    // TODO: Process refund if requested
    const refundProcessed = cancelDto.processRefund === true;

    // TODO: Send cancellation notification if requested
    const notificationSent = cancelDto.sendNotification !== false;

    return {
      id: reservationId,
      reservationNumber: currentReservation.reservationNumber,
      status: AccommodationReservationStatus.CANCELLED,
      cancelledAt: new Date().toISOString(),
      cancellationReason: cancelDto.cancellationReason,
      refundProcessed,
      notificationSent,
      message: 'Reservation cancelled successfully',
    };
  }

  /**
   * Calculate pricing for accommodation reservation
   */
  async calculatePricing(
    businessId: string,
    calculatePricingDto: CalculateAccommodationPricingDto,
  ): Promise<CalculateAccommodationPricingResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const {
      checkInDate,
      checkOutDate,
      accommodationUnits: requestedUnits,
    } = calculatePricingDto;

    // Validate dates
    const checkIn = new Date(checkInDate);
    const checkOut = new Date(checkOutDate);

    if (checkIn >= checkOut) {
      throw new BadRequestException(
        'Check-out date must be after check-in date',
      );
    }

    // Calculate number of nights
    const nights = Math.ceil(
      (checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24),
    );

    // Get unit details
    const unitIds = requestedUnits.map((unit) => unit.accommodationUnitId);
    const unitDetails = await this.db
      .select({
        id: accommodationUnits.id,
        name: accommodationUnits.name,
        roomNumber: accommodationUnits.roomNumber,
        basePrice: accommodationUnits.basePrice,
        maxAdults: accommodationUnits.maxAdults,
        maxChildren: accommodationUnits.maxChildren,
      })
      .from(accommodationUnits)
      .where(
        and(
          eq(accommodationUnits.businessId, businessId),
          inArray(accommodationUnits.id, unitIds),
          eq(accommodationUnits.isDeleted, false),
        ),
      );

    if (unitDetails.length !== unitIds.length) {
      throw new NotFoundException('One or more accommodation units not found');
    }

    // Calculate pricing for each unit
    const unitBreakdown: UnitPricingBreakdownDto[] = [];
    let totalSubtotal = 0;
    let totalAdults = 0;
    let totalChildren = 0;

    for (const unitRequest of requestedUnits) {
      const unitDetail = unitDetails.find(
        (u) => u.id === unitRequest.accommodationUnitId,
      );
      if (!unitDetail) continue;

      // Validate occupancy
      if (unitRequest.adults > unitDetail.maxAdults) {
        throw new BadRequestException(
          `Unit ${unitDetail.name || unitDetail.roomNumber} exceeds maximum adults capacity (${unitDetail.maxAdults})`,
        );
      }

      if ((unitRequest.children || 0) > unitDetail.maxChildren) {
        throw new BadRequestException(
          `Unit ${unitDetail.name || unitDetail.roomNumber} exceeds maximum children capacity (${unitDetail.maxChildren})`,
        );
      }

      const baseRatePerNight = parseFloat(unitDetail.basePrice);
      const unitSubtotal = baseRatePerNight * nights;

      // Simple daily rate breakdown (same rate for all days)
      const dailyRates: Record<string, string> = {};
      for (let i = 0; i < nights; i++) {
        const date = new Date(checkIn);
        date.setDate(date.getDate() + i);
        const dateStr = date.toISOString().split('T')[0];
        dailyRates[dateStr] = unitDetail.basePrice;
      }

      // Determine meal period for this unit
      const unitMealPeriod =
        unitRequest.mealPeriod ||
        calculatePricingDto.defaultMealPeriod ||
        MealPeriod.ROOM_ONLY;
      const mealPeriodCost = 0; // TODO: Calculate actual meal period costs

      unitBreakdown.push({
        accommodationUnitId: unitDetail.id,
        unitName: unitDetail.name || `Room ${unitDetail.roomNumber}`,
        baseRatePerNight: unitDetail.basePrice,
        nights,
        unitSubtotal: unitSubtotal.toFixed(2),
        unitDiscountAmount: '0.00', // TODO: Calculate discounts
        unitTaxAmount: '0.00', // TODO: Calculate taxes
        unitTotal: unitSubtotal.toFixed(2),
        unitMealPeriod: unitMealPeriod,
        unitMealPeriodCost: mealPeriodCost.toFixed(2),
        dailyRates,
      });

      totalSubtotal += unitSubtotal;
      totalAdults += unitRequest.adults;
      totalChildren += unitRequest.children || 0;
    }

    // Apply discounts (simplified)
    let totalDiscountAmount = 0;
    if (calculatePricingDto.discountType && calculatePricingDto.discountValue) {
      const discountValue = parseFloat(calculatePricingDto.discountValue);
      if (calculatePricingDto.discountType === 'PERCENTAGE') {
        totalDiscountAmount = (totalSubtotal * discountValue) / 100;
      } else {
        totalDiscountAmount = discountValue;
      }
    }

    // Calculate taxes (simplified)
    const taxableAmount = totalSubtotal - totalDiscountAmount;
    const taxRate = 10; // Default 10% tax rate
    const totalTaxAmount = (taxableAmount * taxRate) / 100;

    const finalTotal = taxableAmount + totalTaxAmount;

    return {
      checkInDate,
      checkOutDate,
      nights,
      totalAdults,
      totalChildren,
      unitBreakdown,
      subtotal: totalSubtotal.toFixed(2),
      totalDiscountAmount: totalDiscountAmount.toFixed(2),
      totalTaxAmount: totalTaxAmount.toFixed(2),
      total: finalTotal.toFixed(2),
      appliedDiscount:
        calculatePricingDto.discountType && calculatePricingDto.discountValue
          ? {
              type: calculatePricingDto.discountType,
              value: calculatePricingDto.discountValue,
              reason: calculatePricingDto.discountReason,
            }
          : undefined,
      taxBreakdown: {
        taxType: calculatePricingDto.taxType || 'INCLUSIVE',
        taxRate: taxRate.toFixed(2),
        taxableAmount: taxableAmount.toFixed(2),
      },
    };
  }

  /**
   * Check-in guest for accommodation reservation
   */
  async checkIn(
    userId: string,
    businessId: string,
    reservationId: string,
    checkInDto?: CheckInDto,
    metadata?: ActivityMetadata,
  ): Promise<CheckInResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if reservation exists and can be checked in
    const reservation = await this.db
      .select({
        id: accommodationReservations.id,
        reservationNumber: accommodationReservations.reservationNumber,
        status: accommodationReservations.status,
        checkInDate: accommodationReservations.checkInDate,
      })
      .from(accommodationReservations)
      .where(
        and(
          eq(accommodationReservations.id, reservationId),
          eq(accommodationReservations.businessId, businessId),
          eq(accommodationReservations.isDeleted, false),
        ),
      )
      .limit(1);

    if (!reservation.length) {
      throw new NotFoundException('Accommodation reservation not found');
    }

    const currentReservation = reservation[0];

    // Check if reservation can be checked in
    if (
      currentReservation.status === AccommodationReservationStatus.CHECKED_IN
    ) {
      throw new BadRequestException('Reservation is already checked in');
    }

    if (
      currentReservation.status === AccommodationReservationStatus.CANCELLED
    ) {
      throw new BadRequestException('Cannot check in a cancelled reservation');
    }

    if (
      currentReservation.status === AccommodationReservationStatus.CHECKED_OUT
    ) {
      throw new BadRequestException(
        'Cannot check in a checked-out reservation',
      );
    }

    // Use actual check-in date or current time
    const actualCheckInDate = checkInDto?.actualCheckInDate
      ? new Date(checkInDto.actualCheckInDate)
      : new Date();

    // Update reservation status
    const updateData: any = {
      status: AccommodationReservationStatus.CHECKED_IN,
      actualCheckInDate,
      updatedBy: userId,
      updatedAt: new Date(),
    };

    if (checkInDto?.notes) {
      updateData.notes = checkInDto.notes;
    }

    if (checkInDto?.specialRequests) {
      updateData.specialRequests = checkInDto.specialRequests;
    }

    await this.db
      .update(accommodationReservations)
      .set(updateData)
      .where(eq(accommodationReservations.id, reservationId));

    // Get guest count (simplified - would need to join with guests table)
    const guestsCheckedIn = 1; // Placeholder

    // Log activity
    await this.activityLogService.logUpdate(
      reservationId,
      EntityType.RESERVATION,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    // TODO: Send welcome message if requested
    const welcomeMessageSent = checkInDto?.sendWelcomeMessage !== false;

    return {
      id: reservationId,
      reservationNumber: currentReservation.reservationNumber,
      status: AccommodationReservationStatus.CHECKED_IN,
      checkedInAt: actualCheckInDate.toISOString(),
      guestsCheckedIn,
      welcomeMessageSent,
      message: 'Guests checked in successfully',
    };
  }

  /**
   * Check-out guest for accommodation reservation
   */
  async checkOut(
    userId: string,
    businessId: string,
    reservationId: string,
    checkOutDto?: CheckOutDto,
    metadata?: ActivityMetadata,
  ): Promise<CheckOutResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if reservation exists and can be checked out
    const reservation = await this.db
      .select({
        id: accommodationReservations.id,
        reservationNumber: accommodationReservations.reservationNumber,
        status: accommodationReservations.status,
        checkOutDate: accommodationReservations.checkOutDate,
        total: accommodationReservations.total,
      })
      .from(accommodationReservations)
      .where(
        and(
          eq(accommodationReservations.id, reservationId),
          eq(accommodationReservations.businessId, businessId),
          eq(accommodationReservations.isDeleted, false),
        ),
      )
      .limit(1);

    if (!reservation.length) {
      throw new NotFoundException('Accommodation reservation not found');
    }

    const currentReservation = reservation[0];

    // Check if reservation can be checked out
    if (
      currentReservation.status === AccommodationReservationStatus.CHECKED_OUT
    ) {
      throw new BadRequestException('Reservation is already checked out');
    }

    if (
      currentReservation.status === AccommodationReservationStatus.CANCELLED
    ) {
      throw new BadRequestException('Cannot check out a cancelled reservation');
    }

    if (
      currentReservation.status !== AccommodationReservationStatus.CHECKED_IN
    ) {
      throw new BadRequestException(
        'Reservation must be checked in before checkout',
      );
    }

    // Use actual check-out date or current time
    const actualCheckOutDate = checkOutDto?.actualCheckOutDate
      ? new Date(checkOutDto.actualCheckOutDate)
      : new Date();

    // Calculate final total with additional charges
    const additionalCharges = checkOutDto?.additionalCharges
      ? parseFloat(checkOutDto.additionalCharges)
      : 0;
    const originalTotal = parseFloat(currentReservation.total);
    const finalTotal = originalTotal + additionalCharges;

    // Update reservation status
    const updateData: any = {
      status: AccommodationReservationStatus.CHECKED_OUT,
      actualCheckOutDate,
      total: finalTotal.toFixed(2),
      updatedBy: userId,
      updatedAt: new Date(),
    };

    if (checkOutDto?.notes) {
      updateData.notes = checkOutDto.notes;
    }

    if (checkOutDto?.additionalChargesDescription) {
      updateData.additionalChargesDescription =
        checkOutDto.additionalChargesDescription;
    }

    if (checkOutDto?.roomConditionRating) {
      updateData.roomConditionRating = checkOutDto.roomConditionRating;
    }

    await this.db
      .update(accommodationReservations)
      .set(updateData)
      .where(eq(accommodationReservations.id, reservationId));

    // Get guest count (simplified - would need to join with guests table)
    const guestsCheckedOut = 1; // Placeholder

    // Log activity
    await this.activityLogService.logUpdate(
      reservationId,
      EntityType.RESERVATION,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    // TODO: Process final payment if requested
    const finalPaymentProcessed = checkOutDto?.processFinalPayment === true;

    // TODO: Send feedback request if requested
    const feedbackRequestSent = checkOutDto?.sendFeedbackRequest !== false;

    return {
      id: reservationId,
      reservationNumber: currentReservation.reservationNumber,
      status: AccommodationReservationStatus.CHECKED_OUT,
      checkedOutAt: actualCheckOutDate.toISOString(),
      guestsCheckedOut,
      finalTotal: finalTotal.toFixed(2),
      additionalCharges: additionalCharges.toFixed(2),
      feedbackRequestSent,
      finalPaymentProcessed,
      message: 'Guests checked out successfully',
    };
  }

  /**
   * Add guest to accommodation reservation (simplified implementation)
   */
  async addGuest(
    userId: string,
    businessId: string,
    reservationId: string,
    addGuestDto: AddGuestToReservationDto,
  ): Promise<AddGuestToReservationResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // TODO: Implement full guest creation and linking logic
    // This is a simplified placeholder implementation

    const guestId = 'new-guest-id'; // Would be generated from actual guest creation
    const guestNumber = 'GUEST-2024-000001'; // Would be generated

    return {
      reservationId,
      reservationNumber: 'RES-2024-000001', // Would be fetched from reservation
      guest: {
        id: guestId,
        guestNumber,
        fullName: `${addGuestDto.guest.firstName} ${addGuestDto.guest.lastName}`,
        email: addGuestDto.guest.email,
        phoneNumber: addGuestDto.guest.phoneNumber,
        isPrimary: addGuestDto.isPrimary || false,
      },
      totalGuests: 2, // Would be calculated from actual guest count
      message: 'Guest added to reservation successfully',
    };
  }

  /**
   * Add multiple guests to accommodation reservation (simplified implementation)
   */
  async addMultipleGuests(
    userId: string,
    businessId: string,
    reservationId: string,
    addMultipleGuestsDto: AddMultipleGuestsToReservationDto,
  ): Promise<AddMultipleGuestsResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // TODO: Implement full multiple guest creation logic
    // This is a simplified placeholder implementation

    const guests: GuestAddedResponseDto[] = addMultipleGuestsDto.guests.map(
      (guest, index) => ({
        id: `guest-${index}`,
        guestNumber: `GUEST-2024-00000${index + 1}`,
        fullName: `${guest.firstName} ${guest.lastName}`,
        email: guest.email,
        phoneNumber: guest.phoneNumber,
        isPrimary: false,
      }),
    );

    return {
      reservationId,
      reservationNumber: 'RES-2024-000001',
      guests,
      guestsAdded: guests.length,
      totalGuests: guests.length + 1, // Including existing guests
      message: 'Guests added to reservation successfully',
    };
  }

  /**
   * Remove guest from accommodation reservation (simplified implementation)
   */
  async removeGuest(
    userId: string,
    businessId: string,
    reservationId: string,
    guestId: string,
  ): Promise<RemoveGuestResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // TODO: Implement full guest removal logic with validation
    // This is a simplified placeholder implementation

    return {
      reservationId,
      reservationNumber: 'RES-2024-000001',
      removedGuestId: guestId,
      remainingGuests: 1, // Would be calculated from actual guest count
      message: 'Guest removed from reservation successfully',
    };
  }

  /**
   * Send confirmation for accommodation reservation (simplified implementation)
   */
  async sendConfirmation(
    userId: string,
    businessId: string,
    reservationId: string,
    sendConfirmationDto?: SendConfirmationDto,
  ): Promise<SendConfirmationResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // TODO: Implement actual communication service integration
    // This is a simplified placeholder implementation

    const results: CommunicationResultDto[] = [
      {
        type: 'EMAIL' as any,
        success: true,
        recipient: '<EMAIL>',
        messageId: 'msg_123456',
      },
    ];

    return {
      reservationId,
      reservationNumber: 'RES-2024-000001',
      results,
      successCount: 1,
      failureCount: 0,
      guestsContacted: 1,
      message: 'Confirmation sent successfully',
    };
  }

  /**
   * Send reminder for accommodation reservation (simplified implementation)
   */
  async sendReminder(
    userId: string,
    businessId: string,
    reservationId: string,
    sendReminderDto?: SendReminderDto,
  ): Promise<SendReminderResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // TODO: Implement actual communication service integration
    // This is a simplified placeholder implementation

    const results: CommunicationResultDto[] = [
      {
        type: 'EMAIL' as any,
        success: true,
        recipient: '<EMAIL>',
        messageId: 'msg_789012',
      },
    ];

    return {
      reservationId,
      reservationNumber: 'RES-2024-000001',
      results,
      successCount: 1,
      failureCount: 0,
      guestsContacted: 1,
      message: 'Reminder sent successfully',
    };
  }

  /**
   * Process payment for accommodation reservation (simplified implementation)
   */
  async processPayment(
    userId: string,
    businessId: string,
    reservationId: string,
    paymentDto: ProcessPaymentDto,
  ): Promise<ProcessPaymentResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // TODO: Implement actual payment processing integration
    // This is a simplified placeholder implementation

    const payment: PaymentResultDto = {
      id: 'payment-123',
      amount: paymentDto.amount,
      paymentMethod: paymentDto.paymentMethod,
      status: 'COMPLETED',
      paymentReference: paymentDto.paymentReference || 'TXN_AUTO_123',
      paymentDate: new Date().toISOString(),
      currency: paymentDto.currency || 'USD',
    };

    return {
      reservationId,
      reservationNumber: 'RES-2024-000001',
      payment,
      reservationPaymentStatus: 'PARTIAL',
      totalPaid: '750.00',
      remainingBalance: '650.00',
      confirmationSent: paymentDto.sendConfirmation !== false,
      message: 'Payment processed successfully',
    };
  }

  /**
   * Process refund for accommodation reservation (simplified implementation)
   */
  async processRefund(
    userId: string,
    businessId: string,
    reservationId: string,
    refundDto: ProcessRefundDto,
  ): Promise<ProcessRefundResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // TODO: Implement actual refund processing integration
    // This is a simplified placeholder implementation

    const refund: RefundResultDto = {
      id: 'refund-123',
      amount: refundDto.amount,
      reason: refundDto.reason,
      status: 'PROCESSED',
      refundReference: 'REF_AUTO_123',
      refundDate: new Date().toISOString(),
      expectedProcessingDays: 3,
    };

    return {
      reservationId,
      reservationNumber: 'RES-2024-000001',
      refund,
      reservationPaymentStatus: 'REFUNDED',
      totalRefunded: refundDto.amount,
      notificationSent: refundDto.sendNotification !== false,
      message: 'Refund processed successfully',
    };
  }
}
