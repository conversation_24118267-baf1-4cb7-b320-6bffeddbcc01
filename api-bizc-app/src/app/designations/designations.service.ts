import {
  Injectable,
  Inject,
  NotFoundException,
  BadRequestException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { designations } from '../drizzle/schema/designations.schema';
import { users } from '../drizzle/schema/users.schema';
import { staffMembers } from '../drizzle/schema/staff.schema';
import { CreateDesignationDto } from './dto/create-designation.dto';
import { UpdateDesignationDto } from './dto/update-designation.dto';
import { DesignationDto, DesignationStatus } from './dto/designation.dto';
import { DesignationListDto } from './dto/designation-list.dto';
import { DesignationSlimDto } from './dto/designation-slim.dto';
import {
  and,
  eq,
  ilike,
  sql,
  gte,
  lte,
  desc,
  asc,
  or,
  isNull,
  inArray,
} from 'drizzle-orm';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { UsersService } from '../users/users.service';
import {
  EntityType,
  ActivityType,
  ActivitySource,
  ExecutionStrategy,
} from '../shared/types/activity.enum';
import { ActivityMetadata } from '../shared/types/activity-metadata.type';

@Injectable()
export class DesignationsService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly usersService: UsersService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createDesignationDto: CreateDesignationDto,
  ): Promise<DesignationDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a designation with the same name already exists for this business
      // Using ilike for case-insensitive comparison
      const existingDesignation = await this.db
        .select()
        .from(designations)
        .where(
          and(
            eq(designations.businessId, businessId),
            ilike(designations.name, createDesignationDto.name),
            eq(designations.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingDesignation) {
        throw new ConflictException(
          `A designation with the name '${createDesignationDto.name}' already exists for this business`,
        );
      }

      const [newDesignation] = await this.db
        .insert(designations)
        .values({
          businessId: businessId,
          name: createDesignationDto.name,
          parentId: createDesignationDto.parentId,
          description: createDesignationDto.description,
          status: createDesignationDto.status ?? DesignationStatus.ACTIVE,
          createdBy: userId,
        })
        .returning();

      // Log the designation creation activity
      await this.activityLogService.logCreate(
        newDesignation.id,
        EntityType.DESIGNATION,
        userId,
        businessId,
        {
          source: ActivitySource.WEB,
        },
      );

      return await this.mapToDesignationDto(newDesignation);
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create designation: ${error.message}`,
      );
    }
  }

  async bulkCreate(
    userId: string,
    businessId: string | null,
    createDesignationsDto: CreateDesignationDto[],
  ): Promise<DesignationDto[]> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!createDesignationsDto || createDesignationsDto.length === 0) {
        throw new BadRequestException('No designations provided for creation');
      }

      // Check for duplicate names within the request
      const requestNames = createDesignationsDto.map((dto) =>
        dto.name.toLowerCase(),
      );
      const duplicateNames = requestNames.filter(
        (name, index) => requestNames.indexOf(name) !== index,
      );
      if (duplicateNames.length > 0) {
        throw new BadRequestException(
          `Duplicate designation names found in request: ${duplicateNames.join(', ')}`,
        );
      }

      // Check if any designations with the same names already exist for this business
      const existingDesignations = await this.db
        .select()
        .from(designations)
        .where(
          and(
            eq(designations.businessId, businessId),
            sql`LOWER(${designations.name}) IN (${requestNames.map((name) => `'${name}'`).join(',')})`,
            eq(designations.isDeleted, false),
          ),
        );

      if (existingDesignations.length > 0) {
        const existingNames = existingDesignations.map((d) => d.name);
        throw new ConflictException(
          `Designations with the following names already exist: ${existingNames.join(', ')}`,
        );
      }

      const createdDesignations: DesignationDto[] = [];

      // Use a transaction to ensure all designations are created or none are
      await this.db.transaction(async (tx) => {
        for (const createDesignationDto of createDesignationsDto) {
          // Create the designation
          const [newDesignation] = await tx
            .insert(designations)
            .values({
              businessId,
              name: createDesignationDto.name,
              parentId: createDesignationDto.parentId,
              description: createDesignationDto.description,
              status: createDesignationDto.status ?? DesignationStatus.ACTIVE,
              createdBy: userId,
            })
            .returning();

          // Log the activity
          await this.activityLogService.logCreate(
            newDesignation.id,
            EntityType.DESIGNATION,
            userId,
            businessId,
            {
              reason: 'Bulk create operation',
              source: ActivitySource.WEB,
            },
          );

          createdDesignations.push(
            await this.mapToDesignationDto(newDesignation),
          );
        }
      });

      return createdDesignations;
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create designations: ${error.message}`,
      );
    }
  }

  async findAllOptimized(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    name?: string,
    status?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: DesignationListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    // Get user's activeBusinessId
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(designations.businessId, businessId),
      eq(designations.isDeleted, false),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(designations.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        // Add 23:59:59 to include the entire day
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(designations.createdAt, toDate));
      }
    }

    // Add name filtering if provided
    if (name) {
      whereConditions.push(ilike(designations.name, `%${name}%`));
    }

    // Add status filtering if provided
    if (status) {
      const statusArray = status.split(',').map((s) => s.trim());
      if (statusArray.length === 1) {
        whereConditions.push(
          eq(designations.status, statusArray[0] as DesignationStatus),
        );
      } else {
        whereConditions.push(
          or(
            ...statusArray.map((s) =>
              eq(designations.status, s as DesignationStatus),
            ),
          ),
        );
      }
    }

    // Handle advanced filters
    if (filters) {
      try {
        const parsedFilters = JSON.parse(filters);
        const filterConditions = [];

        for (const filter of parsedFilters) {
          const { id: fieldId, value, operator } = filter;

          if (!value && operator !== 'isEmpty' && operator !== 'isNotEmpty') {
            continue;
          }

          switch (fieldId) {
            case 'name':
              switch (operator) {
                case 'iLike':
                  filterConditions.push(ilike(designations.name, `%${value}%`));
                  break;
                case 'notILike':
                  filterConditions.push(
                    sql`NOT ${ilike(designations.name, `%${value}%`)}`,
                  );
                  break;
                case 'eq':
                  filterConditions.push(eq(designations.name, value));
                  break;
                case 'ne':
                  filterConditions.push(sql`${designations.name} != ${value}`);
                  break;
                case 'isEmpty':
                  filterConditions.push(
                    or(eq(designations.name, ''), isNull(designations.name)),
                  );
                  break;
                case 'isNotEmpty':
                  filterConditions.push(
                    sql`${designations.name} IS NOT NULL AND ${designations.name} != ''`,
                  );
                  break;
              }
              break;
            case 'description':
              switch (operator) {
                case 'iLike':
                  filterConditions.push(
                    ilike(designations.description, `%${value}%`),
                  );
                  break;
                case 'notILike':
                  filterConditions.push(
                    sql`NOT ${ilike(designations.description, `%${value}%`)}`,
                  );
                  break;
                case 'eq':
                  filterConditions.push(eq(designations.description, value));
                  break;
                case 'ne':
                  filterConditions.push(
                    sql`${designations.description} != ${value}`,
                  );
                  break;
                case 'isEmpty':
                  filterConditions.push(
                    or(
                      eq(designations.description, ''),
                      isNull(designations.description),
                    ),
                  );
                  break;
                case 'isNotEmpty':
                  filterConditions.push(
                    sql`${designations.description} IS NOT NULL AND ${designations.description} != ''`,
                  );
                  break;
              }
              break;
            case 'status':
              switch (operator) {
                case 'eq':
                  filterConditions.push(eq(designations.status, value));
                  break;
                case 'ne':
                  filterConditions.push(
                    sql`${designations.status} != ${value}`,
                  );
                  break;
              }
              break;
          }
        }

        if (filterConditions.length > 0) {
          if (joinOperator === 'or') {
            whereConditions.push(or(...filterConditions));
          } else {
            whereConditions.push(...filterConditions);
          }
        }
      } catch {
        throw new BadRequestException('Invalid filters format');
      }
    }

    // Handle sorting
    let orderBy = desc(designations.createdAt); // default sort
    if (sort) {
      try {
        const parsedSort = JSON.parse(sort);
        if (parsedSort.length > 0) {
          const sortField = parsedSort[0];
          const sortDirection = sortField.desc ? desc : asc;

          switch (sortField.id) {
            case 'name':
              orderBy = sortDirection(designations.name);
              break;
            case 'createdAt':
              orderBy = sortDirection(designations.createdAt);
              break;
            case 'updatedAt':
              orderBy = sortDirection(designations.updatedAt);
              break;
            case 'staffMembersCount':
            case 'subTypesCount':
              // These will be handled with post-query sorting since they are calculated fields
              // Use createdAt as default to maintain performance
              orderBy = desc(designations.createdAt);
              break;
            default:
              orderBy = desc(designations.createdAt);
          }
        }
      } catch {
        // If sort parsing fails, use default
        orderBy = desc(designations.createdAt);
      }
    }

    // Check if we need to sort by calculated fields
    const needsPostQuerySort =
      sort &&
      (() => {
        try {
          const parsedSort = JSON.parse(sort);
          return (
            parsedSort.length > 0 &&
            (parsedSort[0].id === 'staffMembersCount' ||
              parsedSort[0].id === 'subTypesCount')
          );
        } catch {
          return false;
        }
      })();

    // Find all designations for the user's active business with pagination
    const result = await this.db
      .select()
      .from(designations)
      .where(and(...whereConditions))
      .orderBy(orderBy)
      .limit(needsPostQuerySort ? undefined : limit)
      .offset(needsPostQuerySort ? undefined : offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(designations)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    // Note: Skipping view activity logging for performance reasons
    // await this.activityLogService.logView(...);

    // Get staff members counts for all designations
    const designationIds = result.map((d) => d.id);
    const staffCountsMap = await this.getStaffMembersCountForDesignations(
      designationIds,
      businessId,
    );

    // Get parent information
    const parentInfoQuery = await this.db
      .select({
        id: designations.id,
        parentId: designations.parentId,
        parentName: sql<string>`parent_designation.name`.as('parentName'),
      })
      .from(designations)
      .leftJoin(
        sql`${designations} parent_designation`,
        sql`${designations.parentId} = parent_designation.id`,
      )
      .where(
        and(
          inArray(designations.id, designationIds),
          eq(designations.isDeleted, false),
        ),
      );

    // Get sub-designations count for each designation
    const subTypesCountQuery = await this.db
      .select({
        parentId: designations.parentId,
        count: sql<number>`count(*)`.as('count'),
      })
      .from(designations)
      .where(
        and(
          inArray(designations.parentId, designationIds),
          eq(designations.isDeleted, false),
        ),
      )
      .groupBy(designations.parentId);

    // Create lookup maps
    const parentInfoMap = new Map(
      parentInfoQuery.map((p) => [p.id, p.parentName]),
    );
    const subTypesCountMap = new Map(
      subTypesCountQuery.map((s) => [s.parentId, s.count]),
    );

    // Map to DTOs with staff counts and parent names
    let data = await Promise.all(
      result.map((designation) =>
        this.mapToDesignationListDto(
          designation,
          staffCountsMap.get(designation.id) || 0,
          parentInfoMap.get(designation.id) || null,
          subTypesCountMap.get(designation.id) || 0,
        ),
      ),
    );

    // Handle post-query sorting for calculated fields
    if (needsPostQuerySort && sort) {
      try {
        const parsedSort = JSON.parse(sort);
        const sortField = parsedSort[0];
        const isDesc = sortField.desc === true;

        if (sortField.id === 'staffMembersCount') {
          data.sort((a, b) => {
            const diff = a.staffMembersCount - b.staffMembersCount;
            return isDesc ? -diff : diff;
          });
        } else if (sortField.id === 'subTypesCount') {
          data.sort((a, b) => {
            const diff = a.subTypesCount - b.subTypesCount;
            return isDesc ? -diff : diff;
          });
        }

        // Apply pagination after sorting
        data = data.slice(offset, offset + limit);
      } catch {
        // Invalid JSON, use unsorted data with pagination
        data = data.slice(offset, offset + limit);
      }
    }

    return {
      data,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findAll(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
  ): Promise<{
    data: DesignationDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    // Get user's activeBusinessId
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(designations.businessId, businessId),
      eq(designations.isDeleted, false),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(designations.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        // Add 23:59:59 to include the entire day
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(designations.createdAt, toDate));
      }
    }

    // Find all designations for the user's active business with pagination
    const result = await this.db
      .select()
      .from(designations)
      .where(and(...whereConditions))
      .orderBy(desc(designations.createdAt))
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(designations)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    // Note: Skipping view activity logging for performance reasons
    // await this.activityLogService.logView(...);

    // Map to DTOs
    const data = await Promise.all(
      result.map((designation) => this.mapToDesignationDto(designation)),
    );

    return {
      data,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findOne(userId: string, id: string): Promise<DesignationDto> {
    // Get the designation
    const designation = await this.db
      .select()
      .from(designations)
      .where(eq(designations.id, id))
      .then((results) => results[0]);

    if (!designation) {
      throw new NotFoundException(`Designation with ID ${id} not found`);
    }

    // Get user's activeBusinessId to verify permission
    const user = await this.db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (
      !user ||
      !user.activeBusinessId ||
      designation.businessId !== user.activeBusinessId
    ) {
      throw new UnauthorizedException(
        `You do not have access to this designation`,
      );
    }

    // Note: Skipping view activity logging for performance reasons
    // await this.activityLogService.logView(...);

    return await this.mapToDesignationDto(designation);
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateDesignationDto: UpdateDesignationDto,
  ): Promise<DesignationDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Get the existing designation
      const existingDesignation = await this.db
        .select()
        .from(designations)
        .where(eq(designations.id, id))
        .then((results) => results[0]);

      if (!existingDesignation) {
        throw new NotFoundException(`Designation with ID ${id} not found`);
      }

      // Check if the user has access to this designation
      if (existingDesignation.businessId !== businessId) {
        throw new UnauthorizedException(
          `You do not have access to this designation`,
        );
      }

      // If name is being updated, check for duplicates
      if (
        updateDesignationDto.name &&
        updateDesignationDto.name !== existingDesignation.name
      ) {
        const duplicateName = await this.db
          .select()
          .from(designations)
          .where(
            and(
              eq(designations.businessId, businessId),
              ilike(designations.name, updateDesignationDto.name),
              sql`${designations.id} != ${id}`,
              eq(designations.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (duplicateName) {
          throw new ConflictException(
            `A designation with the name '${updateDesignationDto.name}' already exists for this business`,
          );
        }
      }

      // Update the designation
      const [updatedDesignation] = await this.db
        .update(designations)
        .set({
          name: updateDesignationDto.name ?? existingDesignation.name,
          parentId:
            updateDesignationDto.parentId !== undefined
              ? updateDesignationDto.parentId
              : existingDesignation.parentId,
          description:
            updateDesignationDto.description !== undefined
              ? updateDesignationDto.description
              : existingDesignation.description,

          status: updateDesignationDto.status ?? existingDesignation.status,
          updatedAt: new Date(),
        })
        .where(eq(designations.id, id))
        .returning();

      // Log the activity
      await this.activityLogService.logUpdate(
        updatedDesignation.id,
        EntityType.DESIGNATION,
        userId,
        businessId,
        {
          source: ActivitySource.WEB,
        },
      );

      return await this.mapToDesignationDto(updatedDesignation);
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof ConflictException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update designation: ${error.message}`,
      );
    }
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<{ success: boolean; message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Get the existing designation
      const existingDesignation = await this.db
        .select()
        .from(designations)
        .where(eq(designations.id, id))
        .then((results) => results[0]);

      if (!existingDesignation) {
        throw new NotFoundException(`Designation with ID ${id} not found`);
      }

      // Check if the user has access to this designation
      if (existingDesignation.businessId !== businessId) {
        throw new UnauthorizedException(
          `You do not have access to this designation`,
        );
      }

      // Soft delete the designation by setting isDeleted and status to inactive
      await this.db
        .update(designations)
        .set({
          status: DesignationStatus.INACTIVE,
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(designations.id, id));

      // Log the activity
      await this.activityLogService.logDelete(
        existingDesignation.id,
        EntityType.DESIGNATION,
        userId,
        businessId,
        {
          source: ActivitySource.WEB,
        },
      );

      return {
        success: true,
        message: `Designation '${existingDesignation.name}' has been successfully deleted`,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to delete designation: ${error.message}`,
      );
    }
  }

  async checkNameAvailability(
    userId: string,
    businessId: string | null,
    name: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if a designation with this name already exists for this business
    const existingDesignation = await this.db
      .select()
      .from(designations)
      .where(
        and(
          eq(designations.businessId, businessId),
          ilike(designations.name, name),
          eq(designations.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    return {
      available: !existingDesignation,
    };
  }

  private async mapToDesignationDto(
    designation: typeof designations.$inferSelect,
  ): Promise<DesignationDto> {
    // Get staff members count for this designation
    const staffMembersCount = await this.getStaffMembersCountForDesignation(
      designation.id,
      designation.businessId,
    );

    // Get user names for createdBy and updatedBy
    const createdByName = await this.usersService.getUserName(
      designation.createdBy.toString(),
    );
    let updatedByName: string | undefined;
    if (designation.updatedBy) {
      updatedByName = await this.usersService.getUserName(
        designation.updatedBy.toString(),
      );
    }

    return {
      id: designation.id,
      businessId: designation.businessId,
      name: designation.name,
      parentId: designation.parentId?.toString(),
      description: designation.description || null,
      status: designation.status as DesignationStatus,
      createdBy: designation.createdBy,
      createdByName,
      updatedByName,
      staffMembersCount,
      deletedAt: null, // Always null since we only return non-deleted records
      createdAt: designation.createdAt,
      updatedAt: designation.updatedAt,
    };
  }

  private async mapToDesignationListDto(
    designation: typeof designations.$inferSelect,
    staffMembersCount?: number,
    parentName?: string | null,
    subTypesCount?: number,
  ): Promise<DesignationListDto> {
    // If staffMembersCount is not provided, fetch it
    const staffCount =
      staffMembersCount ??
      (await this.getStaffMembersCountForDesignation(
        designation.id,
        designation.businessId,
      ));

    // If subTypesCount is not provided, fetch it
    const subCount =
      subTypesCount ??
      (await this.getSubTypesCountForDesignation(
        designation.id,
        designation.businessId,
      ));

    return {
      id: designation.id,
      name: designation.name,
      parentName: parentName || null,
      description: designation.description || null,
      status: designation.status as DesignationStatus,
      subTypesCount: subCount,
      staffMembersCount: staffCount,
    };
  }

  async findAllSlim(
    userId: string,
    businessId: string | null,
  ): Promise<DesignationSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Find all active designations for the user's active business
    const result = await this.db
      .select({
        id: designations.id,
        name: designations.name,
      })
      .from(designations)
      .where(
        and(
          eq(designations.businessId, businessId),
          eq(designations.status, DesignationStatus.ACTIVE),
          eq(designations.isDeleted, false),
        ),
      )
      .orderBy(asc(designations.name));

    // Note: Skipping view activity logging for performance reasons
    // await this.activityLogService.logView(...);

    return result.map((designation) =>
      this.mapToDesignationSlimDto(designation),
    );
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createDesignationDto: CreateDesignationDto,
  ): Promise<{ id: string }> {
    const designation = await this.create(
      userId,
      businessId,
      createDesignationDto,
    );
    return { id: designation.id };
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createDesignationsDto: CreateDesignationDto[],
  ): Promise<{ ids: string[] }> {
    const designations = await this.bulkCreate(
      userId,
      businessId,
      createDesignationsDto,
    );
    return { ids: designations.map((d) => d.id) };
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateDesignationDto: UpdateDesignationDto,
  ): Promise<{ id: string }> {
    const designation = await this.update(
      userId,
      businessId,
      id,
      updateDesignationDto,
    );
    return { id: designation.id };
  }

  private mapToDesignationSlimDto(designation: {
    id: string;
    name: string;
  }): DesignationSlimDto {
    return {
      id: designation.id,
      name: designation.name,
    };
  }

  private async getStaffMembersCountForDesignation(
    designationId: string,
    businessId: string,
  ): Promise<number> {
    try {
      const result = await this.db
        .select({ count: sql<number>`count(*)` })
        .from(staffMembers)
        .where(
          and(
            eq(staffMembers.businessId, businessId),
            eq(staffMembers.designationId, designationId),
            eq(staffMembers.isDeleted, false),
          ),
        );

      return Number(result[0].count);
    } catch (error) {
      console.warn(
        `Failed to get staff members count for designation ${designationId}:`,
        error.message,
      );
      return 0;
    }
  }

  private async getSubTypesCountForDesignation(
    designationId: string,
    businessId: string,
  ): Promise<number> {
    try {
      const result = await this.db
        .select({ count: sql<number>`count(*)` })
        .from(designations)
        .where(
          and(
            eq(designations.businessId, businessId),
            eq(designations.parentId, designationId),
            eq(designations.isDeleted, false),
          ),
        );

      return Number(result[0].count);
    } catch (error) {
      console.warn(
        `Failed to get sub types count for designation ${designationId}:`,
        error.message,
      );
      return 0;
    }
  }

  private async getStaffMembersCountForDesignations(
    designationIds: string[],
    businessId: string,
  ): Promise<Map<string, number>> {
    try {
      if (designationIds.length === 0) {
        return new Map();
      }

      const results = await this.db
        .select({
          designationId: staffMembers.designationId,
          count: sql<number>`count(*)`.as('count'),
        })
        .from(staffMembers)
        .where(
          and(
            eq(staffMembers.businessId, businessId),
            inArray(staffMembers.designationId, designationIds),
            eq(staffMembers.isDeleted, false),
          ),
        )
        .groupBy(staffMembers.designationId);

      const countsMap = new Map<string, number>();

      // Initialize all designation IDs with 0
      designationIds.forEach((id) => countsMap.set(id, 0));

      // Set actual counts
      results.forEach((result) => {
        if (result.designationId) {
          countsMap.set(result.designationId, result.count);
        }
      });

      return countsMap;
    } catch (error) {
      console.warn(
        'Failed to get staff members count for designations:',
        error.message,
      );
      return new Map();
    }
  }

  async bulkUpdateDesignationHierarchy(
    userId: string,
    businessId: string | null,
    updates: { id: string; parentId?: string | null }[],
  ): Promise<{
    updated: number;
    failed: Array<{ id: string; error: string }>;
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!updates || updates.length === 0) {
        throw new BadRequestException('No hierarchy updates provided');
      }

      let updated = 0;
      const failed: Array<{ id: string; error: string }> = [];

      // Helper function to check for circular references
      const wouldCreateCircle = async (
        parentId: string,
        childId: string,
      ): Promise<boolean> => {
        if (parentId === childId) return true;

        let currentParentId = parentId;
        const visited = new Set<string>();

        while (currentParentId && !visited.has(currentParentId)) {
          visited.add(currentParentId);

          const parent = await this.db
            .select({ parentId: designations.parentId })
            .from(designations)
            .where(
              and(
                eq(designations.id, currentParentId),
                eq(designations.businessId, businessId),
                eq(designations.isDeleted, false),
              ),
            )
            .then((results) => results[0]);

          if (!parent) break;

          if (parent.parentId === childId) return true;
          currentParentId = parent.parentId;
        }

        return false;
      };

      await this.db.transaction(async (tx) => {
        for (const update of updates) {
          try {
            // Verify the designation exists and belongs to the business
            const designation = await tx
              .select()
              .from(designations)
              .where(
                and(
                  eq(designations.id, update.id),
                  eq(designations.businessId, businessId),
                  eq(designations.isDeleted, false),
                ),
              )
              .then((results) => results[0]);

            if (!designation) {
              failed.push({
                id: update.id,
                error: 'Designation not found',
              });
              continue;
            }

            // If setting a parent, verify it exists and won't create a circle
            if (update.parentId) {
              const parent = await tx
                .select()
                .from(designations)
                .where(
                  and(
                    eq(designations.id, update.parentId),
                    eq(designations.businessId, businessId),
                    eq(designations.isDeleted, false),
                  ),
                )
                .then((results) => results[0]);

              if (!parent) {
                failed.push({
                  id: update.id,
                  error: 'Parent designation not found',
                });
                continue;
              }

              // Check for circular reference
              if (await wouldCreateCircle(update.parentId, update.id)) {
                failed.push({
                  id: update.id,
                  error: 'Would create circular reference',
                });
                continue;
              }
            }

            await tx
              .update(designations)
              .set({
                parentId: update.parentId,
                updatedBy: userId,
                updatedAt: new Date(),
              })
              .where(eq(designations.id, update.id));

            updated++;
          } catch (error) {
            failed.push({
              id: update.id,
              error: error.message || 'Unknown error',
            });
          }
        }
      });

      // Log the activity
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_UPDATE,
        EntityType.DESIGNATION,
        updates.map((u) => u.id),
        { hierarchyUpdate: true },
        userId,
        businessId,
        {
          filterCriteria: { updates },
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: ActivitySource.WEB,
        },
      );

      return { updated, failed };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update designation hierarchy: ${error.message}`,
      );
    }
  }

  async findAllHierarchy(
    userId: string,
    businessId: string | null,
  ): Promise<{ id: string; name: string; parentId: string | null }[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Find all active designations for the user's active business
    const result = await this.db
      .select({
        id: designations.id,
        name: designations.name,
        parentId: designations.parentId,
      })
      .from(designations)
      .where(
        and(
          eq(designations.businessId, businessId),
          eq(designations.isDeleted, false),
        ),
      )
      .orderBy(asc(designations.name));

    // Note: Skipping view activity logging for performance reasons
    // await this.activityLogService.logView(...);

    return result.map((designation) => ({
      id: designation.id,
      name: designation.name,
      parentId: designation.parentId?.toString() || null,
    }));
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    designationIds: string[],
  ): Promise<{
    deleted: number;
    failed: Array<{ id: string; error: string }>;
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!designationIds || designationIds.length === 0) {
        throw new BadRequestException('No designation IDs provided');
      }

      let deleted = 0;
      const failed: Array<{ id: string; error: string }> = [];

      // Process each designation deletion
      await Promise.all(
        designationIds.map(async (designationId) => {
          try {
            // Check if designation exists and belongs to the business
            const designation = await this.db
              .select()
              .from(designations)
              .where(
                and(
                  eq(designations.id, designationId),
                  eq(designations.businessId, businessId),
                  eq(designations.isDeleted, false),
                ),
              )
              .then((results) => results[0]);

            if (!designation) {
              failed.push({
                id: designationId,
                error: 'Designation not found',
              });
              return;
            }

            // Soft delete the designation
            await this.db
              .update(designations)
              .set({
                isDeleted: true,
                updatedBy: userId,
                updatedAt: new Date(),
              })
              .where(eq(designations.id, designationId));

            deleted++;
          } catch (error) {
            failed.push({
              id: designationId,
              error: error.message || 'Unknown error',
            });
          }
        }),
      );

      // Log the activity
      const deletedIds = designationIds.filter(
        (id) => !failed.some((f) => f.id === id),
      );
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_DELETE,
        EntityType.DESIGNATION,
        deletedIds,
        { isDeleted: true },
        userId,
        businessId,
        {
          filterCriteria: { designationIds },
          failures: failed,
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: ActivitySource.WEB,
        },
      );

      return { deleted, failed };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk delete designations: ${error.message}`,
      );
    }
  }

  async bulkUpdateStatus(
    userId: string,
    businessId: string | null,
    designationIds: string[],
    status: DesignationStatus,
  ): Promise<{
    updated: number;
    failed: Array<{ id: string; error: string }>;
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!designationIds || designationIds.length === 0) {
        throw new BadRequestException('No designation IDs provided');
      }

      let updated = 0;
      const failed: Array<{ id: string; error: string }> = [];

      // Process each designation status update
      await Promise.all(
        designationIds.map(async (designationId) => {
          try {
            // Check if designation exists and belongs to the business
            const designation = await this.db
              .select()
              .from(designations)
              .where(
                and(
                  eq(designations.id, designationId),
                  eq(designations.businessId, businessId),
                  eq(designations.isDeleted, false),
                ),
              )
              .then((results) => results[0]);

            if (!designation) {
              failed.push({
                id: designationId,
                error: 'Designation not found',
              });
              return;
            }

            // Update the designation status
            await this.db
              .update(designations)
              .set({
                status: status,
                updatedBy: userId,
              })
              .where(eq(designations.id, designationId));

            updated++;
          } catch (error) {
            failed.push({
              id: designationId,
              error: error.message || 'Unknown error',
            });
          }
        }),
      );

      // Log the activity
      const updatedIds = designationIds.filter(
        (id) => !failed.some((f) => f.id === id),
      );
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_STATUS_CHANGE,
        EntityType.DESIGNATION,
        updatedIds,
        { status },
        userId,
        businessId,
        {
          filterCriteria: { designationIds, status },
          failures: failed,
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: ActivitySource.WEB,
        },
      );

      return { updated, failed };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk update designation status: ${error.message}`,
      );
    }
  }
}
