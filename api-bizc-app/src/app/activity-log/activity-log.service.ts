import { Injectable, Inject, NotFoundException, Logger } from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { activityLogs } from '../drizzle/schema/activity-log.schema';
import {
  ActivityType,
  EntityType,
  ActivitySource,
  OperationType,
  DeleteType,
  TriggeredBy,
  PriorityImpact,
  ExecutionStrategy,
  FileFormat,
} from '../shared/types/activity.enum';
import { eq, and, desc, asc, gte, lte, count } from 'drizzle-orm';
import { CreateActivityLogDto } from './dto/create-activity-log.dto';
import { UpdateActivityLogDto } from './dto/update-activity-log.dto';
import { GetActivityLogsQueryDto } from './dto/get-activity-logs-query.dto';
import { ActivityLogDto } from './dto/activity-log.dto';
import { ActivityLogListDto } from './dto/activity-log-list.dto';
import { PaginatedActivityLogsResponseDto } from './dto/paginated-activity-logs-response.dto';

// Import our typed interfaces
import {
  BulkOperationChanges,
  StatusChangeChanges,
  PriorityChangeChanges,
  CreateChanges,
  AuthMetadata,
  BulkOperationMetadata,
  StatusChangeMetadata,
  PriorityChangeMetadata,
  ImportExportMetadata,
  isBulkOperation,
} from './interfaces/activity-log.interfaces';

@Injectable()
export class ActivityLogService {
  private readonly logger = new Logger(ActivityLogService.name);

  constructor(@Inject(DRIZZLE) private db: DrizzleDB) {}

  /**
   * Type-safe activity log creation for different activity types
   */
  async createTypedLog<T extends ActivityType>(
    activityType: T,
    data: {
      entityType: EntityType;
      entityId?: string;
      entityIds?: string[];
      userId: string;
      businessId?: string;
      source?: ActivitySource;
      changes: any; // Simplified type to avoid complex union issues
      metadata: any; // Simplified type to avoid complex union issues
      ipAddress?: string;
      userAgent?: string;
      sessionId?: string;
      duration?: number;
      affectedCount?: number;
    },
  ): Promise<ActivityLogDto> {
    try {
      const [result] = await this.db
        .insert(activityLogs)
        .values({
          entityType: data.entityType,
          entityId: data.entityId,
          entityIds: data.entityIds,
          businessId: data.businessId,
          activityType,
          source: data.source || ActivitySource.WEB,
          userId: data.userId,
          changes: data.changes,
          metadata: data.metadata,
          ipAddress: data.ipAddress,
          userAgent: data.userAgent,
          sessionId: data.sessionId,
          duration: data.duration,
          affectedCount: data.affectedCount,
        })
        .returning();

      return this.mapToActivityLogDto(result);
    } catch (error) {
      this.logger.error(`Failed to create ${activityType} activity log`, {
        error: error.message,
        activityType,
        entityType: data.entityType,
      });
      throw error;
    }
  }

  /**
   * Log a create operation with proper typing
   */
  async logCreate(
    entityId: string,
    entityType: EntityType,
    userId: string,
    businessId: string,
    options?: {
      reason?: string;
      source?: ActivitySource;
      ipAddress?: string;
      userAgent?: string;
      sessionId?: string;
      duration?: number;
    },
  ): Promise<ActivityLogDto> {
    const changes = {
      operation: OperationType.CREATE,
    };

    const metadata = {
      reason: options?.reason || 'Record created',
    };

    return this.createTypedLog(ActivityType.CREATE, {
      entityType,
      entityId,
      userId,
      businessId,
      source: options?.source || ActivitySource.WEB,
      changes,
      metadata,
      ipAddress: options?.ipAddress,
      userAgent: options?.userAgent,
      sessionId: options?.sessionId,
      duration: options?.duration,
      affectedCount: 1,
    });
  }

  /**
   * Log an update operation with proper typing
   */
  async logUpdate(
    entityId: string,
    entityType: EntityType,
    userId: string,
    businessId: string,
    options?: {
      source?: ActivitySource;
      ipAddress?: string;
      userAgent?: string;
      sessionId?: string;
      duration?: number;
    },
  ): Promise<ActivityLogDto> {
    return this.createTypedLog(ActivityType.UPDATE, {
      entityType,
      entityId,
      userId,
      businessId,
      source: options?.source || ActivitySource.WEB,
      changes: {
        operation: OperationType.UPDATE,
      },
      metadata: {
        timestamp: new Date(),
      },
      ipAddress: options?.ipAddress,
      userAgent: options?.userAgent,
      sessionId: options?.sessionId,
      duration: options?.duration,
      affectedCount: 1,
    });
  }

  /**
   * Log a delete operation with proper typing
   */
  async logDelete(
    entityId: string,
    entityType: EntityType,
    userId: string,
    businessId: string,
    options?: {
      reason?: string;
      source?: ActivitySource;
      deleteType?: DeleteType;
      relatedEntities?: Array<{ type: string; id: string; action: string }>;
      ipAddress?: string;
      userAgent?: string;
      sessionId?: string;
      duration?: number;
    },
  ): Promise<ActivityLogDto> {
    const isHardDelete = options?.deleteType === DeleteType.HARD;

    const changes = {
      operation: OperationType.DELETE,
      deleteType: options?.deleteType || DeleteType.SOFT,
      permanent: isHardDelete,
    };

    const metadata = {
      reason: options?.reason || 'Record deleted',
      deleteType: options?.deleteType || DeleteType.SOFT,
      relatedEntities: options?.relatedEntities,
      timestamp: new Date(),
      permanentDelete: isHardDelete,
    };

    return this.createTypedLog(ActivityType.DELETE, {
      entityType,
      entityId,
      userId,
      businessId,
      source: options?.source || ActivitySource.WEB,
      changes,
      metadata,
      ipAddress: options?.ipAddress,
      userAgent: options?.userAgent,
      sessionId: options?.sessionId,
      duration: options?.duration,
      affectedCount: 1,
    });
  }

  /**
   * Log a restore operation with proper typing
   */
  async logRestore(
    entityId: string,
    entityType: EntityType,
    userId: string,
    businessId: string,
    options?: {
      reason?: string;
      source?: ActivitySource;
      relatedEntities?: Array<{ type: string; id: string; action: string }>;
      ipAddress?: string;
      userAgent?: string;
      sessionId?: string;
      duration?: number;
    },
  ): Promise<ActivityLogDto> {
    const changes = {
      operation: OperationType.RESTORE,
      restored: true,
    };

    const metadata = {
      reason: options?.reason || 'Record restored',
      relatedEntities: options?.relatedEntities,
      timestamp: new Date(),
    };

    return this.createTypedLog(ActivityType.RESTORE, {
      entityType,
      entityId,
      userId,
      businessId,
      source: options?.source || ActivitySource.WEB,
      changes,
      metadata,
      ipAddress: options?.ipAddress,
      userAgent: options?.userAgent,
      sessionId: options?.sessionId,
      duration: options?.duration,
      affectedCount: 1,
    });
  }

  /**
   * Log a status change with proper typing
   */
  async logStatusChange(
    entityId: string,
    entityType: EntityType,
    previousStatus: string,
    newStatus: string,
    userId: string,
    businessId: string,
    options?: {
      reason?: string;
      triggeredBy?: TriggeredBy;
      relatedEntities?: Array<{ type: string; id: string; action: string }>;
      ipAddress?: string;
      userAgent?: string;
      sessionId?: string;
    },
  ): Promise<ActivityLogDto> {
    const changes: StatusChangeChanges = {
      before: { status: previousStatus },
      after: { status: newStatus },
      fields: ['status'],
    };

    const metadata: StatusChangeMetadata = {
      statusTransition: `${previousStatus} -> ${newStatus}`,
      previousStatus,
      newStatus,
      reason: options?.reason || 'Status updated',
      triggeredBy: options?.triggeredBy || TriggeredBy.MANUAL,
      source: ActivitySource.WEB,
      relatedEntities: options?.relatedEntities,
    };

    return this.createTypedLog(ActivityType.STATUS_CHANGE, {
      entityType,
      entityId,
      userId,
      businessId,
      source: ActivitySource.WEB,
      changes,
      metadata,
      ipAddress: options?.ipAddress,
      userAgent: options?.userAgent,
      sessionId: options?.sessionId,
    });
  }

  /**
   * Log a priority change with proper typing
   */
  async logPriorityChange(
    entityId: string,
    entityType: EntityType,
    previousPriority: number,
    newPriority: number,
    userId: string,
    businessId: string,
    options?: {
      reason?: string;
      affectedHierarchy?: boolean;
      cascadedTo?: string[];
      ipAddress?: string;
      userAgent?: string;
      sessionId?: string;
    },
  ): Promise<ActivityLogDto> {
    const changes: PriorityChangeChanges = {
      before: { priority: previousPriority },
      after: { priority: newPriority },
      fields: ['priority'],
    };

    const metadata: PriorityChangeMetadata = {
      priorityChange: `${previousPriority} -> ${newPriority}`,
      previousPriority,
      newPriority,
      impact:
        newPriority > previousPriority
          ? PriorityImpact.INCREASED
          : PriorityImpact.DECREASED,
      reason: options?.reason || 'Priority adjusted',
      affectedHierarchy: options?.affectedHierarchy,
      cascadedTo: options?.cascadedTo,
      source: ActivitySource.WEB,
    };

    return this.createTypedLog(ActivityType.PRIORITY_CHANGE, {
      entityType,
      entityId,
      userId,
      businessId,
      source: ActivitySource.WEB,
      changes,
      metadata,
      ipAddress: options?.ipAddress,
      userAgent: options?.userAgent,
      sessionId: options?.sessionId,
    });
  }

  /**
   * Log bulk operations with proper typing
   */
  async logBulkOperation(
    operationType: ActivityType,
    entityType: EntityType,
    affectedIds: string[],
    updates: any,
    userId: string,
    businessId: string,
    options: {
      filterCriteria: Record<string, any>;
      beforeSample?: any[];
      failures?: Array<{ id: string; error: string }>;
      executionStrategy?: ExecutionStrategy;
      source?: ActivitySource;
      ipAddress?: string;
      userAgent?: string;
      sessionId?: string;
      duration?: number;
    },
  ): Promise<ActivityLogDto> {
    const bulkOperationId = crypto.randomUUID();
    const successCount = affectedIds.length - (options.failures?.length || 0);

    const changes: BulkOperationChanges = {
      before: options.beforeSample
        ? { sample: options.beforeSample.slice(0, 3) }
        : undefined,
      after: updates,
      fields: Object.keys(updates),
    };

    const metadata: BulkOperationMetadata = {
      bulkOperationId,
      filterCriteria: options.filterCriteria,
      totalProcessed: affectedIds.length,
      successCount,
      failureCount: options.failures?.length || 0,
      failures: options.failures,
      updateFields: Object.keys(updates),
      executionStrategy:
        options.executionStrategy || ExecutionStrategy.PARALLEL,
      source: ActivitySource.WEB,
    };

    // Add specific metadata based on operation type
    let finalMetadata: any = metadata;
    if (operationType === ActivityType.BULK_STATUS_CHANGE && updates.status) {
      finalMetadata = {
        ...metadata,
        statusTransition: `* -> ${updates.status}`,
        newStatus: updates.status,
      };
    } else if (
      operationType === ActivityType.BULK_PRIORITY_CHANGE &&
      updates.priority !== undefined
    ) {
      finalMetadata = {
        ...metadata,
        priorityChange: `* -> ${updates.priority}`,
        newPriority: updates.priority,
        impact: PriorityImpact.BULK_UPDATE,
      };
    }

    return this.createTypedLog(operationType, {
      entityType,
      entityIds: affectedIds,
      userId,
      businessId,
      source: options.source || ActivitySource.WEB,
      changes,
      metadata: finalMetadata,
      affectedCount: affectedIds.length,
      ipAddress: options.ipAddress,
      userAgent: options.userAgent,
      sessionId: options.sessionId,
      duration: options.duration,
    });
  }

  /**
   * Log authentication events
   */
  async logAuthEvent(
    eventType: ActivityType,
    userId: string,
    options: {
      ipAddress: string;
      userAgent: string;
      sessionId?: string;
      failureReason?: string;
      mfaUsed?: boolean;
      sessionDuration?: number;
      deviceInfo?: {
        os?: string;
        browser?: string;
        device?: string;
      };
    },
  ): Promise<ActivityLogDto> {
    const metadata: AuthMetadata = {
      deviceInfo: options.deviceInfo,
      failureReason:
        eventType === ActivityType.LOGIN_FAILED
          ? options.failureReason
          : undefined,
      mfaUsed: eventType === ActivityType.LOGIN ? options.mfaUsed : undefined,
      sessionDuration:
        eventType === ActivityType.LOGOUT ? options.sessionDuration : undefined,
    };

    return this.createTypedLog(eventType, {
      entityType: EntityType.AUTH,
      entityId: userId,
      userId,
      source: this.detectSource(options.userAgent),
      changes: undefined as never,
      metadata,
      ipAddress: options.ipAddress,
      userAgent: options.userAgent,
      sessionId: options.sessionId,
    });
  }

  /**
   * Log import/export operations
   */
  async logImportExport(
    operationType: ActivityType,
    entityType: EntityType,
    userId: string,
    businessId: string,
    options: {
      fileName: string;
      fileSize: number;
      format: FileFormat;
      recordCount: number;
      importedData?: any[];
      exportFilters?: Record<string, any>;
      validationErrors?: Array<{
        row: number;
        field: string;
        error: string;
      }>;
      ipAddress?: string;
      userAgent?: string;
      sessionId?: string;
      duration?: number;
    },
  ): Promise<ActivityLogDto> {
    const metadata: ImportExportMetadata = {
      fileName: options.fileName,
      fileSize: options.fileSize,
      format: options.format,
      recordCount: options.recordCount,
      validationErrors: options.validationErrors,
      exportFilters: options.exportFilters,
      importId:
        operationType === ActivityType.IMPORT ? crypto.randomUUID() : undefined,
      source: ActivitySource.WEB,
    };

    const changes =
      operationType === ActivityType.IMPORT && options.importedData
        ? options.importedData.map((data) => ({ after: data }) as CreateChanges)
        : undefined;

    return this.createTypedLog(operationType, {
      entityType,
      userId,
      businessId,
      source: ActivitySource.WEB,
      changes: changes as any,
      metadata,
      affectedCount: options.recordCount,
      ipAddress: options.ipAddress,
      userAgent: options.userAgent,
      sessionId: options.sessionId,
      duration: options.duration,
    });
  }

  /**
   * Get typed activity logs with filtering
   */
  async findTypedLogs<T extends ActivityType>(
    businessId: string,
    activityType: T,
    options?: {
      entityType?: EntityType;
      entityId?: string;
      userId?: string;
      fromDate?: Date;
      toDate?: Date;
      sessionId?: string;
      limit?: number;
    },
  ): Promise<any[]> {
    const conditions = [
      eq(activityLogs.businessId, businessId),
      eq(activityLogs.activityType, activityType),
    ];

    if (options?.entityType) {
      conditions.push(eq(activityLogs.entityType, options.entityType));
    }
    if (options?.entityId) {
      conditions.push(eq(activityLogs.entityId, options.entityId));
    }
    if (options?.userId) {
      conditions.push(eq(activityLogs.userId, options.userId));
    }
    if (options?.sessionId) {
      conditions.push(eq(activityLogs.sessionId, options.sessionId));
    }
    if (options?.fromDate) {
      conditions.push(gte(activityLogs.createdAt, options.fromDate));
    }
    if (options?.toDate) {
      conditions.push(lte(activityLogs.createdAt, options.toDate));
    }

    const results = await this.db
      .select()
      .from(activityLogs)
      .where(and(...conditions))
      .orderBy(desc(activityLogs.createdAt))
      .limit(options?.limit || 100);

    return results;
  }

  /**
   * Get session activities
   */
  async getSessionActivities(sessionId: string): Promise<ActivityLogDto[]> {
    const results = await this.db
      .select()
      .from(activityLogs)
      .where(eq(activityLogs.sessionId, sessionId))
      .orderBy(asc(activityLogs.createdAt));

    return results.map((row) => this.mapToActivityLogDto(row));
  }

  /**
   * Detect suspicious activities
   */
  async detectSuspiciousActivities(
    userId: string,
    timeWindowMinutes: number = 60,
  ): Promise<{
    rapidBulkDeletes: boolean;
    failedLogins: number;
    unusualActivity: boolean;
    suspiciousPatterns: string[];
  }> {
    const since = new Date(Date.now() - timeWindowMinutes * 60 * 1000);

    const recentLogs = await this.db
      .select()
      .from(activityLogs)
      .where(
        and(
          eq(activityLogs.userId, userId),
          gte(activityLogs.createdAt, since),
        ),
      );

    const bulkDeletes = recentLogs.filter(
      (log) => log.activityType === ActivityType.BULK_DELETE,
    );
    const failedLogins = recentLogs.filter(
      (log) => log.activityType === ActivityType.LOGIN_FAILED,
    );
    const patterns: string[] = [];

    if (bulkDeletes.length > 3) {
      patterns.push(
        `${bulkDeletes.length} bulk delete operations in ${timeWindowMinutes} minutes`,
      );
    }

    if (failedLogins.length > 5) {
      patterns.push(`${failedLogins.length} failed login attempts`);
    }

    if (recentLogs.length > 100) {
      patterns.push(`High volume activity: ${recentLogs.length} actions`);
    }

    // Check for rapid status changes
    const statusChanges = recentLogs.filter(
      (log) =>
        log.activityType === ActivityType.STATUS_CHANGE ||
        log.activityType === ActivityType.BULK_STATUS_CHANGE,
    );
    if (statusChanges.length > 10) {
      patterns.push(`Frequent status changes: ${statusChanges.length} changes`);
    }

    return {
      rapidBulkDeletes: bulkDeletes.length > 3,
      failedLogins: failedLogins.length,
      unusualActivity: patterns.length > 0,
      suspiciousPatterns: patterns,
    };
  }

  /**
   * Get activity summary for analytics
   */
  async getActivitySummary(
    businessId: string,
    options?: {
      userId?: string;
      entityType?: EntityType;
      days?: number;
    },
  ): Promise<{
    byType: Record<ActivityType, number>;
    byEntity: Record<EntityType, number>;
    totalActivities: number;
    uniqueUsers: number;
    averageDuration: number;
    bulkOperationStats: {
      count: number;
      totalRecordsAffected: number;
      averageRecordsPerOperation: number;
    };
  }> {
    const conditions = [eq(activityLogs.businessId, businessId)];

    if (options?.userId) {
      conditions.push(eq(activityLogs.userId, options.userId));
    }
    if (options?.entityType) {
      conditions.push(eq(activityLogs.entityType, options.entityType));
    }
    if (options?.days) {
      const since = new Date();
      since.setDate(since.getDate() - options.days);
      conditions.push(gte(activityLogs.createdAt, since));
    }

    const results = await this.db
      .select()
      .from(activityLogs)
      .where(and(...conditions));

    // Calculate statistics
    const byType: Record<string, number> = {};
    const byEntity: Record<string, number> = {};
    const uniqueUserIds = new Set<string>();
    let totalDuration = 0;
    let durationCount = 0;
    const bulkOps = [];

    for (const log of results) {
      // Count by type
      byType[log.activityType] = (byType[log.activityType] || 0) + 1;

      // Count by entity
      byEntity[log.entityType] = (byEntity[log.entityType] || 0) + 1;

      // Track unique users
      uniqueUserIds.add(log.userId);

      // Calculate average duration
      if (log.duration) {
        totalDuration += log.duration;
        durationCount++;
      }

      // Collect bulk operations
      if (isBulkOperation(log.activityType)) {
        bulkOps.push(log);
      }
    }

    const totalBulkRecords = bulkOps.reduce(
      (sum, op) => sum + (op.affectedCount || 0),
      0,
    );

    return {
      byType: byType as Record<ActivityType, number>,
      byEntity: byEntity as Record<EntityType, number>,
      totalActivities: results.length,
      uniqueUsers: uniqueUserIds.size,
      averageDuration:
        durationCount > 0 ? Math.round(totalDuration / durationCount) : 0,
      bulkOperationStats: {
        count: bulkOps.length,
        totalRecordsAffected: totalBulkRecords,
        averageRecordsPerOperation:
          bulkOps.length > 0
            ? Math.round(totalBulkRecords / bulkOps.length)
            : 0,
      },
    };
  }

  /**
   * Get activity counts by entity type
   */
  async getActivityCounts(
    businessId: string,
  ): Promise<Record<EntityType, number>> {
    const results = await this.db
      .select({
        entityType: activityLogs.entityType,
        count: count(),
      })
      .from(activityLogs)
      .where(eq(activityLogs.businessId, businessId))
      .groupBy(activityLogs.entityType);

    const counts: Record<string, number> = {};
    for (const result of results) {
      counts[result.entityType] = result.count;
    }

    return counts as Record<EntityType, number>;
  }

  /**
   * Original methods maintained for backward compatibility
   */
  async create(
    createActivityLogDto: CreateActivityLogDto,
  ): Promise<ActivityLogDto> {
    return this.createTypedLog(createActivityLogDto.activityType, {
      entityType: createActivityLogDto.entityType,
      entityId: createActivityLogDto.entityId,
      entityIds: createActivityLogDto.entityIds,
      userId: createActivityLogDto.userId,
      businessId: createActivityLogDto.businessId,
      source: ActivitySource.WEB,
      changes: createActivityLogDto.changes || {},
      metadata: createActivityLogDto.metadata || {},
      ipAddress: createActivityLogDto.ipAddress,
      userAgent: createActivityLogDto.userAgent,
      sessionId: createActivityLogDto.sessionId,
      duration: createActivityLogDto.duration,
      affectedCount: createActivityLogDto.affectedCount,
    });
  }

  async findAll(
    businessId: string,
    query: GetActivityLogsQueryDto,
  ): Promise<PaginatedActivityLogsResponseDto> {
    const { page = 1, limit = 10, sort = 'createdAt:desc' } = query;
    const offset = (page - 1) * limit;

    const conditions = [eq(activityLogs.businessId, businessId)];

    if (query.entityType) {
      conditions.push(eq(activityLogs.entityType, query.entityType));
    }
    if (query.entityId) {
      conditions.push(eq(activityLogs.entityId, query.entityId));
    }
    if (query.activityType) {
      conditions.push(eq(activityLogs.activityType, query.activityType));
    }
    if (query.userId) {
      conditions.push(eq(activityLogs.userId, query.userId));
    }
    if (query.sessionId) {
      conditions.push(eq(activityLogs.sessionId, query.sessionId));
    }
    if (query.fromDate) {
      conditions.push(gte(activityLogs.createdAt, new Date(query.fromDate)));
    }
    if (query.toDate) {
      conditions.push(lte(activityLogs.createdAt, new Date(query.toDate)));
    }

    const [, sortDirection] = sort.split(':');
    const orderBy =
      sortDirection === 'asc'
        ? asc(activityLogs.createdAt)
        : desc(activityLogs.createdAt);

    try {
      const [{ total }] = await this.db
        .select({ total: count() })
        .from(activityLogs)
        .where(and(...conditions));

      const data = await this.db
        .select({
          id: activityLogs.id,
          entityType: activityLogs.entityType,
          entityId: activityLogs.entityId,
          activityType: activityLogs.activityType,
          userId: activityLogs.userId,
          ipAddress: activityLogs.ipAddress,
          createdAt: activityLogs.createdAt,
        })
        .from(activityLogs)
        .where(and(...conditions))
        .orderBy(orderBy)
        .limit(limit)
        .offset(offset);

      const totalPages = Math.ceil(total / limit);

      return {
        data: data.map((row) => this.mapToActivityLogListDto(row)),
        meta: {
          total,
          page,
          limit,
          totalPages,
        },
      };
    } catch (error) {
      this.logger.error('Failed to fetch activity logs', {
        error: error.message,
        businessId,
        query,
      });
      throw error;
    }
  }

  async findOne(id: string, businessId: string): Promise<ActivityLogDto> {
    try {
      const [result] = await this.db
        .select()
        .from(activityLogs)
        .where(
          and(eq(activityLogs.id, id), eq(activityLogs.businessId, businessId)),
        );

      if (!result) {
        throw new NotFoundException('Activity log not found');
      }

      return this.mapToActivityLogDto(result);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error('Failed to fetch activity log', {
        error: error.message,
        id,
        businessId,
      });
      throw error;
    }
  }

  async update(
    id: string,
    businessId: string,
    updateActivityLogDto: UpdateActivityLogDto,
  ): Promise<ActivityLogDto> {
    try {
      const [result] = await this.db
        .update(activityLogs)
        .set({
          metadata: updateActivityLogDto.metadata,
          // Only allow updating metadata for audit purposes
        })
        .where(
          and(eq(activityLogs.id, id), eq(activityLogs.businessId, businessId)),
        )
        .returning();

      if (!result) {
        throw new NotFoundException('Activity log not found');
      }

      return this.mapToActivityLogDto(result);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error('Failed to update activity log', {
        error: error.message,
        id,
        businessId,
      });
      throw error;
    }
  }

  async remove(id: string, businessId: string): Promise<ActivityLogDto> {
    try {
      const [result] = await this.db
        .delete(activityLogs)
        .where(
          and(eq(activityLogs.id, id), eq(activityLogs.businessId, businessId)),
        )
        .returning();

      if (!result) {
        throw new NotFoundException('Activity log not found');
      }

      return this.mapToActivityLogDto(result);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error('Failed to delete activity log', {
        error: error.message,
        id,
        businessId,
      });
      throw error;
    }
  }

  /**
   * Utility methods
   */
  private detectSource(userAgent?: string): ActivitySource {
    if (!userAgent) return ActivitySource.API;
    if (userAgent.includes('Mobile')) return ActivitySource.MOBILE;
    if (userAgent.includes('Mozilla')) return ActivitySource.WEB;
    return ActivitySource.API;
  }

  private mapToActivityLogDto(row: any): ActivityLogDto {
    return {
      id: row.id,
      entityType: row.entityType,
      entityId: row.entityId,
      entityIds: row.entityIds,
      businessId: row.businessId,
      activityType: row.activityType,
      source: row.source,
      userId: row.userId,
      changes: row.changes,
      metadata: row.metadata,
      ipAddress: row.ipAddress,
      userAgent: row.userAgent,
      sessionId: row.sessionId,
      duration: row.duration,
      affectedCount: row.affectedCount,
      createdAt: row.createdAt,
    };
  }

  private mapToActivityLogListDto(row: any): ActivityLogListDto {
    return {
      id: row.id,
      entityType: row.entityType,
      entityId: row.entityId,
      activityType: row.activityType,
      userId: row.userId,
      ipAddress: row.ipAddress,
      createdAt: row.createdAt,
    };
  }
}
