import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  activityTypeEnum,
  entityTypeEnum,
  activitySourceEnum,
} from '../../drizzle/schema/activity-log.schema';
import {
  ActivityType,
  EntityType,
  ActivitySource,
} from '../../shared/types/activity.enum';

export class ActivityLogDto {
  @ApiProperty({
    description: 'Unique identifier for the activity log',
    example: '550e8400-e29b-41d4-a716-************',
  })
  id: string;

  @ApiProperty({
    description: 'Type of entity being acted upon',
    enum: entityTypeEnum.enumValues,
    example: 'PRODUCT',
  })
  entityType: EntityType;

  @ApiPropertyOptional({
    description: 'UUID of the entity being acted upon (for single operations)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  entityId?: string;

  @ApiPropertyOptional({
    description: 'Array of entity UUIDs (for bulk operations)',
    type: [String],
    example: [
      '550e8400-e29b-41d4-a716-************',
      '550e8400-e29b-41d4-a716-************',
    ],
  })
  entityIds?: string[];

  @ApiProperty({
    description: 'Type of activity performed',
    enum: activityTypeEnum.enumValues,
    example: 'CREATE',
  })
  activityType: ActivityType;

  @ApiProperty({
    description: 'Source of the activity (web, mobile, api, etc.)',
    enum: activitySourceEnum.enumValues,
    example: 'web',
  })
  source: ActivitySource;

  @ApiProperty({
    description: 'UUID of the user who performed the activity',
    example: '550e8400-e29b-41d4-a716-************',
  })
  userId: string;

  @ApiPropertyOptional({
    description:
      'UUID of the business this activity belongs to (nullable for AUTH operations)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  businessId?: string;

  @ApiPropertyOptional({
    description: 'Before/after values for tracking changes',
    type: 'object',
    additionalProperties: true,
    example: {
      before: { name: 'Old Product Name', price: '10.00' },
      after: { name: 'New Product Name', price: '15.00' },
      fields: ['name', 'price'],
    },
  })
  changes?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Number of records affected (for bulk operations)',
    example: 5,
  })
  affectedCount?: number;

  @ApiPropertyOptional({
    description: 'Additional context and metadata for the activity',
    type: 'object',
    additionalProperties: true,
    example: {
      source: 'web',
      reason: 'User requested update',
      bulkOperationId: '550e8400-e29b-41d4-a716-************',
    },
  })
  metadata?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'IP address of the user who performed the activity',
    example: '***********',
  })
  ipAddress?: string;

  @ApiPropertyOptional({
    description: 'User agent string from the client',
    example: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  })
  userAgent?: string;

  @ApiPropertyOptional({
    description: 'Session ID to track activities within the same session',
    example: 'sess_550e8400-e29b-41d4-a716-446655440004',
  })
  sessionId?: string;

  @ApiPropertyOptional({
    description: 'Operation duration in milliseconds',
    example: 150,
  })
  duration?: number;

  @ApiProperty({
    description: 'When the activity was performed',
    example: '2024-01-01T12:00:00.000Z',
  })
  createdAt: Date;
}
