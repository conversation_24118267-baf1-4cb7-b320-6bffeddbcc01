import {
  IsEnum,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
  IsUUID,
  IsIP,
  IsArray,
  <PERSON><PERSON>nt,
  <PERSON>,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  activityTypeEnum,
  entityTypeEnum,
} from '../../drizzle/schema/activity-log.schema';
import { ActivityType, EntityType } from '../../shared/types/activity.enum';

export class CreateActivityLogDto {
  @ApiProperty({
    description: 'Type of entity being acted upon',
    enum: entityTypeEnum.enumValues,
    example: 'PRODUCT',
  })
  @IsNotEmpty()
  @IsEnum(entityTypeEnum.enumValues)
  entityType: EntityType;

  @ApiPropertyOptional({
    description: 'UUID of the entity being acted upon (for single operations)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID()
  entityId?: string;

  @ApiPropertyOptional({
    description: 'Array of entity UUIDs (for bulk operations)',
    type: [String],
    example: [
      '550e8400-e29b-41d4-a716-************',
      '550e8400-e29b-41d4-a716-************',
    ],
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  entityIds?: string[];

  @ApiProperty({
    description: 'Type of activity performed',
    enum: activityTypeEnum.enumValues,
    example: 'CREATE',
  })
  @IsNotEmpty()
  @IsEnum(activityTypeEnum.enumValues)
  activityType: ActivityType;

  @ApiProperty({
    description: 'UUID of the user who performed the activity',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsNotEmpty()
  @IsUUID()
  userId: string;

  @ApiPropertyOptional({
    description:
      'UUID of the business this activity belongs to (nullable for AUTH operations)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID()
  businessId?: string;

  @ApiPropertyOptional({
    description: 'Before/after values for tracking changes',
    type: 'object',
    additionalProperties: true,
    example: {
      before: { name: 'Old Product Name', price: '10.00' },
      after: { name: 'New Product Name', price: '15.00' },
      fields: ['name', 'price'],
    },
  })
  @IsOptional()
  @IsObject()
  changes?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Number of records affected (for bulk operations)',
    example: 5,
    minimum: 1,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  affectedCount?: number;

  @ApiPropertyOptional({
    description: 'Additional context and metadata for the activity',
    type: 'object',
    additionalProperties: true,
    example: {
      source: 'web',
      reason: 'User requested update',
      bulkOperationId: '550e8400-e29b-41d4-a716-************',
    },
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'IP address of the user who performed the activity',
    example: '***********',
  })
  @IsOptional()
  @IsIP()
  ipAddress?: string;

  @ApiPropertyOptional({
    description: 'User agent string from the client',
    example: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  })
  @IsOptional()
  @IsString()
  userAgent?: string;

  @ApiPropertyOptional({
    description: 'Session ID to track activities within the same session',
    example: 'sess_550e8400-e29b-41d4-a716-446655440004',
  })
  @IsOptional()
  @IsString()
  sessionId?: string;

  @ApiPropertyOptional({
    description: 'Operation duration in milliseconds',
    example: 150,
    minimum: 0,
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  duration?: number;
}
