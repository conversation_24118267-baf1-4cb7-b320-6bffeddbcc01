// interfaces/activity-log.interfaces.ts

import {
  ActivityType,
  EntityType,
  ActivitySource,
} from '../../shared/types/activity.enum';

// ============= BASE INTERFACES =============
export interface BaseChanges {
  fields?: string[];
}

export interface BaseMetadata {
  reason?: string;
  source?: 'web' | 'mobile' | 'api' | 'import' | 'system';
  version?: string;
  timestamp?: Date;
}

// ============= CHANGES INTERFACES =============

// Single record changes
export interface SingleRecordChanges<T = any> extends BaseChanges {
  before: Partial<T>;
  after: Partial<T>;
}

// Bulk operation changes
export interface BulkOperationChanges<T = any> extends BaseChanges {
  before?: {
    sample: Partial<T>[];
    summary?: Record<string, any>;
  };
  after: Partial<T>;
}

// Status change specific
export interface StatusChangeChanges extends SingleRecordChanges {
  before: { status: string };
  after: { status: string };
}

// Priority change specific
export interface PriorityChangeChanges extends SingleRecordChanges {
  before: { priority: number };
  after: { priority: number };
}

// Creation changes (no before state)
export interface CreateChanges<T = any> extends BaseChanges {
  before?: never;
  after: T;
}

// Deletion changes (soft delete)
export interface DeleteChanges extends BaseChanges {
  before: { isDeleted: boolean };
  after: { isDeleted: boolean };
}

// ============= METADATA INTERFACES =============

// Auth operation metadata
export interface AuthMetadata extends BaseMetadata {
  ipLocation?: {
    country?: string;
    city?: string;
    region?: string;
  };
  deviceInfo?: {
    os?: string;
    browser?: string;
    device?: string;
  };
  failureReason?: string; // For failed logins
  mfaUsed?: boolean;
  sessionDuration?: number; // For logout
}

// Bulk operation metadata
export interface BulkOperationMetadata extends BaseMetadata {
  bulkOperationId: string;
  filterCriteria: Record<string, any>;
  affectedIds?: string[];
  totalProcessed: number;
  successCount: number;
  failureCount?: number;
  failures?: Array<{
    id: string;
    error: string;
  }>;
  updateFields?: string[];
  executionStrategy?: 'parallel' | 'sequential';
}

// Status change metadata
export interface StatusChangeMetadata extends BaseMetadata {
  statusTransition: string; // "ACTIVE -> INACTIVE"
  previousStatus: string;
  newStatus: string;
  validationsPassed?: boolean;
  triggeredBy?: 'manual' | 'system' | 'schedule' | 'event';
  relatedEntities?: Array<{
    type: string;
    id: string;
    action: string;
  }>;
}

// Priority change metadata
export interface PriorityChangeMetadata extends BaseMetadata {
  priorityChange: string; // "0 -> 5"
  previousPriority: number;
  newPriority: number;
  impact: 'increased' | 'decreased' | 'no_change';
  affectedHierarchy?: boolean;
  cascadedTo?: string[]; // IDs of related entities affected
}

// Import/Export metadata
export interface ImportExportMetadata extends BaseMetadata {
  fileName: string;
  fileSize: number;
  format: 'csv' | 'json' | 'xlsx';
  recordCount: number;
  mapping?: Record<string, string>;
  validationErrors?: Array<{
    row: number;
    field: string;
    error: string;
  }>;
  importId?: string;
  exportFilters?: Record<string, any>;
}

// Search/View metadata
export interface SearchViewMetadata extends BaseMetadata {
  query?: string;
  filters?: Record<string, any>;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  pageSize?: number;
  pageNumber?: number;
  resultCount?: number;
  searchDuration?: number;
  viewDuration?: number; // Time spent on page
}

// System operation metadata
export interface SystemMetadata extends BaseMetadata {
  source: 'system';
  scheduledJob?: string;
  cronExpression?: string;
  triggeredBy: 'schedule' | 'event' | 'webhook';
  systemVersion?: string;
  environment?: 'production' | 'staging' | 'development';
}

// ============= ACTIVITY TYPE MAPPINGS =============

// Type-safe activity log entry types
export interface ActivityLogEntryTypes {
  // Single operations
  CREATE: {
    changes: CreateChanges;
    metadata: BaseMetadata;
  };
  UPDATE: {
    changes: SingleRecordChanges;
    metadata: BaseMetadata;
  };
  DELETE: {
    changes: DeleteChanges;
    metadata: BaseMetadata;
  };
  RESTORE: {
    changes: DeleteChanges;
    metadata: BaseMetadata;
  };
  STATUS_CHANGE: {
    changes: StatusChangeChanges;
    metadata: StatusChangeMetadata;
  };
  PRIORITY_CHANGE: {
    changes: PriorityChangeChanges;
    metadata: PriorityChangeMetadata;
  };

  // Bulk operations
  BULK_CREATE: {
    changes: CreateChanges[];
    metadata: BulkOperationMetadata;
  };
  BULK_UPDATE: {
    changes: BulkOperationChanges;
    metadata: BulkOperationMetadata;
  };
  BULK_DELETE: {
    changes: BulkOperationChanges;
    metadata: BulkOperationMetadata;
  };
  BULK_STATUS_CHANGE: {
    changes: BulkOperationChanges<{ status: string }>;
    metadata: BulkOperationMetadata & StatusChangeMetadata;
  };
  BULK_PRIORITY_CHANGE: {
    changes: BulkOperationChanges<{ priority: number }>;
    metadata: BulkOperationMetadata & PriorityChangeMetadata;
  };

  // Auth operations
  LOGIN: {
    changes: never;
    metadata: AuthMetadata;
  };
  LOGOUT: {
    changes: never;
    metadata: AuthMetadata;
  };
  LOGIN_FAILED: {
    changes: never;
    metadata: AuthMetadata;
  };

  // Data operations
  EXPORT: {
    changes: never;
    metadata: ImportExportMetadata;
  };
  IMPORT: {
    changes: CreateChanges[];
    metadata: ImportExportMetadata;
  };
  VIEW: {
    changes: never;
    metadata: SearchViewMetadata;
  };
  SEARCH: {
    changes: never;
    metadata: SearchViewMetadata;
  };
}

// ============= HELPER TYPES =============

// Extract types for specific activity
export type ActivityLogEntry<T extends keyof ActivityLogEntryTypes> = {
  id: string;
  entityType: EntityType;
  entityId?: string;
  entityIds?: string[];
  businessId?: string;
  activityType: T;
  source: ActivitySource;
  userId: string;
  changes: ActivityLogEntryTypes[T]['changes'];
  metadata: ActivityLogEntryTypes[T]['metadata'];
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  duration?: number;
  affectedCount?: number;
  createdAt: Date;
};

// Union type for all possible activity log entries
export type AnyActivityLogEntry = {
  [K in keyof ActivityLogEntryTypes]: ActivityLogEntry<K>;
}[keyof ActivityLogEntryTypes];

// ============= TYPE GUARD FUNCTIONS =============

export function isStatusChangeActivity(
  activity: AnyActivityLogEntry,
): activity is ActivityLogEntry<'STATUS_CHANGE'> {
  return activity.activityType === 'STATUS_CHANGE';
}

export function isPriorityChangeActivity(
  activity: AnyActivityLogEntry,
): activity is ActivityLogEntry<'PRIORITY_CHANGE'> {
  return activity.activityType === 'PRIORITY_CHANGE';
}

export function isBulkOperation(activityType: string): boolean {
  return activityType.startsWith('BULK_');
}

export function isAuthOperation(
  activity: AnyActivityLogEntry,
): activity is ActivityLogEntry<'LOGIN' | 'LOGOUT' | 'LOGIN_FAILED'> {
  return ['LOGIN', 'LOGOUT', 'LOGIN_FAILED'].includes(activity.activityType);
}

export function isImportExportOperation(
  activity: AnyActivityLogEntry,
): activity is ActivityLogEntry<'IMPORT'> | ActivityLogEntry<'EXPORT'> {
  return ['IMPORT', 'EXPORT'].includes(activity.activityType);
}

export function isSearchViewOperation(
  activity: AnyActivityLogEntry,
): activity is ActivityLogEntry<'VIEW' | 'SEARCH'> {
  return ['VIEW', 'SEARCH'].includes(activity.activityType);
}

// ============= UTILITY FUNCTIONS =============

// Helper function to create type-safe activity log data
export function createActivityLogData<T extends ActivityType>(
  type: T,
  data: Omit<ActivityLogEntry<T>, 'id' | 'createdAt'>,
): Omit<ActivityLogEntry<T>, 'id' | 'createdAt'> {
  return data;
}

// Helper function to validate activity log structure
export function validateActivityLogStructure<T extends ActivityType>(
  type: T,
  data: any,
): data is ActivityLogEntry<T> {
  // Basic validation
  if (!data || typeof data !== 'object') return false;
  if (data.activityType !== type) return false;
  if (!data.entityType || !data.userId) return false;

  // Type-specific validation
  switch (type) {
    case 'BULK_CREATE':
    case 'BULK_UPDATE':
    case 'BULK_DELETE':
    case 'BULK_STATUS_CHANGE':
    case 'BULK_PRIORITY_CHANGE':
      return Array.isArray(data.entityIds) && data.entityIds.length > 0;

    case 'LOGIN':
    case 'LOGOUT':
    case 'LOGIN_FAILED':
      return data.entityType === 'AUTH';

    default:
      return !!data.entityId;
  }
}

// ============= CONSTANTS =============

export const ACTIVITY_TYPES = {
  // Single operations
  CREATE: 'CREATE',
  UPDATE: 'UPDATE',
  DELETE: 'DELETE',
  RESTORE: 'RESTORE',
  STATUS_CHANGE: 'STATUS_CHANGE',
  PRIORITY_CHANGE: 'PRIORITY_CHANGE',

  // Bulk operations
  BULK_CREATE: 'BULK_CREATE',
  BULK_UPDATE: 'BULK_UPDATE',
  BULK_DELETE: 'BULK_DELETE',
  BULK_STATUS_CHANGE: 'BULK_STATUS_CHANGE',
  BULK_PRIORITY_CHANGE: 'BULK_PRIORITY_CHANGE',

  // Auth operations
  LOGIN: 'LOGIN',
  LOGOUT: 'LOGOUT',
  LOGIN_FAILED: 'LOGIN_FAILED',

  // Data operations
  EXPORT: 'EXPORT',
  IMPORT: 'IMPORT',
  VIEW: 'VIEW',
  SEARCH: 'SEARCH',
} as const;

export const ENTITY_TYPES = {
  DESIGNATION: 'DESIGNATION',
  USER: 'USER',
  BUSINESS: 'BUSINESS',
  AUTH: 'AUTH',
  SYSTEM: 'SYSTEM',
  ASSET: 'ASSET',
  CATEGORY: 'CATEGORY',
  PRODUCT: 'PRODUCT',
  SERVICE: 'SERVICE',
  CUSTOMER: 'CUSTOMER',
  ORDER: 'ORDER',
  INVOICE: 'INVOICE',
  PAYMENT: 'PAYMENT',
  STAFF: 'STAFF',
  DEPARTMENT: 'DEPARTMENT',
  LOCATION: 'LOCATION',
  SUPPLIER: 'SUPPLIER',
  VEHICLE: 'VEHICLE',
  RESERVATION: 'RESERVATION',
  CAMPAIGN: 'CAMPAIGN',
  TEMPLATE: 'TEMPLATE',
  WORK_ORDER: 'WORK_ORDER',
  PROJECT: 'PROJECT',
  TASK: 'TASK',
  EXPENSE: 'EXPENSE',
  LEAD: 'LEAD',
  ESTIMATE: 'ESTIMATE',
  MEETING: 'MEETING',
} as const;

// ============= EXAMPLE USAGE =============

/*
  // Example 1: Creating a type-safe status change log
  const statusChangeData = createActivityLogData('STATUS_CHANGE', {
    entityType: 'DESIGNATION',
    entityId: '123',
    userId: 'user-456',
    businessId: 'business-789',
    activityType: 'STATUS_CHANGE',
    changes: {
      before: { status: 'ACTIVE' },
      after: { status: 'INACTIVE' },
      fields: ['status']
    },
    metadata: {
      statusTransition: 'ACTIVE -> INACTIVE',
      previousStatus: 'ACTIVE',
      newStatus: 'INACTIVE',
      reason: 'Position suspended',
      triggeredBy: 'manual',
      source: 'web'
    }
  });
  
  // Example 2: Type-safe activity log query
  function getStatusChangeLogs(): Promise<ActivityLogEntry<'STATUS_CHANGE'>[]> {
    // Implementation
    return Promise.resolve([]);
  }
  
  // Example 3: Using type guards
  function processActivityLog(log: AnyActivityLogEntry) {
    if (isStatusChangeActivity(log)) {
      // TypeScript knows log.metadata is StatusChangeMetadata
      console.log(`Status changed from ${log.metadata.previousStatus} to ${log.metadata.newStatus}`);
    } else if (isBulkOperation(log.activityType)) {
      // Handle bulk operations
      console.log(`Bulk operation affected ${log.affectedCount} records`);
    } else if (isAuthOperation(log)) {
      // TypeScript knows log.metadata is AuthMetadata
      console.log(`Auth event from ${log.metadata.source}`);
    }
  }
  */
