# Session Management Implementation

This document describes the comprehensive session management system implemented for the BizC ERP authentication module.

## 🗂️ **Files Created/Modified**

### **New Files**
1. `/src/app/drizzle/schema/user-sessions.schema.ts` - Database schema for user sessions
2. `/src/app/auth/services/session.service.ts` - Core session management service  
3. `/src/app/auth/middleware/session-tracking.middleware.ts` - Automatic session activity tracking
4. `/src/app/auth/guards/session-auth.guard.ts` - Enhanced JWT guard with session validation
5. `/src/app/auth/dto/session.dto.ts` - Session-related DTOs
6. `/src/app/shared/decorators/session-id.decorator.ts` - Session ID extraction decorator
7. `/src/app/shared/decorators/activity-source.decorator.ts` - Source detection decorator
8. `/src/app/shared/decorators/request-context.decorator.ts` - Request context decorator

### **Modified Files**
1. `/src/app/auth/auth.controller.ts` - Added session management endpoints
2. `/src/app/auth/auth.module.ts` - Integrated session services and middleware
3. `/src/app/drizzle/schema/schema.ts` - Added user sessions schema

## 🏗️ **Architecture Overview**

### **Database Schema**
```sql
-- User Sessions Table
user_sessions (
  id: UUID PRIMARY KEY,
  session_id: TEXT UNIQUE NOT NULL,  -- External session ID
  user_id: UUID REFERENCES users(id),
  source: session_source NOT NULL,   -- WEB, MOBILE, API
  device_type: device_type NOT NULL, -- DESKTOP, MOBILE, TABLET, etc.
  status: session_status DEFAULT 'ACTIVE',
  user_agent: TEXT,
  ip_address: TEXT,
  device_id: TEXT,
  device_info: TEXT, -- JSON device details
  country: TEXT,
  city: TEXT,
  timezone: TEXT,
  first_seen_at: TIMESTAMP DEFAULT NOW(),
  last_seen_at: TIMESTAMP DEFAULT NOW(),
  expires_at: TIMESTAMP,
  request_count: INTEGER DEFAULT 0,
  last_activity_type: TEXT,
  ended_at: TIMESTAMP,
  end_reason: TEXT,
  created_at: TIMESTAMP DEFAULT NOW(),
  updated_at: TIMESTAMP DEFAULT NOW()
);
```

### **Core Services**

#### **SessionService**
- `createSession()` - Create new session with activity logging
- `updateSession()` - Update session activity 
- `getSession()` - Retrieve session details
- `getUserActiveSessions()` - Get user's active sessions
- `endSession()` - End session (logout)
- `revokeSession()` - Revoke specific session
- `revokeAllUserSessions()` - Revoke all user sessions except current
- `cleanupExpiredSessions()` - Cleanup expired sessions
- `getUserSessionStats()` - Get session statistics
- `trackSessionActivity()` - Track request activity

#### **SessionTrackingMiddleware**
- Automatically tracks session activity for authenticated routes
- Updates `last_seen_at`, `request_count`, `last_activity_type`
- Non-blocking - doesn't interrupt request flow on errors

#### **SessionAuthGuard**
- Extends standard JWT guard with session validation
- Validates session status (ACTIVE, not EXPIRED/REVOKED)
- Ensures session belongs to authenticated user
- Optional session validation with `@SkipSessionValidation()` decorator

### **Decorators**

#### **@SessionId()**
```typescript
@Post()
create(@SessionId() sessionId: string) {
  // sessionId automatically extracted from:
  // 1. X-Session-Id header
  // 2. sessionId query parameter  
  // 3. sessionId from request body
  // 4. Auto-generated fallback
}
```

#### **@Source()**
```typescript
@Post()
create(@Source() source: ActivitySource) {
  // source automatically detected as WEB, MOBILE, or API
}
```

#### **@RequestContext()**
```typescript
@Post()
create(@RequestContext() context: RequestContextType) {
  // context contains: sessionId, source, ipAddress, userAgent, timestamp
}
```

## 🔌 **API Endpoints**

### **Session Management**
```typescript
GET    /auth/sessions              // Get user's active sessions
GET    /auth/sessions/stats        // Get session statistics  
GET    /auth/sessions/:sessionId   // Get specific session details
POST   /auth/logout               // End current session
DELETE /auth/sessions/:sessionId   // Revoke specific session
POST   /auth/sessions/revoke-all   // Revoke all sessions except current
POST   /auth/login-with-session    // Enhanced login with session creation
```

### **Example Usage**

#### **Enhanced Login**
```typescript
POST /auth/login-with-session
{
  "email": "<EMAIL>",
  "password": "password",
  "sessionId": "web_1640995200000_abc123",
  "source": "WEB",
  "deviceId": "device_fingerprint_123",
  "deviceInfo": { "os": "Windows 10", "browser": "Chrome" },
  "timezone": "America/New_York"
}

Response:
{
  "accessToken": "...",
  "refreshToken": "...",
  "user": { "id": "...", "email": "..." },
  "session": {
    "id": "...",
    "sessionId": "web_1640995200000_abc123",
    "source": "WEB",
    "deviceType": "DESKTOP",
    "status": "ACTIVE",
    "firstSeenAt": "2024-01-15T10:00:00Z",
    "lastSeenAt": "2024-01-15T10:00:00Z"
  }
}
```

#### **Session Activity Tracking**
```typescript
// Automatic via middleware for all authenticated routes
// Manual tracking in controllers:
@Delete(':id')
remove(
  @RequestContext() context: RequestContextType,
  @Request() req: AuthenticatedRequest,
  @Param('id') id: string,
) {
  // context.sessionId, context.source, etc. automatically available
  return this.service.remove(req.user.id, id, context);
}
```

## 🔒 **Security Features**

### **Session Validation**
- Session status validation (ACTIVE only)
- Session expiry checking  
- User ownership verification
- IP address tracking for security monitoring

### **Activity Logging Integration**
- Automatic LOGIN/LOGOUT activity logging
- Session creation/termination tracking
- Request activity correlation with sessions
- Security event detection (multiple concurrent sessions)

### **Session Revocation**
- Individual session revocation
- Bulk session revocation (security response)
- Automatic cleanup of expired sessions
- Admin session management capabilities

## 📊 **Analytics & Monitoring**

### **Session Statistics**
```typescript
{
  "totalSessions": 25,
  "activeSessions": 3,
  "expiredSessions": 20,
  "revokedSessions": 2,
  "bySource": { "WEB": 15, "MOBILE": 8, "API": 2 },
  "byDeviceType": { "DESKTOP": 12, "MOBILE": 10, "TABLET": 3 },
  "uniqueDevices": 8,
  "uniqueIPs": 5,
  "averageSessionDuration": 45 // minutes
}
```

### **Activity Correlation**
- Link activity logs to specific sessions
- User journey reconstruction
- Security incident investigation
- Performance analysis by session patterns

## 🚀 **Client Integration**

### **Web Client**
```javascript
// Generate and store session ID
const sessionId = `web_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
localStorage.setItem('sessionId', sessionId);

// Include in API requests
headers: {
  'Authorization': `Bearer ${token}`,
  'X-Session-Id': sessionId,
  'X-Client-Source': 'WEB'
}
```

### **Mobile Client**
```javascript
const sessionId = `mobile_${Date.now()}_${deviceId}`;

headers: {
  'Authorization': `Bearer ${token}`,
  'X-Session-Id': sessionId,
  'X-Client-Source': 'MOBILE',
  'X-App-Version': '1.2.3'
}
```

### **API Client**
```javascript
const correlationId = `api_${Date.now()}_${clientId}`;

headers: {
  'X-API-Key': apiKey,
  'X-Session-Id': correlationId,
  'X-Client-Source': 'API',
  'X-Client-Id': clientId
}
```

## 🎯 **Benefits**

### **Security**
- Enhanced account takeover detection
- Session hijacking prevention
- Suspicious activity monitoring
- Multi-device session management

### **User Experience**
- Device-aware authentication
- Session management dashboard
- Remote logout capabilities
- Activity history tracking

### **Compliance**
- Complete audit trails
- Session-based access logging
- Security incident investigation
- Regulatory compliance support

### **Operations**
- System usage analytics
- Performance monitoring
- User behavior analysis
- Capacity planning insights

## 🔧 **Configuration**

### **Environment Variables**
```bash
# JWT Configuration (existing)
JWT_ACCESS_SECRET=your-access-secret
JWT_REFRESH_SECRET=your-refresh-secret

# Session Configuration (optional)
SESSION_CLEANUP_INTERVAL=3600000  # 1 hour in ms
SESSION_DEFAULT_EXPIRY=********   # 24 hours in ms
MAX_CONCURRENT_SESSIONS=10        # Per user limit
```

### **Module Configuration**
```typescript
// Apply middleware to all routes except auth endpoints
consumer
  .apply(SessionTrackingMiddleware)
  .exclude('auth/login', 'auth/register', 'auth/refresh-token')
  .forRoutes('*');
```

## 🚀 **Migration Guide**

### **Database Migration**
1. Run database migration to create `user_sessions` table
2. Update existing controllers to use new decorators
3. Enable session tracking middleware
4. Update client applications to send session IDs

### **Backward Compatibility**
- All existing JWT authentication continues to work
- Session validation is optional by default
- Gradual migration path available
- No breaking changes to existing APIs

This implementation provides a robust, scalable session management system that enhances security, improves user experience, and provides comprehensive activity tracking capabilities.