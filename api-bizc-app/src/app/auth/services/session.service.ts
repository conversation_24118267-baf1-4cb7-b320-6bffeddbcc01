import { Injectable, Inject, Logger } from '@nestjs/common';
import { DRIZZLE } from '../../drizzle/drizzle.module';
import { DrizzleDB } from '../../drizzle/types/drizzle';
import {
  userSessions,
  UserSession,
  NewUserSession,
  SessionSource,
  DeviceType,
  CreateSessionData,
  UpdateSessionData,
  SessionStats,
  SessionWithUser,
  DeviceInfo,
} from '../../drizzle/schema/user-sessions.schema';
import { eq, and, desc, lte, sql, isNull } from 'drizzle-orm';
import { ActivitySource } from '../../shared/types/activity.enum';
import { ActivityLogService } from '../../activity-log/activity-log.service';
import { ActivityType } from '../../drizzle/schema/activity-log.schema';

@Injectable()
export class SessionService {
  private readonly logger = new Logger(SessionService.name);

  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private activityLogService: ActivityLogService,
  ) {}

  /**
   * Create a new user session
   */
  async createSession(data: CreateSessionData): Promise<UserSession> {
    try {
      const deviceType = this.detectDeviceType(data.userAgent, data.source);
      const deviceInfo = this.parseDeviceInfo(data.userAgent);

      const sessionData: NewUserSession = {
        sessionId: data.sessionId,
        userId: data.userId,
        source: data.source as SessionSource,
        deviceType,
        userAgent: data.userAgent,
        ipAddress: data.ipAddress,
        deviceId: data.deviceId,
        deviceInfo: data.deviceInfo ? JSON.stringify(data.deviceInfo) : null,
        country: data.country,
        city: data.city,
        timezone: data.timezone,
        expiresAt: data.expiresAt,
        status: 'ACTIVE',
        requestCount: 0,
      };

      const [session] = await this.db
        .insert(userSessions)
        .values(sessionData)
        .returning();

      // Log session creation
      await this.activityLogService.logAuthEvent(
        ActivityType.LOGIN,
        data.userId,
        {
          ipAddress: data.ipAddress || '',
          userAgent: data.userAgent || '',
          sessionId: data.sessionId,
          deviceInfo: deviceInfo,
        },
      );

      this.logger.log(
        `Session created: ${data.sessionId} for user ${data.userId}`,
      );
      return session;
    } catch (error) {
      this.logger.error('Failed to create session', {
        error: error.message,
        sessionId: data.sessionId,
        userId: data.userId,
      });
      throw error;
    }
  }

  /**
   * Update an existing session
   */
  async updateSession(
    sessionId: string,
    data: UpdateSessionData,
  ): Promise<UserSession | null> {
    try {
      const updateData: Partial<NewUserSession> = {
        lastSeenAt: data.lastSeenAt || new Date(),
        updatedAt: new Date(),
      };

      if (data.requestCount !== undefined) {
        updateData.requestCount = data.requestCount;
      }
      if (data.lastActivityType) {
        updateData.lastActivityType = data.lastActivityType;
      }
      if (data.ipAddress) {
        updateData.ipAddress = data.ipAddress;
      }
      if (data.country) {
        updateData.country = data.country;
      }
      if (data.city) {
        updateData.city = data.city;
      }

      const [session] = await this.db
        .update(userSessions)
        .set(updateData)
        .where(eq(userSessions.sessionId, sessionId))
        .returning();

      return session || null;
    } catch (error) {
      this.logger.error('Failed to update session', {
        error: error.message,
        sessionId,
      });
      throw error;
    }
  }

  /**
   * Get session by session ID
   */
  async getSession(sessionId: string): Promise<UserSession | null> {
    try {
      const session = await this.db.query.userSessions.findFirst({
        where: eq(userSessions.sessionId, sessionId),
      });

      return (session as UserSession) || null;
    } catch (error) {
      this.logger.error('Failed to get session', {
        error: error.message,
        sessionId,
      });
      return null;
    }
  }

  /**
   * Get session with user information
   */
  async getSessionWithUser(sessionId: string): Promise<SessionWithUser | null> {
    try {
      const session = await this.db.query.userSessions.findFirst({
        where: eq(userSessions.sessionId, sessionId),
        with: {
          user: {
            columns: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
              role: true,
              status: true,
            },
          },
        },
      });

      return (session as SessionWithUser) || null;
    } catch (error) {
      this.logger.error('Failed to get session with user', {
        error: error.message,
        sessionId,
      });
      return null;
    }
  }

  /**
   * Get user's active sessions
   */
  async getUserActiveSessions(userId: string): Promise<UserSession[]> {
    try {
      const sessions = await this.db.query.userSessions.findMany({
        where: and(
          eq(userSessions.userId, userId),
          eq(userSessions.status, 'ACTIVE'),
          isNull(userSessions.endedAt),
        ),
        orderBy: [desc(userSessions.lastSeenAt)],
      });
      return sessions as UserSession[];
    } catch (error) {
      this.logger.error('Failed to get user active sessions', {
        error: error.message,
        userId,
      });
      return [];
    }
  }

  /**
   * End a session (logout)
   */
  async endSession(
    sessionId: string,
    reason: string = 'LOGOUT',
    userId?: string,
  ): Promise<UserSession | null> {
    try {
      const [session] = await this.db
        .update(userSessions)
        .set({
          status: 'LOGGED_OUT',
          endedAt: new Date(),
          endReason: reason,
          updatedAt: new Date(),
        })
        .where(eq(userSessions.sessionId, sessionId))
        .returning();

      if (session && userId) {
        // Calculate session duration
        const sessionDuration = session.endedAt
          ? session.endedAt.getTime() - session.firstSeenAt.getTime()
          : 0;

        // Log session end
        await this.activityLogService.logAuthEvent(
          ActivityType.LOGOUT,
          userId,
          {
            ipAddress: session.ipAddress || '',
            userAgent: session.userAgent || '',
            sessionId: sessionId,
            sessionDuration: Math.floor(sessionDuration / 1000), // in seconds
          },
        );
      }

      this.logger.log(`Session ended: ${sessionId}, reason: ${reason}`);
      return session || null;
    } catch (error) {
      this.logger.error('Failed to end session', {
        error: error.message,
        sessionId,
        reason,
      });
      throw error;
    }
  }

  /**
   * Revoke session (admin action or security)
   */
  async revokeSession(
    sessionId: string,
    reason: string = 'REVOKED',
    revokedBy?: string,
  ): Promise<UserSession | null> {
    try {
      const [session] = await this.db
        .update(userSessions)
        .set({
          status: 'REVOKED',
          endedAt: new Date(),
          endReason: reason,
          updatedAt: new Date(),
        })
        .where(eq(userSessions.sessionId, sessionId))
        .returning();

      this.logger.warn(`Session revoked: ${sessionId}`, {
        reason,
        revokedBy,
      });

      return session || null;
    } catch (error) {
      this.logger.error('Failed to revoke session', {
        error: error.message,
        sessionId,
        reason,
      });
      throw error;
    }
  }

  /**
   * Revoke all user sessions except current
   */
  async revokeAllUserSessions(
    userId: string,
    exceptSessionId?: string,
    reason: string = 'REVOKE_ALL',
  ): Promise<number> {
    try {
      const conditions = [
        eq(userSessions.userId, userId),
        eq(userSessions.status, 'ACTIVE'),
      ];

      if (exceptSessionId) {
        conditions.push(sql`${userSessions.sessionId} != ${exceptSessionId}`);
      }

      const result = await this.db
        .update(userSessions)
        .set({
          status: 'REVOKED',
          endedAt: new Date(),
          endReason: reason,
          updatedAt: new Date(),
        })
        .where(and(...conditions));

      this.logger.log(`Revoked sessions for user ${userId}`, {
        exceptSessionId,
        reason,
      });

      return result.rowCount || 0;
    } catch (error) {
      this.logger.error('Failed to revoke user sessions', {
        error: error.message,
        userId,
        exceptSessionId,
      });
      throw error;
    }
  }

  /**
   * Clean up expired sessions
   */
  async cleanupExpiredSessions(): Promise<number> {
    try {
      const now = new Date();

      const result = await this.db
        .update(userSessions)
        .set({
          status: 'EXPIRED',
          endedAt: now,
          endReason: 'EXPIRED',
          updatedAt: now,
        })
        .where(
          and(
            eq(userSessions.status, 'ACTIVE'),
            lte(userSessions.expiresAt, now),
          ),
        );

      const expiredCount = result.rowCount || 0;
      if (expiredCount > 0) {
        this.logger.log(`Cleaned up ${expiredCount} expired sessions`);
      }

      return expiredCount;
    } catch (error) {
      this.logger.error('Failed to cleanup expired sessions', {
        error: error.message,
      });
      return 0;
    }
  }

  /**
   * Get session statistics for a user
   */
  async getUserSessionStats(userId: string): Promise<SessionStats> {
    try {
      const sessions = await this.db.query.userSessions.findMany({
        where: eq(userSessions.userId, userId),
      });

      const stats: SessionStats = {
        totalSessions: sessions.length,
        activeSessions: sessions.filter((s) => s.status === 'ACTIVE').length,
        expiredSessions: sessions.filter((s) => s.status === 'EXPIRED').length,
        revokedSessions: sessions.filter((s) => s.status === 'REVOKED').length,
        bySource: {
          [ActivitySource.WEB]: 0,
          [ActivitySource.MOBILE]: 0,
          [ActivitySource.API]: 0,
        },
        byDeviceType: {
          DESKTOP: 0,
          MOBILE: 0,
          TABLET: 0,
          API_CLIENT: 0,
          UNKNOWN: 0,
        },
        uniqueDevices: new Set(sessions.map((s) => s.deviceId).filter(Boolean))
          .size,
        uniqueIPs: new Set(sessions.map((s) => s.ipAddress).filter(Boolean))
          .size,
        averageSessionDuration: 0,
      };

      // Calculate statistics
      sessions.forEach((session) => {
        if (session.source in stats.bySource) {
          stats.bySource[session.source as SessionSource]++;
        }
        if (session.deviceType in stats.byDeviceType) {
          stats.byDeviceType[session.deviceType]++;
        }
      });

      // Calculate average session duration for ended sessions
      const endedSessions = sessions.filter((s) => s.endedAt);
      if (endedSessions.length > 0) {
        const totalDuration = endedSessions.reduce((sum, session) => {
          const duration =
            session.endedAt!.getTime() - session.firstSeenAt.getTime();
          return sum + duration;
        }, 0);
        stats.averageSessionDuration = Math.floor(
          totalDuration / endedSessions.length / 1000 / 60,
        ); // in minutes
      }

      return stats;
    } catch (error) {
      this.logger.error('Failed to get user session stats', {
        error: error.message,
        userId,
      });
      throw error;
    }
  }

  /**
   * Track session activity (increment request count, update last seen)
   */
  async trackSessionActivity(
    sessionId: string,
    activityType?: string,
    ipAddress?: string,
  ): Promise<void> {
    try {
      await this.db
        .update(userSessions)
        .set({
          lastSeenAt: new Date(),
          requestCount: sql`${userSessions.requestCount} + 1`,
          lastActivityType: activityType,
          ipAddress: ipAddress || sql`${userSessions.ipAddress}`,
          updatedAt: new Date(),
        })
        .where(
          and(
            eq(userSessions.sessionId, sessionId),
            eq(userSessions.status, 'ACTIVE'),
          ),
        );
    } catch (error) {
      this.logger.error('Failed to track session activity', {
        error: error.message,
        sessionId,
        activityType,
      });
      // Don't throw error for activity tracking to avoid disrupting main flow
    }
  }

  /**
   * Detect device type from user agent and source
   */
  private detectDeviceType(
    userAgent?: string,
    source?: ActivitySource,
  ): DeviceType {
    if (source === ActivitySource.API) {
      return 'API_CLIENT';
    }

    if (!userAgent) {
      return 'UNKNOWN';
    }

    const ua = userAgent.toLowerCase();

    if (
      ua.includes('mobile') ||
      ua.includes('android') ||
      ua.includes('iphone')
    ) {
      return 'MOBILE';
    }

    if (ua.includes('tablet') || ua.includes('ipad')) {
      return 'TABLET';
    }

    if (
      ua.includes('mozilla') ||
      ua.includes('chrome') ||
      ua.includes('safari')
    ) {
      return 'DESKTOP';
    }

    return 'UNKNOWN';
  }

  /**
   * Parse device information from user agent
   */
  private parseDeviceInfo(userAgent?: string): DeviceInfo | undefined {
    if (!userAgent) return undefined;

    const ua = userAgent.toLowerCase();
    const info: DeviceInfo = {};

    // Detect OS
    if (ua.includes('windows')) info.os = 'Windows';
    else if (ua.includes('mac')) info.os = 'macOS';
    else if (ua.includes('linux')) info.os = 'Linux';
    else if (ua.includes('android')) info.os = 'Android';
    else if (ua.includes('ios')) info.os = 'iOS';

    // Detect browser
    if (ua.includes('chrome')) info.browser = 'Chrome';
    else if (ua.includes('firefox')) info.browser = 'Firefox';
    else if (ua.includes('safari')) info.browser = 'Safari';
    else if (ua.includes('edge')) info.browser = 'Edge';
    else if (ua.includes('opera')) info.browser = 'Opera';

    // Detect mobile
    info.mobile =
      ua.includes('mobile') || ua.includes('android') || ua.includes('iphone');

    return info;
  }
}
