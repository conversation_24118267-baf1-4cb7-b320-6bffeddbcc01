import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
  Logger,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { JwtAuthGuard } from './jwt-auth.guard';
import { SessionService } from '../services/session.service';

/**
 * Enhanced JWT Guard with Session Validation
 *
 * Extends the standard JWT guard to also validate active sessions.
 * Provides additional security by ensuring the session is still active.
 */
@Injectable()
export class SessionAuthGuard extends JwtAuthGuard implements CanActivate {
  private readonly logger = new Logger(SessionAuthGuard.name);

  constructor(
    reflector: Reflector,
    private sessionService: SessionService,
  ) {
    super(reflector);
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // First run the standard JWT validation
    const jwtValid = await super.canActivate(context);
    if (!jwtValid) {
      return false;
    }

    // Skip session validation if explicitly disabled
    const skipSessionValidation = this['reflector'].getAllAndOverride<boolean>(
      'skipSessionValidation',
      [context.getHandler(), context.getClass()],
    );

    if (skipSessionValidation) {
      return true;
    }

    // Validate session
    const request = context.switchToHttp().getRequest();
    return this.validateSession(request);
  }

  /**
   * Validate that the session is active
   */
  private async validateSession(request: any): Promise<boolean> {
    try {
      const sessionId = this.extractSessionId(request);
      if (!sessionId) {
        // If no session ID provided, allow JWT-only authentication
        // This maintains backward compatibility
        this.logger.debug('No session ID provided, allowing JWT-only auth');
        return true;
      }

      const session = await this.sessionService.getSession(sessionId);
      if (!session) {
        throw new UnauthorizedException('Invalid session');
      }

      // Check session status
      if (session.status !== 'ACTIVE') {
        throw new UnauthorizedException(
          `Session ${session.status.toLowerCase()}`,
        );
      }

      // Check session expiry
      if (session.expiresAt && session.expiresAt < new Date()) {
        await this.sessionService.endSession(sessionId, 'EXPIRED');
        throw new UnauthorizedException('Session expired');
      }

      // Verify session belongs to authenticated user
      if (session.userId !== request.user.id) {
        throw new UnauthorizedException('Session user mismatch');
      }

      // Attach session to request for controllers
      request.session = session;
      request.sessionId = sessionId;

      return true;
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }

      this.logger.error('Session validation error', {
        error: error.message,
        userId: request.user?.id,
      });

      // On validation errors, allow JWT-only auth to maintain availability
      return true;
    }
  }

  /**
   * Extract session ID from request
   */
  private extractSessionId(request: any): string | null {
    return (
      request.headers['x-session-id'] ||
      request.query?.sessionId ||
      request.body?.sessionId ||
      null
    );
  }
}

/**
 * Decorator to skip session validation for specific endpoints
 */
import { SetMetadata } from '@nestjs/common';

export const SkipSessionValidation = () =>
  SetMetadata('skipSessionValidation', true);
