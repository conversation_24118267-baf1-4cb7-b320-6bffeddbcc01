import { Module, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { JwtModule, JwtService } from '@nestjs/jwt';
import { JwtStrategy } from './strategies/jwt.strategy';
import { RolesGuard } from './guards/roles.guard';
import { PermissionsGuard } from './guards/permissions.guard';
// import { ModulesGuard } from './guards/modules.guard';
import { ConfigService } from '@nestjs/config';
import { ConfigModule } from '@core/config/config.module';
import { PassportModule } from '@nestjs/passport';
import { SharedModule } from '../shared/shared.module';
import { DrizzleModule } from '@app/drizzle/drizzle.module';
import { SessionService } from './services/session.service';
import { SessionAuthGuard } from './guards/session-auth.guard';
import { SessionTrackingMiddleware } from './middleware/session-tracking.middleware';
import { ActivityLogModule } from '../activity-log/activity-log.module';

@Module({
  imports: [
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: () => ({
        secret: process.env.JWT_ACCESS_SECRET || 'your-access-secret-key',
        signOptions: { expiresIn: '1d' },
      }),
      inject: [ConfigService],
    }),
    PassportModule.register({ session: true }),
    ConfigModule,
    SharedModule,
    DrizzleModule,
    ActivityLogModule,
  ],
  controllers: [AuthController],
  providers: [
    AuthService,
    SessionService,
    JwtStrategy,
    RolesGuard,
    PermissionsGuard,
    SessionAuthGuard,
    // ModulesGuard,
    {
      provide: 'JWT_REFRESH_TOKEN_SERVICE',
      useFactory: () => {
        return new JwtService({
          secret: process.env.JWT_REFRESH_SECRET || 'your-refresh-secret-key',
          signOptions: { expiresIn: '7d' },
        });
      },
    },
  ],
  exports: [
    AuthService,
    SessionService,
    RolesGuard,
    PermissionsGuard,
    SessionAuthGuard,
    SessionService,
  ],
})
export class AuthModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    // Apply session tracking middleware to all authenticated routes
    // This will automatically track session activity
    consumer
      .apply(SessionTrackingMiddleware)
      .exclude(
        'auth/login',
        'auth/register',
        'auth/refresh-token',
        'auth/forgot-password',
        'auth/reset-password',
        'auth/login-with-session',
      )
      .forRoutes('*');
  }
}
