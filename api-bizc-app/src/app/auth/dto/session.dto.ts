import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsEnum,
  IsDateString,
  IsObject,
} from 'class-validator';
import { ActivitySource } from '../../shared/types/activity.enum';

/**
 * Session creation data for login
 */
export class CreateSessionDto {
  @ApiProperty({
    description: 'Unique session identifier generated by client',
    example: 'web_1640995200000_abc123',
  })
  @IsString()
  sessionId: string;

  @ApiProperty({
    description: 'Source of the session',
    enum: ActivitySource,
    example: ActivitySource.WEB,
  })
  @IsEnum(ActivitySource)
  source: ActivitySource;

  @ApiPropertyOptional({
    description: 'Device identifier for tracking',
    example: 'device_fingerprint_123',
  })
  @IsOptional()
  @IsString()
  deviceId?: string;

  @ApiPropertyOptional({
    description: 'Additional device information',
    example: { os: 'Windows 10', browser: 'Chrome', version: '96.0' },
  })
  @IsOptional()
  @IsObject()
  deviceInfo?: any;

  @ApiPropertyOptional({
    description: 'User timezone',
    example: 'America/New_York',
  })
  @IsOptional()
  @IsString()
  timezone?: string;

  @ApiPropertyOptional({
    description: 'Session expiry time (ISO string)',
    example: '2024-01-15T10:30:00Z',
  })
  @IsOptional()
  @IsDateString()
  expiresAt?: string;
}

/**
 * Session information response
 */
export class SessionDto {
  @ApiProperty({
    description: 'Internal session ID',
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
  })
  id: string;

  @ApiProperty({
    description: 'External session identifier',
    example: 'web_1640995200000_abc123',
  })
  sessionId: string;

  @ApiProperty({
    description: 'User ID',
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
  })
  userId: string;

  @ApiProperty({
    description: 'Session source',
    enum: ActivitySource,
  })
  source: ActivitySource;

  @ApiProperty({
    description: 'Device type',
    example: 'DESKTOP',
  })
  deviceType: string;

  @ApiProperty({
    description: 'Session status',
    example: 'ACTIVE',
  })
  status: string;

  @ApiPropertyOptional({
    description: 'User agent string',
  })
  userAgent?: string;

  @ApiPropertyOptional({
    description: 'IP address',
  })
  ipAddress?: string;

  @ApiPropertyOptional({
    description: 'Device identifier',
  })
  deviceId?: string;

  @ApiPropertyOptional({
    description: 'Country',
  })
  country?: string;

  @ApiPropertyOptional({
    description: 'City',
  })
  city?: string;

  @ApiProperty({
    description: 'First seen timestamp',
  })
  firstSeenAt: Date;

  @ApiProperty({
    description: 'Last seen timestamp',
  })
  lastSeenAt: Date;

  @ApiPropertyOptional({
    description: 'Session expiry',
  })
  expiresAt?: Date;

  @ApiProperty({
    description: 'Request count in this session',
  })
  requestCount: number;

  @ApiPropertyOptional({
    description: 'Last activity type',
  })
  lastActivityType?: string;

  @ApiProperty({
    description: 'Session creation timestamp',
  })
  createdAt: Date;
}

/**
 * User's active sessions response
 */
export class UserSessionsDto {
  @ApiProperty({
    description: 'Current session ID (if available)',
    example: 'web_1640995200000_abc123',
  })
  currentSessionId?: string;

  @ApiProperty({
    description: 'List of active sessions',
    type: [SessionDto],
  })
  sessions: SessionDto[];

  @ApiProperty({
    description: 'Total active sessions count',
  })
  totalActiveSessions: number;
}

/**
 * Session statistics
 */
export class SessionStatsDto {
  @ApiProperty({ description: 'Total sessions ever created' })
  totalSessions: number;

  @ApiProperty({ description: 'Currently active sessions' })
  activeSessions: number;

  @ApiProperty({ description: 'Expired sessions' })
  expiredSessions: number;

  @ApiProperty({ description: 'Revoked sessions' })
  revokedSessions: number;

  @ApiProperty({
    description: 'Sessions by source',
    example: { WEB: 5, MOBILE: 2, API: 1 },
  })
  bySource: Record<string, number>;

  @ApiProperty({
    description: 'Sessions by device type',
    example: { DESKTOP: 4, MOBILE: 3, TABLET: 1 },
  })
  byDeviceType: Record<string, number>;

  @ApiProperty({ description: 'Number of unique devices used' })
  uniqueDevices: number;

  @ApiProperty({ description: 'Number of unique IP addresses' })
  uniqueIPs: number;

  @ApiProperty({ description: 'Average session duration in minutes' })
  averageSessionDuration: number;
}

/**
 * Revoke session request
 */
export class RevokeSessionDto {
  @ApiProperty({
    description: 'Reason for revoking session',
    example: 'User requested logout from all devices',
  })
  @IsString()
  reason: string;
}

/**
 * Revoke all sessions request
 */
export class RevokeAllSessionsDto {
  @ApiPropertyOptional({
    description: 'Session ID to keep active (current session)',
    example: 'web_1640995200000_abc123',
  })
  @IsOptional()
  @IsString()
  keepSessionId?: string;

  @ApiProperty({
    description: 'Reason for revoking all sessions',
    example: 'Security concern - login from new device',
  })
  @IsString()
  reason: string;
}

/**
 * Session revocation response
 */
export class SessionRevocationResponseDto {
  @ApiProperty({ description: 'Number of sessions revoked' })
  revokedCount: number;

  @ApiProperty({ description: 'Success message' })
  message: string;
}

/**
 * Enhanced login response with session info
 */
export class LoginWithSessionResponseDto {
  @ApiProperty({ description: 'JWT access token' })
  accessToken: string;

  @ApiProperty({ description: 'JWT refresh token' })
  refreshToken: string;

  @ApiProperty({ description: 'User information' })
  user: {
    id: string;
    email: string;
    firstName?: string;
    lastName?: string;
    role: string;
    activeBusinessId?: string;
  };

  @ApiProperty({ description: 'Session information' })
  session: SessionDto;

  @ApiPropertyOptional({ description: 'Whether 2FA is required' })
  requires2FA?: boolean;
}
