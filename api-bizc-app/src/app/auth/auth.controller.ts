import {
  Controller,
  Post,
  Body,
  UseGuards,
  Request,
  Get,
  Delete,
  Param,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { RegisterDto } from './dto/register.dto';
import { LoginDto } from './dto/login.dto';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { RefreshTokenDto } from './dto/refresh-token.dto';
import { Enable2FADto } from './dto/two-factor.dto';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { Throttle } from '@nestjs/throttler';
import { Roles } from './decorators/roles.decorator';
import { RolesGuard } from './guards/roles.guard';
import { SkipAuthentication } from './decorators/skip-authentication.decorator';
import { Verify2FADto, Verify2FAResponseDto } from './dto/verify-2fa.dto';
import { LoginResponseDto, Requires2FAResponseDto } from './dto/login.dto';
import { RegisterResponseDto } from './dto/register.dto';
import { CreateUserDto, CreateUserResponseDto } from './dto/create-user.dto';
import { SessionService } from './services/session.service';
import {
  CreateSessionDto,
  SessionDto,
  UserSessionsDto,
  SessionStatsDto,
  RevokeSessionDto,
  RevokeAllSessionsDto,
  SessionRevocationResponseDto,
  LoginWithSessionResponseDto,
} from './dto/session.dto';
import {
  SessionId,
  RequestContext,
  type RequestContextType,
} from '../shared/decorators';

@ApiTags('auth')
@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly sessionService: SessionService,
  ) {}

  @ApiOperation({ summary: 'Register new user' })
  @ApiResponse({
    status: 201,
    description: 'User successfully registered',
    type: RegisterResponseDto,
  })
  @ApiResponse({
    status: 400,
    description:
      'Bad request - Invalid registration data or user already exists',
  })
  @SkipAuthentication()
  @Post('register')
  async register(
    @Body() registerDto: RegisterDto,
  ): Promise<RegisterResponseDto> {
    const response = await this.authService.register(registerDto);
    console.log('response :::: ##### ((((( ', response);
    return response;
  }

  @ApiOperation({ summary: 'Login user' })
  @ApiResponse({
    status: 200,
    description: 'User successfully logged in',
    type: LoginResponseDto,
  })
  @ApiResponse({
    status: 200,
    description: '2FA verification required',
    type: Requires2FAResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Invalid credentials' })
  @SkipAuthentication()
  @Post('login')
  login(
    @Body() loginDto: LoginDto,
  ): Promise<LoginResponseDto | Requires2FAResponseDto> {
    return this.authService.login(loginDto);
  }

  @ApiOperation({ summary: 'Refresh access token' })
  @ApiResponse({
    status: 200,
    description: 'Tokens successfully refreshed',
    schema: {
      properties: {
        accessToken: { type: 'string', description: 'New JWT access token' },
        refreshToken: { type: 'string', description: 'New JWT refresh token' },
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Invalid refresh token' })
  @SkipAuthentication()
  @Post('refresh')
  refreshToken(@Body() refreshTokenDto: RefreshTokenDto) {
    return this.authService.refreshToken(refreshTokenDto.refreshToken);
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Generate 2FA secret and QR code' })
  @ApiResponse({
    status: 200,
    description: 'Returns 2FA secret and QR code',
    schema: {
      properties: {
        secret: { type: 'string', description: '2FA secret key' },
        qrCode: { type: 'string', description: 'QR code data URL' },
      },
    },
  })
  @Post('2fa/generate')
  generate2FA(@Request() req) {
    console.log('req.user :::: ##### ((((( ', req.user);
    return this.authService.generate2FASecret(req.user.id);
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Enable 2FA' })
  @Post('2fa/enable')
  enable2FA(@Request() req, @Body() enable2FADto: Enable2FADto) {
    return this.authService.enable2FA(req.user, enable2FADto);
  }

  @ApiOperation({ summary: 'Verify 2FA and complete login' })
  @ApiResponse({
    status: 200,
    description:
      'Returns access and refresh tokens after successful 2FA verification',
    type: Verify2FAResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid verification code or user ID',
  })
  @SkipAuthentication()
  @Post('2fa/verify')
  verify2FA(@Body() verify2FADto: Verify2FADto): Promise<Verify2FAResponseDto> {
    return this.authService.verify2FA(verify2FADto.userId, verify2FADto);
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Verify 2FA code without enabling it' })
  @ApiResponse({ status: 200, description: 'Returns verification status' })
  @Post('2fa/verify-setup')
  verify2FASetup(@Request() req, @Body() verify2FADto: Verify2FADto) {
    return this.authService.verify2FASetup(req.user, verify2FADto);
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Disable 2FA' })
  @ApiResponse({ status: 200, description: '2FA successfully disabled' })
  @Post('2fa/disable')
  disable2FA(@Request() req) {
    return this.authService.disable2FA(req.user);
  }

  @Throttle({ default: { limit: 3, ttl: 60000 } })
  @ApiOperation({ summary: 'Request password reset' })
  @ApiResponse({ status: 200, description: 'Password reset email sent' })
  @SkipAuthentication()
  @Post('forgot-password')
  forgotPassword(@Body() forgotPasswordDto: ForgotPasswordDto) {
    return this.authService.forgotPassword(forgotPasswordDto);
  }

  @ApiOperation({ summary: 'Reset password using token' })
  @ApiResponse({ status: 200, description: 'Password successfully reset' })
  @SkipAuthentication()
  @Post('reset-password')
  resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
    return this.authService.resetPassword(resetPasswordDto);
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create new user (Admin only)' })
  @ApiResponse({
    status: 201,
    description: 'User created successfully',
    type: CreateUserResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or user already exists',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication token',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Admin access required',
  })
  @UseGuards(RolesGuard)
  @Roles('admin')
  @Post('create-user')
  createUser(
    @Body() createUserDto: CreateUserDto,
  ): Promise<CreateUserResponseDto> {
    return this.authService.createUser(createUserDto);
  }

  // ========================================
  // SESSION MANAGEMENT ENDPOINTS
  // ========================================

  @ApiOperation({ summary: 'Get user active sessions' })
  @ApiResponse({
    status: 200,
    description: 'Active sessions retrieved successfully',
    type: UserSessionsDto,
  })
  @ApiBearerAuth()
  @Get('sessions')
  async getUserSessions(
    @Request() req: any,
    @SessionId() currentSessionId: string,
  ): Promise<UserSessionsDto> {
    const sessions = await this.sessionService.getUserActiveSessions(
      req.user.id,
    );

    return {
      currentSessionId,
      sessions: sessions as SessionDto[],
      totalActiveSessions: sessions.length,
    };
  }

  @ApiOperation({ summary: 'Get session statistics' })
  @ApiResponse({
    status: 200,
    description: 'Session statistics retrieved successfully',
    type: SessionStatsDto,
  })
  @ApiBearerAuth()
  @Get('sessions/stats')
  async getSessionStats(@Request() req: any): Promise<SessionStatsDto> {
    return this.sessionService.getUserSessionStats(req.user.id);
  }

  @ApiOperation({ summary: 'Get specific session details' })
  @ApiParam({
    name: 'sessionId',
    description: 'Session identifier',
    example: 'web_1640995200000_abc123',
  })
  @ApiResponse({
    status: 200,
    description: 'Session details retrieved successfully',
    type: SessionDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Session not found',
  })
  @ApiBearerAuth()
  @Get('sessions/:sessionId')
  async getSession(
    @Request() req: any,
    @Param('sessionId') sessionId: string,
  ): Promise<SessionDto> {
    const session = await this.sessionService.getSession(sessionId);

    if (!session || session.userId !== req.user.id) {
      throw new Error('Session not found');
    }

    return session as SessionDto;
  }

  @ApiOperation({ summary: 'End current session (logout)' })
  @ApiResponse({
    status: 200,
    description: 'Session ended successfully',
  })
  @ApiBearerAuth()
  @Post('logout')
  async logout(
    @Request() req: any,
    @SessionId() sessionId: string,
  ): Promise<{ message: string }> {
    await this.sessionService.endSession(sessionId, 'LOGOUT', req.user.id);
    return { message: 'Logged out successfully' };
  }

  @ApiOperation({ summary: 'Revoke a specific session' })
  @ApiParam({
    name: 'sessionId',
    description: 'Session identifier to revoke',
    example: 'web_1640995200000_abc123',
  })
  @ApiResponse({
    status: 200,
    description: 'Session revoked successfully',
    type: SessionRevocationResponseDto,
  })
  @ApiBearerAuth()
  @Delete('sessions/:sessionId')
  async revokeSession(
    @Request() req: any,
    @Param('sessionId') sessionId: string,
    @Body() revokeDto: RevokeSessionDto,
  ): Promise<SessionRevocationResponseDto> {
    // Verify session belongs to user
    const session = await this.sessionService.getSession(sessionId);
    if (!session || session.userId !== req.user.id) {
      throw new Error('Session not found');
    }

    await this.sessionService.revokeSession(sessionId, revokeDto.reason);

    return {
      revokedCount: 1,
      message: 'Session revoked successfully',
    };
  }

  @ApiOperation({ summary: 'Revoke all user sessions except current' })
  @ApiResponse({
    status: 200,
    description: 'Sessions revoked successfully',
    type: SessionRevocationResponseDto,
  })
  @ApiBearerAuth()
  @Post('sessions/revoke-all')
  async revokeAllSessions(
    @Request() req: any,
    @SessionId() currentSessionId: string,
    @Body() revokeAllDto: RevokeAllSessionsDto,
  ): Promise<SessionRevocationResponseDto> {
    const keepSessionId = revokeAllDto.keepSessionId || currentSessionId;

    const revokedCount = await this.sessionService.revokeAllUserSessions(
      req.user.id,
      keepSessionId,
      revokeAllDto.reason,
    );

    return {
      revokedCount,
      message: `${revokedCount} sessions revoked successfully`,
    };
  }

  @ApiOperation({ summary: 'Enhanced login with session management' })
  @ApiResponse({
    status: 200,
    description: 'Login successful with session created',
    type: LoginWithSessionResponseDto,
  })
  @ApiResponse({
    status: 202,
    description: '2FA required',
    type: Requires2FAResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Invalid credentials',
  })
  @SkipAuthentication()
  @Throttle({ default: { limit: 5, ttl: 60000 } })
  @Post('login-with-session')
  async loginWithSession(
    @Body() loginDto: LoginDto & CreateSessionDto,
    @RequestContext() context: RequestContextType,
  ): Promise<LoginWithSessionResponseDto | Requires2FAResponseDto> {
    // Standard login process
    const loginResult = await this.authService.login(loginDto);

    // If 2FA required, return early
    if ('requires2FA' in loginResult && loginResult.requires2FA) {
      return loginResult;
    }

    const fullLoginResult = loginResult as LoginResponseDto;

    // Create session for successful login
    const session = await this.sessionService.createSession({
      sessionId: loginDto.sessionId,
      userId: fullLoginResult.user._id,
      source: context.source,
      userAgent: context.userAgent,
      ipAddress: context.ipAddress,
      deviceId: loginDto.deviceId,
      deviceInfo: loginDto.deviceInfo,
      timezone: loginDto.timezone,
      expiresAt: loginDto.expiresAt ? new Date(loginDto.expiresAt) : undefined,
    });

    return {
      accessToken: fullLoginResult.tokens.accessToken,
      refreshToken: fullLoginResult.tokens.refreshToken,
      user: {
        id: fullLoginResult.user._id,
        email: fullLoginResult.user.email,
        firstName: fullLoginResult.user.firstName,
        lastName: fullLoginResult.user.lastName,
        role: fullLoginResult.user.role,
      },
      session: session as SessionDto,
    };
  }
}
