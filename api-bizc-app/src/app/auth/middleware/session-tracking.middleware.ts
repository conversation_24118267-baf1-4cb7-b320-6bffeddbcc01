import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { SessionService } from '../services/session.service';

/**
 * Session Tracking Middleware
 *
 * Automatically tracks user sessions and updates session activity.
 * Should be applied to authenticated routes.
 */
@Injectable()
export class SessionTrackingMiddleware implements NestMiddleware {
  private readonly logger = new Logger(SessionTrackingMiddleware.name);

  constructor(private sessionService: SessionService) {}

  async use(
    req: Request & { user?: any; sessionId?: string },
    res: Response,
    next: NextFunction,
  ) {
    try {
      // Only process if user is authenticated
      if (!req.user?.id) {
        return next();
      }

      // Extract session ID from various sources
      const sessionId = this.extractSessionId(req);
      if (!sessionId) {
        return next();
      }

      // Attach session ID to request for controllers
      req.sessionId = sessionId;

      // Track session activity asynchronously (don't block request)
      this.trackSessionActivity(sessionId, req).catch((error) => {
        this.logger.error('Failed to track session activity', {
          error: error.message,
          sessionId,
          userId: req.user.id,
        });
      });

      next();
    } catch (error) {
      this.logger.error('Session tracking middleware error', {
        error: error.message,
        userId: req.user?.id,
      });
      // Don't block request on session tracking errors
      next();
    }
  }

  /**
   * Extract session ID from request
   */
  private extractSessionId(req: Request): string | null {
    return (
      (req.headers['x-session-id'] as string) ||
      (req.query?.sessionId as string) ||
      req.body?.sessionId ||
      null
    );
  }

  /**
   * Track session activity
   */
  private async trackSessionActivity(
    sessionId: string,
    req: Request & { user?: any },
  ): Promise<void> {
    const activityType = `${req.method} ${req.route?.path || req.path}`;
    const ipAddress = req.ip || req.connection?.remoteAddress;

    await this.sessionService.trackSessionActivity(
      sessionId,
      activityType,
      ipAddress,
    );
  }
}
