# ActivityMetadata Decorator

## Overview
The `@ActivityMetadata()` decorator automatically extracts request metadata for activity logging purposes. It provides a clean and consistent way to capture request context information across all controller endpoints.

## Fixed Issues
- ✅ **Naming Conflict**: Resolved TypeScript error by importing interface as `ActivityMetadataType` 
- ✅ **Deprecated Property**: Replaced `request.connection` with `request.socket` for Express compatibility
- ✅ **Unused Parameter**: Prefixed unused parameter with underscore (`_data`)

## Usage

### Basic Usage
```typescript
import { ActivityMetadata } from '../auth/decorators/activity-metadata.decorator';
import type { ActivityMetadata as ActivityMetadataType } from '../shared/types/activity-metadata.type';

@Controller('example')
export class ExampleController {
  
  @Post()
  create(
    @Request() req,
    @Body() createDto: CreateDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ) {
    // metadata contains: { ipAddress, userAgent, sessionId }
    // sessionId is automatically validated for security
    return this.service.create(req.user.id, createDto, metadata);
  }
}
```

### Advanced Usage with Options
```typescript
@Controller('example')
export class ExampleController {
  
  @Post('public')
  createPublic(
    @Request() req,
    @Body() createDto: CreateDto,
    @ActivityMetadata({ validateSessionId: false }) metadata: ActivityMetadataType,
  ) {
    // Skip session ID validation for public endpoints
    return this.service.createPublic(createDto, metadata);
  }

  @Post('strict')
  createStrict(
    @Request() req,
    @Body() createDto: CreateDto,
    @ActivityMetadata({ logInvalidSessions: true }) metadata: ActivityMetadataType,
  ) {
    // Log invalid session attempts for security monitoring
    return this.service.create(req.user.id, createDto, metadata);
  }
}
```

### In Service Layer
```typescript
import { ActivityMetadata } from '../shared/types/activity-metadata.type';

@Injectable()
export class ExampleService {
  
  async create(
    userId: string,
    dto: CreateDto,
    metadata?: ActivityMetadata,
  ) {
    // Use metadata for activity logging
    await this.activityLogService.logCreate(
      entityId,
      EntityType.EXAMPLE,
      userId,
      businessId,
      {
        reason: 'Entity created',
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      }
    );
  }
}
```

## Metadata Properties

| Property | Type | Source | Description |
|----------|------|--------|-------------|
| `ipAddress` | `string?` | `req.ip` or `req.socket.remoteAddress` | Client IP address |
| `userAgent` | `string?` | `req.headers['user-agent']` | User agent string |
| `sessionId` | `string?` | Multiple sources* | Session identifier |

*Session ID Sources (in priority order):
1. `req.sessionId` (from SessionTrackingMiddleware)
2. `req.headers['x-session-id']` (custom header)
3. `req.query.sessionId` (query parameter)
4. `req.body.sessionId` (request body)

## Session ID Validation

### Security Features
The decorator includes built-in validation to prevent security issues:

**Validation Rules:**
- Length: 8-128 characters
- Characters: Alphanumeric + hyphens, underscores, dots
- Security: Blocks path traversal (`..`), SQL injection (`--`), XSS patterns

**Invalid Session ID Examples:**
```typescript
// These will be rejected:
'short'                    // Too short (< 8 chars)
'<script>alert(1)</script>' // Contains XSS
'../../../etc/passwd'      // Path traversal attempt
'session--drop-table'      // SQL injection pattern
```

**Valid Session ID Examples:**
```typescript
// These will be accepted:
'session-123abc'
'user_session_456def'
'app.session.789ghi'
'mobile-app-session-123456789'
```

### Configuration Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `validateSessionId` | `boolean` | `true` | Enable/disable session ID validation |
| `logInvalidSessions` | `boolean` | `true` | Log security events for invalid sessions |

### Security Logging
When invalid session IDs are detected, the system logs:
- Partial session ID (first 8 chars + ***)
- Source IP address
- User agent
- Session ID source (middleware/header/query/body)

```typescript
// Example log entry:
{
  level: 'warn',
  message: 'Invalid session ID detected',
  sessionId: 'invalid***',
  ipAddress: '*************',
  userAgent: 'Mozilla/5.0...',
  source: 'header'
}
```

## Integration with Session Management

The decorator works seamlessly with the existing session management system:

```typescript
// SessionTrackingMiddleware attaches sessionId to request
req.sessionId = extractedSessionId;

// ActivityMetadata decorator picks it up automatically
@ActivityMetadata() metadata: ActivityMetadataType
// metadata.sessionId will contain the session ID
```

## Benefits

1. **DRY Principle**: No more repetitive metadata extraction
2. **Type Safety**: Consistent typing across application
3. **Maintainability**: Centralized extraction logic
4. **Testing**: Decorator is unit testable
5. **Extensibility**: Easy to add new metadata fields
6. **Backward Compatibility**: Works with existing session middleware

## Testing

The decorator includes comprehensive unit tests that verify:
- Metadata extraction from request
- IP address fallback mechanism
- Session ID extraction from multiple sources
- Proper TypeScript typing

Run tests with:
```bash
npm test activity-metadata.decorator.test.ts
```

## Error Handling

The decorator gracefully handles missing or undefined properties:
- If `req.ip` is undefined, falls back to `req.socket.remoteAddress`
- If session ID is not found in any source, returns `undefined`
- All metadata properties are optional, preventing runtime errors