import { ExecutionContext } from '@nestjs/common';
import { ActivityMetadata } from './activity-metadata.decorator';

describe('ActivityMetadata Decorator', () => {
  const mockExecutionContext = {
    switchToHttp: () => ({
      getRequest: () => ({
        ip: '***********',
        headers: { 'user-agent': 'Mozilla/5.0 Test Browser' },
        sessionId: 'test-session-123',
        query: {},
        body: {},
        socket: { remoteAddress: '********' },
      }),
    }),
  } as ExecutionContext;

  it('should extract metadata from request', () => {
    const decorator = ActivityMetadata(undefined, mockExecutionContext);

    expect(decorator).toEqual({
      ipAddress: '***********',
      userAgent: 'Mozilla/5.0 Test Browser',
      sessionId: 'test-session-123',
    });
  });

  it('should use fallback IP when req.ip is not available', () => {
    const mockContext = {
      switchToHttp: () => ({
        getRequest: () => ({
          headers: { 'user-agent': 'Test Browser' },
          socket: { remoteAddress: '********' },
          query: {},
          body: {},
        }),
      }),
    } as ExecutionContext;

    const decorator = ActivityMetadata(undefined, mockContext);

    expect(decorator.ipAddress).toBe('********');
  });

  it('should extract sessionId from header', () => {
    const mockContext = {
      switchToHttp: () => ({
        getRequest: () => ({
          headers: {
            'user-agent': 'Test Browser',
            'x-session-id': 'header-session-123',
          },
          query: {},
          body: {},
        }),
      }),
    } as ExecutionContext;

    const decorator = ActivityMetadata(undefined, mockContext);

    expect(decorator.sessionId).toBe('header-session-123');
  });

  it('should extract sessionId from query params', () => {
    const mockContext = {
      switchToHttp: () => ({
        getRequest: () => ({
          headers: { 'user-agent': 'Test Browser' },
          query: { sessionId: 'query-session-456' },
          body: {},
        }),
      }),
    } as ExecutionContext;

    const decorator = ActivityMetadata(undefined, mockContext);

    expect(decorator.sessionId).toBe('query-session-456');
  });

  it('should extract sessionId from body', () => {
    const mockContext = {
      switchToHttp: () => ({
        getRequest: () => ({
          headers: { 'user-agent': 'Test Browser' },
          query: {},
          body: { sessionId: 'body-session-789' },
        }),
      }),
    } as ExecutionContext;

    const decorator = ActivityMetadata(undefined, mockContext);

    expect(decorator.sessionId).toBe('body-session-789');
  });

  it('should reject invalid session IDs when validation is enabled', () => {
    const mockContext = {
      switchToHttp: () => ({
        getRequest: () => ({
          headers: {
            'user-agent': 'Test Browser',
            'x-session-id': 'invalid<script>alert(1)</script>',
          },
          query: {},
          body: {},
        }),
      }),
    } as ExecutionContext;

    const decorator = ActivityMetadata({}, mockContext);

    expect(decorator.sessionId).toBeUndefined();
  });

  it('should allow invalid session IDs when validation is disabled', () => {
    const mockContext = {
      switchToHttp: () => ({
        getRequest: () => ({
          headers: {
            'user-agent': 'Test Browser',
            'x-session-id': 'short',
          },
          query: {},
          body: {},
        }),
      }),
    } as ExecutionContext;

    const decorator = ActivityMetadata(
      { validateSessionId: false },
      mockContext,
    );

    expect(decorator.sessionId).toBe('short');
  });

  it('should accept valid session IDs', () => {
    const mockContext = {
      switchToHttp: () => ({
        getRequest: () => ({
          headers: {
            'user-agent': 'Test Browser',
            'x-session-id': 'valid-session-123abc',
          },
          query: {},
          body: {},
        }),
      }),
    } as ExecutionContext;

    const decorator = ActivityMetadata({}, mockContext);

    expect(decorator.sessionId).toBe('valid-session-123abc');
  });

  it('should trim whitespace from session IDs', () => {
    const mockContext = {
      switchToHttp: () => ({
        getRequest: () => ({
          headers: {
            'user-agent': 'Test Browser',
            'x-session-id': '  valid-session-123  ',
          },
          query: {},
          body: {},
        }),
      }),
    } as ExecutionContext;

    const decorator = ActivityMetadata({}, mockContext);

    expect(decorator.sessionId).toBe('valid-session-123');
  });
});
