import { createParamDecorator, ExecutionContext, Logger } from '@nestjs/common';
import { Request } from 'express';
import { ActivityMetadata as ActivityMetadataType } from '../../shared/types/activity-metadata.type';
import { detectSourceFromRequest } from '../../shared/decorators/activity-source.decorator';

interface ActivityMetadataOptions {
  validateSessionId?: boolean;
  logInvalidSessions?: boolean;
  detectSource?: boolean;
}

const logger = new Logger('ActivityMetadata');

/**
 * Parameter decorator to extract activity metadata from request
 *
 * Usage:
 * @Post()
 * create(@ActivityMetadata() metadata: ActivityMetadataType) {
 *   // metadata contains ipAddress, userAgent, sessionId, and source
 * }
 *
 * With options:
 * @Post()
 * create(@ActivityMetadata({
 *   validateSessionId: false,
 *   detectSource: false
 * }) metadata: ActivityMetadataType) {
 *   // Skip session ID validation and source detection
 * }
 */
export const ActivityMetadata = createParamDecorator(
  (
    options: ActivityMetadataOptions = {},
    ctx: ExecutionContext,
  ): ActivityMetadataType => {
    const request = ctx
      .switchToHttp()
      .getRequest<
        Request & { sessionId?: string; socket?: { remoteAddress?: string } }
      >();

    const {
      validateSessionId = true,
      logInvalidSessions = true,
      detectSource = true,
    } = options;

    return {
      ipAddress: request.ip || request.socket?.remoteAddress,
      userAgent: request.headers['user-agent'],
      sessionId: extractSessionId(request, {
        validateSessionId,
        logInvalidSessions,
      }),
      source: detectSource ? detectSourceFromRequest(request) : undefined,
    };
  },
);

/**
 * Extract session ID from various sources in the request
 */
function extractSessionId(
  req: Request & { sessionId?: string },
  options: { validateSessionId: boolean; logInvalidSessions: boolean } = {
    validateSessionId: true,
    logInvalidSessions: true,
  },
): string | undefined {
  const sessionId =
    req.sessionId || // From session tracking middleware
    (req.headers['x-session-id'] as string) ||
    (req.query?.sessionId as string) ||
    req.body?.sessionId ||
    undefined;

  // Basic validation: session ID should be a non-empty string
  if (
    sessionId &&
    typeof sessionId === 'string' &&
    sessionId.trim().length > 0
  ) {
    const trimmedSessionId = sessionId.trim();

    // Skip validation if disabled
    if (!options.validateSessionId) {
      return trimmedSessionId;
    }

    // Validate session ID format for security
    if (isValidSessionId(trimmedSessionId)) {
      return trimmedSessionId;
    } else if (options.logInvalidSessions) {
      // Log security event for invalid session ID
      logger.warn('Invalid session ID detected', {
        sessionId: trimmedSessionId.substring(0, 8) + '***', // Partial for security
        ipAddress: req.ip || req.socket?.remoteAddress,
        userAgent: req.headers['user-agent'],
        source: req.sessionId
          ? 'middleware'
          : req.headers['x-session-id']
            ? 'header'
            : req.query?.sessionId
              ? 'query'
              : 'body',
      });
    }
  }

  return undefined;
}

/**
 * Validate session ID format and length
 * Basic validation to prevent malicious or malformed session IDs
 */
function isValidSessionId(sessionId: string): boolean {
  // Session ID should be:
  // - Between 8 and 128 characters
  // - Alphanumeric with allowed special chars (-, _, .)
  // - Not contain suspicious patterns
  const sessionIdRegex = /^[a-zA-Z0-9\-_.]{8,128}$/;

  return (
    sessionIdRegex.test(sessionId) &&
    !sessionId.includes('..') && // Prevent path traversal
    !sessionId.includes('--') && // Prevent SQL injection patterns
    !sessionId.includes('script') && // Prevent XSS patterns
    !sessionId.includes('<') &&
    !sessionId.includes('>')
  );
}
