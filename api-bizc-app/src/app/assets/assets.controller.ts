import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  Query,
  UseGuards,
  UseInterceptors,
  UploadedFiles,
} from '@nestjs/common';
import {
  FilesInterceptor,
  FileFieldsInterceptor,
} from '@nestjs/platform-express';
import { AssetsService } from './assets.service';
import { CreateAssetDto } from './dto/create-asset.dto';
import { UpdateAssetDto } from './dto/update-asset.dto';
import { AssetDto } from './dto/asset.dto';
import { AssetSlimDto } from './dto/asset-slim.dto';
import { AssetIdResponseDto } from './dto/asset-id-response.dto';
import { DeleteAssetResponseDto } from './dto/delete-asset-response.dto';
import { PaginatedAssetsOptimizedResponseDto } from './dto/paginated-assets-optimized-response.dto';
import { BulkCreateAssetDto } from './dto/bulk-create-asset.dto';
import { BulkAssetIdsResponseDto } from './dto/bulk-asset-ids-response.dto';
import { BulkDeleteAssetDto } from './dto/bulk-delete-asset.dto';
import { BulkDeleteAssetResponseDto } from './dto/bulk-delete-asset-response.dto';
import { BulkUpdateAssetStatusDto } from './dto/bulk-update-asset-status.dto';
import { BulkUpdateAssetStatusResponseDto } from './dto/bulk-update-asset-status-response.dto';
import {
  CreateAssetImageDto,
  UpdateAssetImagesSortOrderDto,
} from './dto/asset-image.dto';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { ActivityMetadata } from '../auth/decorators/activity-metadata.decorator';
import { Permission } from '../shared/types/permission.enum';
import { AssetStatus } from '../drizzle/schema/assets.schema';
import type { ActivityMetadata as ActivityMetadataType } from '../shared/types/activity-metadata.type';
import { AssetNameAvailabilityResponseDto } from './dto/check-asset-name.dto';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
  ApiBody,
  ApiConsumes,
} from '@nestjs/swagger';

interface AuthenticatedRequest {
  user: {
    id: string;
    activeBusinessId: string | null;
  };
}

@ApiTags('assets')
@Controller('assets')
@UseGuards(PermissionsGuard)
export class AssetsController {
  constructor(private readonly assetsService: AssetsService) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_CREATE)
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'images', maxCount: 10 },
      { name: 'attachments', maxCount: 10 },
    ]),
  )
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Create a new asset with optional file uploads',
    description:
      'Create a new asset with support for multiple image and attachment uploads',
  })
  @ApiBody({
    description: 'Asset creation with optional image and attachment uploads',
    schema: {
      type: 'object',
      properties: {
        assetCode: {
          type: 'string',
          example: 'AST001',
          description: 'Asset code (required, unique per business)',
        },
        name: {
          type: 'string',
          example: 'Laptop Dell XPS 15',
          description: 'Asset name (required)',
        },
        type: {
          type: 'string',
          enum: [
            'it_equipment',
            'furniture',
            'vehicle',
            'machinery',
            'building',
            'office_equipment',
            'software',
            'other',
          ],
          example: 'it_equipment',
          description: 'Asset type',
        },
        description: {
          type: 'string',
          example: 'High-performance laptop for development work',
          description: 'Asset description',
        },
        purchasePrice: {
          type: 'string',
          example: '1500.00',
          description: 'Purchase price',
        },
        purchaseDate: {
          type: 'string',
          format: 'date',
          example: '2024-01-15',
          description: 'Purchase date (YYYY-MM-DD)',
        },
        status: {
          type: 'string',
          enum: [
            'available',
            'unavailable',
            'assigned',
            'discarded',
            'lost',
            'stolen',
            'damaged',
            'maintenance',
            'allocated',
          ],
          example: 'available',
          description: 'Asset status',
        },
        images: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
          description: 'Asset image files (up to 10 images)',
        },
        attachments: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
          description:
            'Asset attachment files - documents, manuals, etc. (up to 10 files)',
        },
      },
      required: ['assetCode', 'name'],
    },
  })
  @ApiResponse({
    status: 201,
    description: 'The asset has been successfully created',
    type: AssetIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Asset code already exists',
  })
  create(
    @Request() req: AuthenticatedRequest,
    @Body() createAssetDto: CreateAssetDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
    @UploadedFiles()
    files?: {
      images?: Express.Multer.File[];
      attachments?: Express.Multer.File[];
    },
  ): Promise<AssetIdResponseDto> {
    const images = files?.images || [];
    const attachments = files?.attachments || [];

    return this.assetsService.createWithFiles(
      req.user.id,
      req.user.activeBusinessId,
      createAssetDto,
      images,
      attachments,
      metadata,
    );
  }

  @Post('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_CREATE)
  @UseInterceptors(FilesInterceptor('images'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Bulk create assets with optional images' })
  @ApiBody({
    description:
      'Bulk asset creation with optional image uploads. Images can be mapped to assets using field names like "image_0", "image_1", etc., or by using the assets array with imageIndex property.',
    schema: {
      type: 'object',
      properties: {
        assets: {
          type: 'string',
          description:
            'JSON string containing array of asset objects. Each asset can optionally include an "imageIndex" property to specify which image file to use.',
          example:
            '[{"assetCode":"AST001","name":"Laptop Dell","status":"available","imageIndex":0},{"assetCode":"AST002","name":"Desktop HP","status":"available"},{"assetCode":"AST003","name":"Monitor Samsung","status":"available","imageIndex":1}]',
        },
        images: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
          description:
            'Array of asset image files (optional). Images are mapped to assets using the imageIndex property in the assets array.',
        },
      },
      required: ['assets'],
    },
  })
  @ApiResponse({
    status: 201,
    description: 'The assets have been successfully created',
    type: BulkAssetIdsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async bulkCreate(
    @Request() req: AuthenticatedRequest,
    @Body() bulkCreateAssetDto: BulkCreateAssetDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkAssetIdsResponseDto> {
    return this.assetsService.bulkCreate(
      req.user.id,
      req.user.activeBusinessId,
      bulkCreateAssetDto.assets,
      metadata,
    );
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_READ)
  @ApiOperation({
    summary: 'Get all assets for the active business',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'from',
    description: 'Start date for filtering (YYYY-MM-DD)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'to',
    description: 'End date for filtering (YYYY-MM-DD)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'name',
    description: 'Filter by asset name',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'assetCode',
    description: 'Filter by asset code',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'status',
    description: 'Filter by asset status',
    required: false,
    enum: AssetStatus,
  })
  @ApiQuery({
    name: 'type',
    description: 'Filter by asset type (comma-separated enum values)',
    required: false,
    type: String,
    example: 'it_equipment,furniture',
  })
  @ApiQuery({
    name: 'categoryId',
    description: 'Filter by category ID (comma-separated UUIDs)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'subCategoryId',
    description: 'Filter by sub-category ID (comma-separated UUIDs)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'locationId',
    description: 'Filter by location ID (deprecated - use join table)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'filters',
    description: 'Additional filters in JSON format',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'joinOperator',
    description: 'Join operator for multiple filters',
    required: false,
    enum: ['and', 'or'],
  })
  @ApiQuery({
    name: 'sort',
    description: 'Sort order (field:direction)',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description:
      'Returns paginated list of assets with optimized data and relationships',
    type: PaginatedAssetsOptimizedResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Request() req: AuthenticatedRequest,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('name') name?: string,
    @Query('assetCode') assetCode?: string,
    @Query('status') status?: string,
    @Query('type') type?: string,
    @Query('categoryId') categoryId?: string,
    @Query('subCategoryId') subCategoryId?: string,
    @Query('locationId') locationId?: string,
    @Query('filters') filters?: string,
    @Query('joinOperator') joinOperator?: 'and' | 'or',
    @Query('sort') sort?: string,
  ): Promise<PaginatedAssetsOptimizedResponseDto> {
    return this.assetsService.findAllOptimized(
      req.user.id,
      req.user.activeBusinessId,
      page ? parseInt(page.toString()) : undefined,
      limit ? parseInt(limit.toString()) : undefined,
      from,
      to,
      name,
      assetCode,
      status,
      type,
      categoryId,
      subCategoryId,
      locationId,
      filters,
      joinOperator,
      sort,
    );
  }

  @Get('slim')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_READ)
  @ApiOperation({
    summary: 'Get all assets in slim format (minimal data)',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns all assets with minimal data',
    type: [AssetSlimDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllSlim(@Request() req: AuthenticatedRequest): Promise<AssetSlimDto[]> {
    return this.assetsService.findAllSlim(
      req.user.id,
      req.user.activeBusinessId,
    );
  }

  @Get('check-availability')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_READ)
  @ApiOperation({ summary: 'Check if an asset code is available' })
  @ApiQuery({
    name: 'assetCode',
    description: 'Asset code to check',
    required: true,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Returns whether the asset code is available',
    type: AssetNameAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkAssetCodeAvailability(
    @Request() req: AuthenticatedRequest,
    @Query('assetCode') assetCode: string,
  ): Promise<{ available: boolean }> {
    return this.assetsService.checkAssetCodeAvailability(
      req.user.id,
      req.user.activeBusinessId,
      assetCode,
    );
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_READ)
  @ApiOperation({ summary: 'Get an asset by ID' })
  @ApiParam({
    name: 'id',
    description: 'Asset ID',
    type: String,
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the asset with the specified ID',
    type: AssetDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Asset not found',
  })
  findOne(
    @Request() req: AuthenticatedRequest,
    @Param('id') id: string,
  ): Promise<AssetDto> {
    return this.assetsService.findOne(
      req.user.id,
      req.user.activeBusinessId,
      id,
    );
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_UPDATE)
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'images', maxCount: 10 },
      { name: 'attachments', maxCount: 10 },
    ]),
  )
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Update an asset with file management',
    description:
      'Update asset information with support for image and attachment uploads and management',
  })
  @ApiParam({
    name: 'id',
    description: 'Asset ID',
    type: String,
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiResponse({
    status: 200,
    description: 'The asset has been successfully updated',
    type: AssetIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Asset not found',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Asset code already exists',
  })
  update(
    @Request() req: AuthenticatedRequest,
    @Param('id') id: string,
    @Body() updateAssetDto: UpdateAssetDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
    @UploadedFiles()
    files?: {
      images?: Express.Multer.File[];
      attachments?: Express.Multer.File[];
    },
  ): Promise<AssetIdResponseDto> {
    const images = files?.images || [];
    const attachments = files?.attachments || [];

    return this.assetsService.updateWithFiles(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateAssetDto,
      images,
      attachments,
      metadata,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_DELETE)
  @ApiOperation({ summary: 'Delete an asset' })
  @ApiParam({
    name: 'id',
    description: 'Asset ID',
    type: String,
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiResponse({
    status: 200,
    description: 'The asset has been successfully deleted',
    type: DeleteAssetResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Asset not found',
  })
  remove(
    @Request() req: AuthenticatedRequest,
    @Param('id') id: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<DeleteAssetResponseDto> {
    return this.assetsService.remove(
      req.user.id,
      req.user.activeBusinessId,
      id,
      metadata,
    );
  }

  @Delete('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_DELETE)
  @ApiOperation({ summary: 'Bulk delete assets' })
  @ApiBody({
    description: 'Array of asset IDs to delete',
    type: BulkDeleteAssetDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Assets have been successfully deleted',
    type: BulkDeleteAssetResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or assets not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'One or more assets not found',
  })
  async bulkDelete(
    @Request() req: AuthenticatedRequest,
    @Body() bulkDeleteAssetDto: BulkDeleteAssetDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkDeleteAssetResponseDto> {
    return this.assetsService.bulkDelete(
      req.user.id,
      req.user.activeBusinessId,
      bulkDeleteAssetDto.ids,
      metadata,
    );
  }

  @Patch('bulk-status')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_UPDATE)
  @ApiOperation({ summary: 'Bulk update asset status' })
  @ApiBody({
    description: 'Array of asset IDs and status to update',
    type: BulkUpdateAssetStatusDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Asset status has been successfully updated',
    type: BulkUpdateAssetStatusResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or assets not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async bulkUpdateStatus(
    @Request() req: AuthenticatedRequest,
    @Body() bulkUpdateStatusDto: BulkUpdateAssetStatusDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkUpdateAssetStatusResponseDto> {
    return this.assetsService.bulkUpdateStatus(
      req.user.id,
      req.user.activeBusinessId,
      bulkUpdateStatusDto.ids,
      bulkUpdateStatusDto.status,
      metadata,
    );
  }

  @Post(':id/images')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_UPDATE)
  @ApiOperation({
    summary: 'Add images to an asset',
    description: 'Add multiple images to an asset with sort order',
  })
  @ApiParam({
    name: 'id',
    description: 'Asset ID',
    type: String,
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiBody({
    description: 'Array of image data to add to the asset',
    type: [CreateAssetImageDto],
  })
  @ApiResponse({
    status: 201,
    description: 'Images have been successfully added to the asset',
    schema: {
      type: 'object',
      properties: {
        images: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: {
                type: 'string',
                description: 'Asset image ID',
              },
            },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Asset not found',
  })
  async addAssetImages(
    @Request() req: AuthenticatedRequest,
    @Param('id') id: string,
    @Body() images: CreateAssetImageDto[],
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<{ images: { id: string }[] }> {
    const result = await this.assetsService.addAssetImages(
      req.user.id,
      req.user.activeBusinessId,
      id,
      images,
      metadata,
    );
    return { images: result };
  }

  @Patch(':id/images/sort-order')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_UPDATE)
  @ApiOperation({
    summary: 'Update asset images sort order',
    description: 'Update the sort order of asset images',
  })
  @ApiParam({
    name: 'id',
    description: 'Asset ID',
    type: String,
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiBody({
    description: 'Sort order updates for asset images',
    type: UpdateAssetImagesSortOrderDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Image sort order has been successfully updated',
    schema: {
      type: 'object',
      properties: {
        updated: {
          type: 'number',
          description: 'Number of images updated',
        },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Asset not found',
  })
  async updateAssetImagesSortOrder(
    @Request() req: AuthenticatedRequest,
    @Param('id') id: string,
    @Body() updates: UpdateAssetImagesSortOrderDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<{ updated: number }> {
    return this.assetsService.updateAssetImagesSortOrder(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updates,
      metadata,
    );
  }

  @Delete(':id/images/:imageId')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_UPDATE)
  @ApiOperation({
    summary: 'Remove an asset image',
    description: 'Remove a specific image from an asset',
  })
  @ApiParam({ name: 'id', description: 'Asset ID' })
  @ApiParam({ name: 'imageId', description: 'Image ID to remove' })
  @ApiResponse({
    status: 200,
    description: 'Image has been successfully removed',
    schema: {
      type: 'object',
      properties: {
        deleted: {
          type: 'boolean',
          description: 'Whether the image was deleted',
        },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Asset or image not found',
  })
  async removeAssetImage(
    @Request() req: AuthenticatedRequest,
    @Param('id') id: string,
    @Param('imageId') imageId: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<{ deleted: boolean }> {
    return this.assetsService.removeAssetImage(
      req.user.id,
      req.user.activeBusinessId,
      id,
      imageId,
      metadata,
    );
  }

  @Patch(':id/images/:imageId/primary')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_UPDATE)
  @ApiOperation({
    summary: 'Set primary image',
    description: 'Set a specific image as the primary image for an asset',
  })
  @ApiParam({ name: 'id', description: 'Asset ID' })
  @ApiParam({ name: 'imageId', description: 'Image ID to set as primary' })
  @ApiResponse({
    status: 200,
    description: 'Primary image set successfully',
    schema: {
      type: 'object',
      properties: {
        updated: {
          type: 'boolean',
          description: 'Whether the primary image was updated',
        },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Asset or image not found',
  })
  async setAssetPrimaryImage(
    @Request() req: AuthenticatedRequest,
    @Param('id') id: string,
    @Param('imageId') imageId: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<{ updated: boolean }> {
    return this.assetsService.setAssetPrimaryImage(
      req.user.id,
      req.user.activeBusinessId,
      id,
      imageId,
      metadata,
    );
  }

  @Get(':id/attachments')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_READ)
  @ApiOperation({
    summary: 'Get asset attachments',
    description: 'Get all attachments for an asset',
  })
  @ApiParam({ name: 'id', description: 'Asset ID' })
  @ApiResponse({
    status: 200,
    description: 'Asset attachments retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        attachments: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string', description: 'Media ID' },
              fileName: { type: 'string', description: 'File name' },
              originalName: {
                type: 'string',
                description: 'Original file name',
              },
              publicUrl: { type: 'string', description: 'Public URL' },
              size: { type: 'number', description: 'File size in bytes' },
              mimeType: { type: 'string', description: 'MIME type' },
              mediaType: { type: 'string', description: 'Media type' },
              uploadedAt: { type: 'string', description: 'Upload timestamp' },
            },
          },
        },
      },
    },
  })
  async getAssetAttachments(
    @Request() req: AuthenticatedRequest,
    @Param('id') id: string,
  ) {
    return this.assetsService.getAssetAttachments(
      req.user.id,
      req.user.activeBusinessId,
      id,
    );
  }

  @Post(':id/attachments')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_UPDATE)
  @UseInterceptors(FilesInterceptor('attachments', 10))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Add attachments to an asset',
    description: 'Add multiple attachment files to an asset',
  })
  @ApiParam({ name: 'id', description: 'Asset ID' })
  @ApiBody({
    description: 'Attachment files to upload',
    schema: {
      type: 'object',
      properties: {
        attachments: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
          description: 'Attachment files to upload (up to 10 files)',
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Attachments uploaded successfully',
    schema: {
      type: 'object',
      properties: {
        attachments: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string', description: 'Media ID' },
              fileName: { type: 'string', description: 'File name' },
              originalName: {
                type: 'string',
                description: 'Original file name',
              },
            },
          },
        },
      },
    },
  })
  async addAssetAttachments(
    @Request() req: AuthenticatedRequest,
    @Param('id') id: string,
    @UploadedFiles() attachments: Express.Multer.File[],
    @ActivityMetadata() metadata: ActivityMetadataType,
  ) {
    return this.assetsService.addAssetAttachments(
      req.user.id,
      req.user.activeBusinessId,
      id,
      attachments || [],
      metadata,
    );
  }

  @Delete(':id/attachments/:attachmentId')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_UPDATE)
  @ApiOperation({
    summary: 'Remove an asset attachment',
    description: 'Remove a specific attachment from an asset',
  })
  @ApiParam({ name: 'id', description: 'Asset ID' })
  @ApiParam({ name: 'attachmentId', description: 'Attachment ID to remove' })
  @ApiResponse({
    status: 200,
    description: 'Attachment removed successfully',
    schema: {
      type: 'object',
      properties: {
        deleted: {
          type: 'boolean',
          description: 'Whether the attachment was deleted',
        },
      },
    },
  })
  async removeAssetAttachment(
    @Request() req: AuthenticatedRequest,
    @Param('id') id: string,
    @Param('attachmentId') attachmentId: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ) {
    return this.assetsService.removeAssetAttachment(
      req.user.id,
      req.user.activeBusinessId,
      id,
      attachmentId,
      metadata,
    );
  }
}
