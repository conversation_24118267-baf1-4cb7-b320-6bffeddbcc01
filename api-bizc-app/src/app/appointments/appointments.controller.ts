import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { AppointmentsService } from './appointments.service';
import { CreateAppointmentDto } from './dto/create-appointment.dto';
import { UpdateAppointmentDto } from './dto/update-appointment.dto';
import { AppointmentDto } from './dto/appointment.dto';
import { AppointmentSlimDto } from './dto/appointment-slim.dto';
import { AppointmentIdResponseDto } from './dto/appointment-id-response.dto';
import { BulkAppointmentIdsResponseDto } from './dto/bulk-appointment-ids-response.dto';
import { BulkCreateAppointmentDto } from './dto/bulk-create-appointment.dto';
import { BulkDeleteAppointmentDto } from './dto/bulk-delete-appointment.dto';
import { BulkDeleteAppointmentResponseDto } from './dto/bulk-delete-appointment-response.dto';
import { BulkUpdateAppointmentStatusDto } from './dto/bulk-update-appointment-status.dto';
import { BulkUpdateAppointmentStatusResponseDto } from './dto/bulk-update-appointment-status-response.dto';
import { DeleteAppointmentResponseDto } from './dto/delete-appointment-response.dto';
import { PaginatedAppointmentsResponseDto } from './dto/paginated-appointments-response.dto';
import { AppointmentNameAvailabilityResponseDto } from './dto/check-appointment-name.dto';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { ActivityMetadata } from '../auth/decorators/activity-metadata.decorator';
import { Permission } from '../shared/types/permission.enum';
import type { ActivityMetadata as ActivityMetadataType } from '../shared/types/activity-metadata.type';

@ApiTags('appointments')
@Controller('appointments')
@UseGuards(PermissionsGuard)
export class AppointmentsController {
  constructor(private readonly appointmentsService: AppointmentsService) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.APPOINTMENT_CREATE)
  @ApiOperation({
    summary: 'Create a new appointment',
  })
  @ApiBody({
    description: 'Appointment creation data',
    type: CreateAppointmentDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Appointment created successfully',
    type: AppointmentIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Appointment number already exists',
  })
  create(
    @Request() req,
    @Body() createAppointmentDto: CreateAppointmentDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<AppointmentIdResponseDto> {
    return this.appointmentsService.createAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      createAppointmentDto,
      metadata,
    );
  }

  @Post('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.APPOINTMENT_CREATE)
  @ApiOperation({ summary: 'Create multiple appointments in bulk' })
  @ApiBody({
    description: 'Bulk appointment creation data',
    type: BulkCreateAppointmentDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Appointments created successfully',
    type: BulkAppointmentIdsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or appointments already exist',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Appointment numbers already exist',
  })
  bulkCreate(
    @Request() req,
    @Body() bulkCreateAppointmentDto: BulkCreateAppointmentDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkAppointmentIdsResponseDto> {
    return this.appointmentsService.bulkCreateAndReturnIds(
      req.user.id,
      req.user.activeBusinessId,
      bulkCreateAppointmentDto.appointments,
      metadata,
    );
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.APPOINTMENT_READ)
  @ApiOperation({
    summary: 'Get all appointments for the active business',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'from',
    description: 'Start date filter (ISO string)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'to',
    description: 'End date filter (ISO string)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'appointmentNumber',
    description: 'Filter by appointment number',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'status',
    description: 'Filter by appointment status',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'customerId',
    description: 'Filter by customer ID',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'locationId',
    description: 'Filter by location ID',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'bookingSource',
    description: 'Filter by booking source',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'filters',
    description: 'Additional filters as JSON string',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'joinOperator',
    description: 'Join operator for filters (and/or)',
    required: false,
    enum: ['and', 'or'],
  })
  @ApiQuery({
    name: 'sort',
    description: 'Sort criteria',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Appointments retrieved successfully',
    type: PaginatedAppointmentsResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Request() req,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('appointmentNumber') appointmentNumber?: string,
    @Query('status') status?: string,
    @Query('customerId') customerId?: string,
    @Query('locationId') locationId?: string,
    @Query('bookingSource') bookingSource?: string,
    @Query('filters') filters?: string,
    @Query('joinOperator') joinOperator?: 'and' | 'or',
    @Query('sort') sort?: string,
  ): Promise<PaginatedAppointmentsResponseDto> {
    return this.appointmentsService.findAllOptimized(
      req.user.id,
      req.user.activeBusinessId,
      page ? parseInt(page.toString()) : undefined,
      limit ? parseInt(limit.toString()) : undefined,
      from,
      to,
      appointmentNumber,
      status,
      customerId,
      locationId,
      bookingSource,
      filters,
      joinOperator,
      sort,
    );
  }

  @Get('check-appointment-number-availability')
  @ApiBearerAuth()
  @RequirePermissions(Permission.APPOINTMENT_READ)
  @ApiOperation({ summary: 'Check if an appointment number is available' })
  @ApiQuery({
    name: 'appointmentNumber',
    required: true,
    description: 'The appointment number to check for availability',
    example: 'APT-001',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns appointment number availability',
    type: AppointmentNameAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkAppointmentNumberAvailability(
    @Request() req,
    @Query('appointmentNumber') appointmentNumber: string,
  ): Promise<{ available: boolean }> {
    return this.appointmentsService.checkAppointmentNumberAvailability(
      req.user.id,
      req.user.activeBusinessId,
      appointmentNumber,
    );
  }

  @Get('slim')
  @ApiBearerAuth()
  @RequirePermissions(Permission.APPOINTMENT_READ)
  @ApiOperation({ summary: 'Get all appointments in slim format' })
  @ApiResponse({
    status: 200,
    description: 'All appointments returned successfully',
    type: [AppointmentSlimDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllSlim(@Request() req): Promise<AppointmentSlimDto[]> {
    return this.appointmentsService.findAllSlim(
      req.user.id,
      req.user.activeBusinessId,
    );
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.APPOINTMENT_READ)
  @ApiOperation({ summary: 'Get an appointment by ID' })
  @ApiParam({
    name: 'id',
    description: 'Appointment ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the appointment with related data',
    type: AppointmentDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Appointment not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to this appointment',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findOne(@Request() req, @Param('id') id: string): Promise<AppointmentDto> {
    return this.appointmentsService.findOne(req.user.id, id);
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.APPOINTMENT_UPDATE)
  @ApiOperation({ summary: 'Update an appointment' })
  @ApiParam({
    name: 'id',
    description: 'Appointment ID',
  })
  @ApiBody({
    description: 'Appointment update data',
    type: UpdateAppointmentDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Appointment updated successfully',
    type: AppointmentIdResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Appointment not found',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to update this appointment',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Appointment number already exists',
  })
  update(
    @Request() req,
    @Param('id') id: string,
    @Body() updateAppointmentDto: UpdateAppointmentDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<AppointmentIdResponseDto> {
    return this.appointmentsService.updateAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateAppointmentDto,
      metadata,
    );
  }

  @Delete('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.APPOINTMENT_DELETE)
  @ApiOperation({ summary: 'Bulk delete appointments' })
  @ApiBody({
    description: 'Array of appointment IDs to delete',
    type: BulkDeleteAppointmentDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Appointments deleted successfully',
    type: BulkDeleteAppointmentResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or appointments not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  bulkDelete(
    @Request() req,
    @Body() bulkDeleteAppointmentDto: BulkDeleteAppointmentDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkDeleteAppointmentResponseDto> {
    return this.appointmentsService.bulkDelete(
      req.user.id,
      req.user.activeBusinessId,
      bulkDeleteAppointmentDto.appointmentIds,
      metadata,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.APPOINTMENT_DELETE)
  @ApiOperation({ summary: 'Delete an appointment' })
  @ApiParam({
    name: 'id',
    description: 'Appointment ID',
  })
  @ApiResponse({
    status: 200,
    description: 'The appointment has been successfully deleted',
    type: DeleteAppointmentResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Appointment not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to delete this appointment',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  remove(
    @Request() req,
    @Param('id') id: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<DeleteAppointmentResponseDto> {
    return this.appointmentsService.remove(
      req.user.id,
      req.user.activeBusinessId,
      id,
      metadata,
    );
  }

  @Patch('batch/status')
  @ApiBearerAuth()
  @RequirePermissions(Permission.APPOINTMENT_UPDATE)
  @ApiOperation({ summary: 'Batch update appointment status' })
  @ApiBody({
    description: 'Array of appointment status updates',
    type: BulkUpdateAppointmentStatusDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Appointment statuses have been successfully updated',
    type: BulkUpdateAppointmentStatusResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or appointments not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async updateStatus(
    @Request() req,
    @Body() bulkUpdateStatusDto: BulkUpdateAppointmentStatusDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkUpdateAppointmentStatusResponseDto> {
    return this.appointmentsService.bulkUpdateAppointmentStatus(
      req.user.id,
      req.user.activeBusinessId,
      bulkUpdateStatusDto.appointmentIds,
      bulkUpdateStatusDto.status,
      metadata,
    );
  }
}
