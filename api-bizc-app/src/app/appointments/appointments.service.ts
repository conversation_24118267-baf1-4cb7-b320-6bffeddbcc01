import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateAppointmentDto } from './dto/create-appointment.dto';
import { UpdateAppointmentDto } from './dto/update-appointment.dto';
import { AppointmentDto } from './dto/appointment.dto';
import { AppointmentSlimDto } from './dto/appointment-slim.dto';
import { AppointmentListDto } from './dto/appointment-list.dto';
import {
  appointments,
  appointmentServices,
  appointmentStaff,
  AppointmentStatus,
} from '../drizzle/schema/appointments.schema';
import { customers } from '../drizzle/schema/customers.schema';
import { locations } from '../drizzle/schema/locations.schema';
import { serviceTimeSlots } from '../drizzle/schema/service-time-slots.schema';
import { services } from '../drizzle/schema/services.schema';
import { staffMembers } from '../drizzle/schema/staff.schema';
import {
  and,
  eq,
  ilike,
  gte,
  lte,
  desc,
  asc,
  inArray,
  sql,
  or,
} from 'drizzle-orm';
import { ActivityLogService } from '../activity-log/activity-log.service';
import {
  EntityType,
  ActivityType,
  ExecutionStrategy,
  ActivitySource,
} from '../shared/types/activity.enum';
import { UsersService } from '../users/users.service';
import type { ActivityMetadata } from '../shared/types/activity-metadata.type';

@Injectable()
export class AppointmentsService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private activityLogService: ActivityLogService,
    private usersService: UsersService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createAppointmentDto: CreateAppointmentDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if an appointment with the same number already exists for this business
      const existingAppointment = await this.db
        .select()
        .from(appointments)
        .where(
          and(
            eq(
              appointments.appointmentNumber,
              createAppointmentDto.appointmentNumber,
            ),
            eq(appointments.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingAppointment) {
        throw new ConflictException(
          `Appointment with number "${createAppointmentDto.appointmentNumber}" already exists`,
        );
      }

      // Validate customer exists
      if (createAppointmentDto.customerId) {
        const customer = await this.db
          .select()
          .from(customers)
          .where(
            and(
              eq(customers.id, createAppointmentDto.customerId),
              eq(customers.businessId, businessId),
              eq(customers.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!customer) {
          throw new BadRequestException('Customer not found');
        }
      }

      // Validate location exists if provided
      if (createAppointmentDto.locationId) {
        const location = await this.db
          .select()
          .from(locations)
          .where(
            and(
              eq(locations.id, createAppointmentDto.locationId),
              eq(locations.businessId, businessId),
              eq(locations.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!location) {
          throw new BadRequestException('Location not found');
        }
      }

      // Validate time slot exists if provided
      if (createAppointmentDto.timeSlotId) {
        const timeSlot = await this.db
          .select()
          .from(serviceTimeSlots)
          .innerJoin(locations, eq(serviceTimeSlots.locationId, locations.id))
          .where(
            and(
              eq(serviceTimeSlots.id, createAppointmentDto.timeSlotId),
              eq(locations.businessId, businessId),
              eq(serviceTimeSlots.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!timeSlot) {
          throw new BadRequestException('Time slot not found');
        }
      }

      // Use a transaction to ensure appointment creation and junction table operations are atomic
      const newAppointment = await this.db.transaction(async (tx) => {
        // Insert new appointment
        const [appointment] = await tx
          .insert(appointments)
          .values({
            appointmentNumber: createAppointmentDto.appointmentNumber,
            customerId: createAppointmentDto.customerId,
            locationId: createAppointmentDto.locationId,
            timeSlotId: createAppointmentDto.timeSlotId,
            status: createAppointmentDto.status ?? AppointmentStatus.SCHEDULED,
            appointmentDate: createAppointmentDto.appointmentDate,
            notes: createAppointmentDto.notes,
            cancellationReason: createAppointmentDto.cancellationReason,
            confirmedAt: createAppointmentDto.confirmedAt,
            reminderSentAt: createAppointmentDto.reminderSentAt,
            bookingSource: createAppointmentDto.bookingSource,
            totalEstimatedCost: createAppointmentDto.totalEstimatedCost,
            depositAmount: createAppointmentDto.depositAmount,
            depositPaid: createAppointmentDto.depositPaid ?? false,
            createdBy: userId,
          })
          .returning();

        // Handle appointment services junction table
        if (
          createAppointmentDto.serviceIds &&
          createAppointmentDto.serviceIds.length > 0
        ) {
          // Validate all services exist and belong to the business
          const serviceResults = await tx
            .select()
            .from(services)
            .where(
              and(
                inArray(services.id, createAppointmentDto.serviceIds),
                eq(services.businessId, businessId),
                eq(services.isDeleted, false),
              ),
            );

          if (
            serviceResults.length !== createAppointmentDto.serviceIds.length
          ) {
            throw new BadRequestException('One or more services not found');
          }

          // Insert appointment services
          const appointmentServiceValues = createAppointmentDto.serviceIds.map(
            (serviceId) => ({
              appointmentId: appointment.id,
              serviceId,
              createdBy: userId,
            }),
          );

          await tx.insert(appointmentServices).values(appointmentServiceValues);
        }

        // Handle appointment staff junction table
        if (
          createAppointmentDto.staffMemberIds &&
          createAppointmentDto.staffMemberIds.length > 0
        ) {
          // Validate all staff members exist and belong to the business
          const staffResults = await tx
            .select()
            .from(staffMembers)
            .where(
              and(
                inArray(staffMembers.id, createAppointmentDto.staffMemberIds),
                eq(staffMembers.businessId, businessId),
                eq(staffMembers.isDeleted, false),
              ),
            );

          if (
            staffResults.length !== createAppointmentDto.staffMemberIds.length
          ) {
            throw new BadRequestException(
              'One or more staff members not found',
            );
          }

          // Insert appointment staff
          const appointmentStaffValues =
            createAppointmentDto.staffMemberIds.map((staffMemberId) => ({
              appointmentId: appointment.id,
              staffMemberId,
              createdBy: userId,
            }));

          await tx.insert(appointmentStaff).values(appointmentStaffValues);
        }

        return appointment;
      });

      // Log the appointment creation activity
      await this.activityLogService.logCreate(
        newAppointment.id,
        EntityType.APPOINTMENT,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id: newAppointment.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to create appointment');
    }
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createAppointmentDto: CreateAppointmentDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    const appointment = await this.create(
      userId,
      businessId,
      createAppointmentDto,
      metadata,
    );
    return { id: appointment.id };
  }

  async bulkCreate(
    userId: string,
    businessId: string | null,
    createAppointmentDtos: CreateAppointmentDto[],
    metadata?: ActivityMetadata,
  ): Promise<string[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const createdIds: string[] = [];
    const errors: string[] = [];

    for (const createAppointmentDto of createAppointmentDtos) {
      try {
        const result = await this.create(
          userId,
          businessId,
          createAppointmentDto,
        );
        createdIds.push(result.id);
      } catch (error) {
        if (error instanceof ConflictException) {
          errors.push(
            `Appointment "${createAppointmentDto.appointmentNumber}": ${error.message}`,
          );
        } else {
          errors.push(
            `Appointment "${createAppointmentDto.appointmentNumber}": Failed to create`,
          );
        }
      }
    }

    if (errors.length > 0 && createdIds.length === 0) {
      throw new BadRequestException(
        `Failed to create appointments: ${errors.join(', ')}`,
      );
    }

    // Log the bulk creation activity
    if (createdIds.length > 0) {
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_CREATE,
        EntityType.APPOINTMENT,
        createdIds,
        { created: true },
        userId,
        businessId,
        {
          filterCriteria: {
            appointmentCount: createAppointmentDtos.length,
            appointmentNumbers: createAppointmentDtos.map(
              (dto) => dto.appointmentNumber,
            ),
          },
          failures:
            errors.length > 0
              ? errors.map((error, index) => ({
                  id:
                    createAppointmentDtos[createdIds.length + index]
                      ?.appointmentNumber || `index-${index}`,
                  error,
                }))
              : undefined,
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );
    }

    return createdIds;
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createAppointmentDtos: CreateAppointmentDto[],
    metadata?: ActivityMetadata,
  ): Promise<{ ids: string[] }> {
    const ids = await this.bulkCreate(
      userId,
      businessId,
      createAppointmentDtos,
      metadata,
    );
    return { ids };
  }

  async findOne(
    userId: string,
    appointmentId: string,
  ): Promise<AppointmentDto> {
    const appointment = await this.db
      .select()
      .from(appointments)
      .where(
        and(
          eq(appointments.id, appointmentId),
          eq(appointments.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!appointment) {
      throw new NotFoundException('Appointment not found');
    }

    return await this.mapToAppointmentDto(appointment);
  }

  async findAllOptimized(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    appointmentNumber?: string,
    status?: string,
    customerId?: string,
    locationId?: string,
    bookingSource?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: AppointmentListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [eq(appointments.isDeleted, false)];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(appointments.appointmentDate, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        whereConditions.push(lte(appointments.appointmentDate, toDate));
      }
    }

    // Add specific field filters
    if (appointmentNumber) {
      whereConditions.push(
        ilike(appointments.appointmentNumber, `%${appointmentNumber}%`),
      );
    }

    if (status) {
      whereConditions.push(
        eq(appointments.status, status as AppointmentStatus),
      );
    }

    if (customerId) {
      whereConditions.push(eq(appointments.customerId, customerId));
    }

    if (locationId) {
      whereConditions.push(eq(appointments.locationId, locationId));
    }

    if (bookingSource) {
      whereConditions.push(
        eq(appointments.bookingSource, bookingSource as any),
      );
    }

    // Parse additional filters
    let additionalFilters: any[] = [];
    if (filters) {
      try {
        const parsedFilters = JSON.parse(filters);
        additionalFilters = this.buildFiltersFromJson(parsedFilters);
      } catch {
        // Ignore invalid JSON filters
      }
    }

    // Combine conditions based on join operator
    let finalWhereCondition;
    const allConditions = [...whereConditions, ...additionalFilters];

    if (joinOperator === 'or' && allConditions.length > 1) {
      finalWhereCondition = or(...allConditions);
    } else {
      finalWhereCondition = and(...allConditions);
    }

    // Build sort conditions
    let orderBy = [desc(appointments.createdAt)]; // Default sort
    if (sort) {
      orderBy = this.buildSortConditions(sort);
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(appointments)
      .where(finalWhereCondition);

    const total = Number(totalResult[0]?.count || 0);
    const totalPages = Math.ceil(total / limit);

    // Get paginated results
    const appointmentResults = await this.db
      .select()
      .from(appointments)
      .where(finalWhereCondition)
      .orderBy(...orderBy)
      .limit(limit)
      .offset(offset);

    // Log the activity
    await this.activityLogService.createTypedLog(ActivityType.VIEW, {
      entityType: EntityType.APPOINTMENT,
      userId,
      businessId: businessId,
      changes: {},
      metadata: {
        action: 'Viewed appointment list',
        filters: {
          from,
          to,
          appointmentNumber,
          status,
          customerId,
          locationId,
          bookingSource,
        },
      },
    });

    const data = await Promise.all(
      appointmentResults.map((appointment) =>
        this.mapToAppointmentListDto(appointment),
      ),
    );

    return {
      data,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async checkAppointmentNumberAvailability(
    _userId: string,
    businessId: string | null,
    appointmentNumber: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if an appointment with the same number already exists
    const existingAppointment = await this.db
      .select()
      .from(appointments)
      .where(
        and(
          eq(appointments.appointmentNumber, appointmentNumber),
          eq(appointments.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    return { available: !existingAppointment };
  }

  async findAllSlim(
    userId: string,
    businessId: string | null,
  ): Promise<AppointmentSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Find all appointments with only essential fields
    const appointmentResults = await this.db
      .select({
        id: appointments.id,
        appointmentNumber: appointments.appointmentNumber,
        status: appointments.status,
        appointmentDate: appointments.appointmentDate,
      })
      .from(appointments)
      .where(and(eq(appointments.isDeleted, false)))
      .orderBy(
        desc(appointments.appointmentDate),
        desc(appointments.createdAt),
      );

    // Log the activity
    await this.activityLogService.createTypedLog(ActivityType.VIEW, {
      entityType: EntityType.APPOINTMENT,
      userId,
      businessId: businessId,
      changes: {},
      metadata: {
        action: 'Viewed appointment list (slim)',
      },
    });

    return appointmentResults.map((appointment) => ({
      id: appointment.id.toString(),
      appointmentNumber: appointment.appointmentNumber,
      status: appointment.status,
      appointmentDate: appointment.appointmentDate,
    }));
  }

  async update(
    userId: string,
    businessId: string | null,
    appointmentId: string,
    updateAppointmentDto: UpdateAppointmentDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if appointment exists
      const existingAppointment = await this.db
        .select()
        .from(appointments)
        .where(
          and(
            eq(appointments.id, appointmentId),
            eq(appointments.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingAppointment) {
        throw new NotFoundException('Appointment not found');
      }

      // Check if appointment number is being changed and if it conflicts
      if (
        updateAppointmentDto.appointmentNumber &&
        updateAppointmentDto.appointmentNumber !==
          existingAppointment.appointmentNumber
      ) {
        const conflictingAppointment = await this.db
          .select()
          .from(appointments)
          .where(
            and(
              eq(
                appointments.appointmentNumber,
                updateAppointmentDto.appointmentNumber,
              ),
              sql`${appointments.id} != ${appointmentId}`,
              eq(appointments.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (conflictingAppointment) {
          throw new ConflictException(
            `Appointment with number "${updateAppointmentDto.appointmentNumber}" already exists`,
          );
        }
      }

      // Validate customer exists if being updated
      if (updateAppointmentDto.customerId) {
        const customer = await this.db
          .select()
          .from(customers)
          .where(
            and(
              eq(customers.id, updateAppointmentDto.customerId),
              eq(customers.businessId, businessId),
              eq(customers.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!customer) {
          throw new BadRequestException('Customer not found');
        }
      }

      // Validate location exists if being updated
      if (updateAppointmentDto.locationId) {
        const location = await this.db
          .select()
          .from(locations)
          .where(
            and(
              eq(locations.id, updateAppointmentDto.locationId),
              eq(locations.businessId, businessId),
              eq(locations.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!location) {
          throw new BadRequestException('Location not found');
        }
      }

      // Validate time slot exists if being updated
      if (updateAppointmentDto.timeSlotId) {
        const timeSlot = await this.db
          .select()
          .from(serviceTimeSlots)
          .innerJoin(locations, eq(serviceTimeSlots.locationId, locations.id))
          .where(
            and(
              eq(serviceTimeSlots.id, updateAppointmentDto.timeSlotId),
              eq(locations.businessId, businessId),
              eq(serviceTimeSlots.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!timeSlot) {
          throw new BadRequestException('Time slot not found');
        }
      }

      // Use a transaction to ensure appointment update and junction table operations are atomic
      const updatedAppointment = await this.db.transaction(async (tx) => {
        // Update appointment
        const updateData: any = {
          updatedBy: userId,
          updatedAt: new Date(),
        };

        // Only update fields that are provided
        if (updateAppointmentDto.appointmentNumber !== undefined) {
          updateData.appointmentNumber = updateAppointmentDto.appointmentNumber;
        }
        if (updateAppointmentDto.customerId !== undefined) {
          updateData.customerId = updateAppointmentDto.customerId;
        }
        if (updateAppointmentDto.locationId !== undefined) {
          updateData.locationId = updateAppointmentDto.locationId;
        }
        if (updateAppointmentDto.timeSlotId !== undefined) {
          updateData.timeSlotId = updateAppointmentDto.timeSlotId;
        }
        if (updateAppointmentDto.status !== undefined) {
          updateData.status = updateAppointmentDto.status;
        }
        if (updateAppointmentDto.appointmentDate !== undefined) {
          updateData.appointmentDate = updateAppointmentDto.appointmentDate;
        }
        if (updateAppointmentDto.notes !== undefined) {
          updateData.notes = updateAppointmentDto.notes;
        }
        if (updateAppointmentDto.cancellationReason !== undefined) {
          updateData.cancellationReason =
            updateAppointmentDto.cancellationReason;
        }
        if (updateAppointmentDto.confirmedAt !== undefined) {
          updateData.confirmedAt = updateAppointmentDto.confirmedAt;
        }
        if (updateAppointmentDto.reminderSentAt !== undefined) {
          updateData.reminderSentAt = updateAppointmentDto.reminderSentAt;
        }
        if (updateAppointmentDto.bookingSource !== undefined) {
          updateData.bookingSource = updateAppointmentDto.bookingSource;
        }
        if (updateAppointmentDto.totalEstimatedCost !== undefined) {
          updateData.totalEstimatedCost =
            updateAppointmentDto.totalEstimatedCost;
        }
        if (updateAppointmentDto.depositAmount !== undefined) {
          updateData.depositAmount = updateAppointmentDto.depositAmount;
        }
        if (updateAppointmentDto.depositPaid !== undefined) {
          updateData.depositPaid = updateAppointmentDto.depositPaid;
        }

        const [appointment] = await tx
          .update(appointments)
          .set(updateData)
          .where(eq(appointments.id, appointmentId))
          .returning();

        // Handle appointment services junction table updates
        if (updateAppointmentDto.serviceIds !== undefined) {
          // Remove existing appointment services
          await tx
            .update(appointmentServices)
            .set({
              isDeleted: true,
              updatedBy: userId,
              updatedAt: new Date(),
            })
            .where(
              and(
                eq(appointmentServices.appointmentId, appointmentId),
                eq(appointmentServices.isDeleted, false),
              ),
            );

          // Add new appointment services if provided
          if (updateAppointmentDto.serviceIds.length > 0) {
            // Validate all services exist and belong to the business
            const serviceResults = await tx
              .select()
              .from(services)
              .where(
                and(
                  inArray(services.id, updateAppointmentDto.serviceIds),
                  eq(services.businessId, businessId),
                  eq(services.isDeleted, false),
                ),
              );

            if (
              serviceResults.length !== updateAppointmentDto.serviceIds.length
            ) {
              throw new BadRequestException('One or more services not found');
            }

            // Insert new appointment services
            const appointmentServiceValues =
              updateAppointmentDto.serviceIds.map((serviceId) => ({
                appointmentId,
                serviceId,
                createdBy: userId,
              }));

            await tx
              .insert(appointmentServices)
              .values(appointmentServiceValues);
          }
        }

        // Handle appointment staff junction table updates
        if (updateAppointmentDto.staffMemberIds !== undefined) {
          // Remove existing appointment staff
          await tx
            .update(appointmentStaff)
            .set({
              isDeleted: true,
              updatedBy: userId,
              updatedAt: new Date(),
            })
            .where(
              and(
                eq(appointmentStaff.appointmentId, appointmentId),
                eq(appointmentStaff.isDeleted, false),
              ),
            );

          // Add new appointment staff if provided
          if (updateAppointmentDto.staffMemberIds.length > 0) {
            // Validate all staff members exist and belong to the business
            const staffResults = await tx
              .select()
              .from(staffMembers)
              .where(
                and(
                  inArray(staffMembers.id, updateAppointmentDto.staffMemberIds),
                  eq(staffMembers.businessId, businessId),
                  eq(staffMembers.isDeleted, false),
                ),
              );

            if (
              staffResults.length !== updateAppointmentDto.staffMemberIds.length
            ) {
              throw new BadRequestException(
                'One or more staff members not found',
              );
            }

            // Insert new appointment staff
            const appointmentStaffValues =
              updateAppointmentDto.staffMemberIds.map((staffMemberId) => ({
                appointmentId,
                staffMemberId,
                createdBy: userId,
              }));

            await tx.insert(appointmentStaff).values(appointmentStaffValues);
          }
        }

        return appointment;
      });

      // Log the appointment update activity
      await this.activityLogService.logUpdate(
        appointmentId,
        EntityType.APPOINTMENT,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id: updatedAppointment.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to update appointment');
    }
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    appointmentId: string,
    updateAppointmentDto: UpdateAppointmentDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    const appointment = await this.update(
      userId,
      businessId,
      appointmentId,
      updateAppointmentDto,
      metadata,
    );
    return { id: appointment.id };
  }

  async remove(
    userId: string,
    businessId: string | null,
    appointmentId: string,
    metadata?: ActivityMetadata,
  ): Promise<{ deleted: boolean; message: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const existingAppointment = await this.db
      .select()
      .from(appointments)
      .where(
        and(
          eq(appointments.id, appointmentId),
          eq(appointments.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingAppointment) {
      throw new NotFoundException('Appointment not found');
    }

    // Soft delete the appointment
    await this.db
      .update(appointments)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(appointments.id, appointmentId));

    // Log the appointment deletion activity
    await this.activityLogService.logDelete(
      appointmentId,
      EntityType.APPOINTMENT,
      userId,
      businessId,
      {
        reason: `Deleted appointment: ${existingAppointment.appointmentNumber}`,
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return {
      deleted: true,
      message: 'Appointment deleted successfully',
    };
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    appointmentIds: string[],
    metadata?: ActivityMetadata,
  ): Promise<{ deleted: number; message: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (!appointmentIds || appointmentIds.length === 0) {
      throw new BadRequestException('No appointment IDs provided');
    }

    // Check which appointments exist
    const existingAppointments = await this.db
      .select()
      .from(appointments)
      .where(
        and(
          inArray(appointments.id, appointmentIds),
          eq(appointments.isDeleted, false),
        ),
      );

    if (existingAppointments.length === 0) {
      throw new NotFoundException('No appointments found to delete');
    }

    // Soft delete the appointments
    await this.db
      .update(appointments)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(
        and(
          inArray(appointments.id, appointmentIds),
          eq(appointments.isDeleted, false),
        ),
      );

    // Log the bulk deletion activity
    await this.activityLogService.logBulkOperation(
      ActivityType.BULK_DELETE,
      EntityType.APPOINTMENT,
      appointmentIds,
      { isDeleted: true },
      userId,
      businessId,
      {
        filterCriteria: { appointmentIds },
        executionStrategy: ExecutionStrategy.PARALLEL,
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return {
      deleted: existingAppointments.length,
      message: `Successfully deleted ${existingAppointments.length} appointments`,
    };
  }

  async bulkUpdateAppointmentStatus(
    userId: string,
    businessId: string | null,
    appointmentIds: string[],
    status: AppointmentStatus,
    metadata?: ActivityMetadata,
  ): Promise<{ updated: number; message: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (!appointmentIds || appointmentIds.length === 0) {
      throw new BadRequestException('No appointment IDs provided');
    }

    // Check which appointments exist
    const existingAppointments = await this.db
      .select()
      .from(appointments)
      .where(
        and(
          inArray(appointments.id, appointmentIds),
          eq(appointments.isDeleted, false),
        ),
      );

    if (existingAppointments.length === 0) {
      throw new NotFoundException('No appointments found to update');
    }

    // Update appointment statuses
    await this.db
      .update(appointments)
      .set({
        status,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(
        and(
          inArray(appointments.id, appointmentIds),
          eq(appointments.isDeleted, false),
        ),
      );

    // Log the bulk status update activity
    await this.activityLogService.logBulkOperation(
      ActivityType.BULK_STATUS_CHANGE,
      EntityType.APPOINTMENT,
      appointmentIds,
      { status },
      userId,
      businessId,
      {
        filterCriteria: { appointmentIds, status },
        executionStrategy: ExecutionStrategy.PARALLEL,
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return {
      updated: existingAppointments.length,
      message: `Successfully updated ${existingAppointments.length} appointments to ${status}`,
    };
  }

  // Helper methods
  private buildFiltersFromJson(filters: any[]): any[] {
    const conditions: any[] = [];

    for (const filter of filters) {
      if (filter.field && filter.operator && filter.value !== undefined) {
        switch (filter.field) {
          case 'appointmentNumber':
            if (filter.operator === 'contains') {
              conditions.push(
                ilike(appointments.appointmentNumber, `%${filter.value}%`),
              );
            } else if (filter.operator === 'equals') {
              conditions.push(eq(appointments.appointmentNumber, filter.value));
            }
            break;
          case 'status':
            conditions.push(eq(appointments.status, filter.value));
            break;
          case 'customerId':
            conditions.push(eq(appointments.customerId, filter.value));
            break;
          case 'locationId':
            conditions.push(eq(appointments.locationId, filter.value));
            break;
          case 'bookingSource':
            conditions.push(eq(appointments.bookingSource, filter.value));
            break;
          case 'appointmentDate':
            if (filter.operator === 'gte') {
              conditions.push(
                gte(appointments.appointmentDate, new Date(filter.value)),
              );
            } else if (filter.operator === 'lte') {
              conditions.push(
                lte(appointments.appointmentDate, new Date(filter.value)),
              );
            } else if (filter.operator === 'equals') {
              conditions.push(
                eq(appointments.appointmentDate, new Date(filter.value)),
              );
            }
            break;
        }
      }
    }

    return conditions;
  }

  private buildSortConditions(sort: string): any[] {
    const sortConditions: any[] = [];

    try {
      const sortFields = sort.split(',');

      for (const field of sortFields) {
        const [fieldName, direction] = field.trim().split(':');
        const isDesc = direction?.toLowerCase() === 'desc';

        switch (fieldName) {
          case 'appointmentNumber':
            sortConditions.push(
              isDesc
                ? desc(appointments.appointmentNumber)
                : asc(appointments.appointmentNumber),
            );
            break;
          case 'status':
            sortConditions.push(
              isDesc ? desc(appointments.status) : asc(appointments.status),
            );
            break;
          case 'appointmentDate':
            sortConditions.push(
              isDesc
                ? desc(appointments.appointmentDate)
                : asc(appointments.appointmentDate),
            );
            break;
          case 'createdAt':
            sortConditions.push(
              isDesc
                ? desc(appointments.createdAt)
                : asc(appointments.createdAt),
            );
            break;
          case 'updatedAt':
            sortConditions.push(
              isDesc
                ? desc(appointments.updatedAt)
                : asc(appointments.updatedAt),
            );
            break;
          default:
            // Default to createdAt desc if unknown field
            sortConditions.push(desc(appointments.createdAt));
        }
      }
    } catch {
      // If parsing fails, use default sort
      sortConditions.push(desc(appointments.createdAt));
    }

    return sortConditions.length > 0
      ? sortConditions
      : [desc(appointments.createdAt)];
  }

  private async mapToAppointmentDto(
    appointment: typeof appointments.$inferSelect,
  ): Promise<AppointmentDto> {
    // Get user names for createdBy and updatedBy
    const createdByName = await this.usersService.getUserName(
      appointment.createdBy.toString(),
    );
    let updatedByName: string | undefined;
    if (appointment.updatedBy) {
      updatedByName = await this.usersService.getUserName(
        appointment.updatedBy.toString(),
      );
    }

    // Get customer information
    let customer: any = null;
    if (appointment.customerId) {
      const customerResult = await this.db
        .select({
          id: customers.id,
          customerDisplayName: customers.customerDisplayName,
          email: customers.email,
          phone: customers.phoneNumber,
        })
        .from(customers)
        .where(
          and(
            eq(customers.id, appointment.customerId),
            eq(customers.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (customerResult) {
        customer = {
          id: customerResult.id.toString(),
          name: customerResult.customerDisplayName,
          email: customerResult.email,
          phone: customerResult.phone,
        };
      }
    }

    // Get location information
    let location: any = null;
    if (appointment.locationId) {
      const locationResult = await this.db
        .select({
          id: locations.id,
          name: locations.name,
          code: locations.code,
        })
        .from(locations)
        .where(
          and(
            eq(locations.id, appointment.locationId),
            eq(locations.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (locationResult) {
        location = {
          id: locationResult.id.toString(),
          name: locationResult.name,
          code: locationResult.code,
        };
      }
    }

    // Get time slot information
    let timeSlot: any = null;
    if (appointment.timeSlotId) {
      const timeSlotResult = await this.db
        .select({
          id: serviceTimeSlots.id,
          name: serviceTimeSlots.slotName,
          startTime: serviceTimeSlots.startTime,
          endTime: serviceTimeSlots.endTime,
        })
        .from(serviceTimeSlots)
        .where(
          and(
            eq(serviceTimeSlots.id, appointment.timeSlotId),
            eq(serviceTimeSlots.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (timeSlotResult) {
        timeSlot = {
          id: timeSlotResult.id.toString(),
          name: timeSlotResult.name,
          startTime: timeSlotResult.startTime,
          endTime: timeSlotResult.endTime,
        };
      }
    }

    // Get appointment services
    const appointmentServiceResults = await this.db
      .select({
        serviceId: appointmentServices.serviceId,
        serviceName: services.name,
        servicePrice: services.priceRate,
      })
      .from(appointmentServices)
      .innerJoin(services, eq(appointmentServices.serviceId, services.id))
      .where(
        and(
          eq(appointmentServices.appointmentId, appointment.id),
          eq(appointmentServices.isDeleted, false),
          eq(services.isDeleted, false),
        ),
      );

    const appointmentServicesData = appointmentServiceResults.map((result) => ({
      id: result.serviceId.toString(),
      name: result.serviceName,
      price: result.servicePrice,
    }));

    // Get appointment staff
    const appointmentStaffResults = await this.db
      .select({
        staffMemberId: appointmentStaff.staffMemberId,
        staffDisplayName: staffMembers.displayName,
        staffEmail: staffMembers.email,
      })
      .from(appointmentStaff)
      .innerJoin(
        staffMembers,
        eq(appointmentStaff.staffMemberId, staffMembers.id),
      )
      .where(
        and(
          eq(appointmentStaff.appointmentId, appointment.id),
          eq(appointmentStaff.isDeleted, false),
          eq(staffMembers.isDeleted, false),
        ),
      );

    const appointmentStaffData = appointmentStaffResults.map((result) => ({
      id: result.staffMemberId.toString(),
      name: result.staffDisplayName,
      email: result.staffEmail,
    }));

    return {
      id: appointment.id.toString(),
      appointmentNumber: appointment.appointmentNumber,
      customer,
      location,
      timeSlot,
      status: appointment.status,
      appointmentDate: appointment.appointmentDate,
      notes: appointment.notes,
      cancellationReason: appointment.cancellationReason,
      confirmedAt: appointment.confirmedAt,
      reminderSentAt: appointment.reminderSentAt,
      bookingSource: appointment.bookingSource,
      totalEstimatedCost: appointment.totalEstimatedCost,
      depositAmount: appointment.depositAmount,
      depositPaid: appointment.depositPaid,
      services: appointmentServicesData,
      staff: appointmentStaffData,
      createdBy: createdByName,
      updatedBy: updatedByName,
      createdAt: appointment.createdAt,
      updatedAt: appointment.updatedAt,
    };
  }

  private async mapToAppointmentListDto(
    appointment: typeof appointments.$inferSelect,
  ): Promise<AppointmentListDto> {
    // Get customer name
    let customerName: string | null = null;
    if (appointment.customerId) {
      const customerResult = await this.db
        .select({
          customerDisplayName: customers.customerDisplayName,
        })
        .from(customers)
        .where(
          and(
            eq(customers.id, appointment.customerId),
            eq(customers.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      customerName = customerResult?.customerDisplayName || null;
    }

    // Get location name
    let locationName: string | null = null;
    if (appointment.locationId) {
      const locationResult = await this.db
        .select({
          name: locations.name,
        })
        .from(locations)
        .where(
          and(
            eq(locations.id, appointment.locationId),
            eq(locations.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      locationName = locationResult?.name || null;
    }

    // Get services count
    const servicesCount = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(appointmentServices)
      .where(
        and(
          eq(appointmentServices.appointmentId, appointment.id),
          eq(appointmentServices.isDeleted, false),
        ),
      )
      .then((results) => Number(results[0]?.count || 0));

    // Get staff count
    const staffCount = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(appointmentStaff)
      .where(
        and(
          eq(appointmentStaff.appointmentId, appointment.id),
          eq(appointmentStaff.isDeleted, false),
        ),
      )
      .then((results) => Number(results[0]?.count || 0));

    return {
      id: appointment.id.toString(),
      appointmentNumber: appointment.appointmentNumber,
      customerName,
      locationName,
      status: appointment.status,
      appointmentDate: appointment.appointmentDate,
      bookingSource: appointment.bookingSource,
      totalEstimatedCost: appointment.totalEstimatedCost,
      depositPaid: appointment.depositPaid,
      servicesCount,
      staffCount,
      createdAt: appointment.createdAt,
      updatedAt: appointment.updatedAt,
    };
  }
}
