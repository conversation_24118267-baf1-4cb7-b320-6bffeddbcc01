import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateAssetCategoryDto } from './dto/create-asset-category.dto';
import { UpdateAssetCategoryDto } from './dto/update-asset-category.dto';
import { AssetCategoryDto } from './dto/asset-category.dto';
import { AssetCategorySlimDto } from './dto/asset-category-slim.dto';
import { AssetCategoryListDto } from './dto/asset-category-list.dto';
import { assetCategories } from '../drizzle/schema/asset-categories.schema';
import { media } from '../drizzle/schema/media.schema';
import { assets } from '../drizzle/schema/assets.schema';
import {
  eq,
  and,
  ilike,
  sql,
  gte,
  lte,
  desc,
  asc,
  or,
  inArray,
} from 'drizzle-orm';
import { users } from '../drizzle/schema/users.schema';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { CategoryStatus } from '../shared/types';
import {
  EntityType,
  ActivityType,
  ExecutionStrategy,
  ActivitySource,
} from '../shared/types/activity.enum';
import { MediaService } from '../media/media.service';
import { GcsUploadService } from '../gcs-upload/gcs-upload.service';
import { UsersService } from '../users/users.service';
import { ActivityMetadata } from '../shared/types/activity-metadata.type';

@Injectable()
export class AssetCategoriesService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly mediaService: MediaService,
    private readonly gcsUploadService: GcsUploadService,
    private readonly usersService: UsersService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createAssetCategoryDto: CreateAssetCategoryDto,
    imageFile?: Express.Multer.File,
    ogImageFile?: Express.Multer.File,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if an asset category with the same name already exists for this business
      // Using ilike for case-insensitive comparison
      const existingAssetCategory = await this.db
        .select()
        .from(assetCategories)
        .where(
          and(
            eq(assetCategories.businessId, businessId),
            ilike(assetCategories.name, createAssetCategoryDto.name),
            eq(assetCategories.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingAssetCategory) {
        throw new ConflictException(
          `An asset category with the name '${createAssetCategoryDto.name}' already exists for this business`,
        );
      }

      // Handle parent category if provided
      if (createAssetCategoryDto.parentId) {
        const parentCategory = await this.db
          .select()
          .from(assetCategories)
          .where(
            and(
              eq(assetCategories.id, createAssetCategoryDto.parentId),
              eq(assetCategories.businessId, businessId),
              eq(assetCategories.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!parentCategory) {
          throw new BadRequestException('Parent asset category not found');
        }
      }

      let mediaId: string | undefined;
      let ogImageId: string | undefined;

      // Upload image if provided
      if (imageFile) {
        const uploadedMedia = await this.mediaService.uploadMedia(
          imageFile,
          'asset-categories',
          businessId,
          userId,
        );
        mediaId = uploadedMedia.id;
      }

      // Upload OG image if provided
      if (ogImageFile) {
        const uploadedOgMedia = await this.mediaService.uploadMedia(
          ogImageFile,
          'asset-categories/og-images',
          businessId,
          userId,
        );
        ogImageId = uploadedOgMedia.id;
      }

      // Create the asset category
      const [newAssetCategory] = await this.db
        .insert(assetCategories)
        .values({
          businessId,
          name: createAssetCategoryDto.name,
          description: createAssetCategoryDto.description,
          parentId: createAssetCategoryDto.parentId,
          image: mediaId,
          ogImage: ogImageId,
          createdBy: userId,
          status: createAssetCategoryDto.status ?? CategoryStatus.ACTIVE,
        })
        .returning();

      // Log the asset category creation activity
      await this.activityLogService.logCreate(
        newAssetCategory.id,
        EntityType.ASSET_CATEGORY,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id: newAssetCategory.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create asset category: ${error.message}`,
      );
    }
  }

  async findAll(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
  ): Promise<{
    data: AssetCategoryDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(assetCategories.isDeleted, false),
      eq(assetCategories.status, CategoryStatus.ACTIVE),
      eq(assetCategories.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(assetCategories.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        // Add 23:59:59 to include the entire day
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(assetCategories.createdAt, toDate));
      }
    }

    // Find all asset categories for the user's active business with pagination
    const result = await this.db
      .select()
      .from(assetCategories)
      .where(and(...whereConditions))
      .orderBy(desc(assetCategories.createdAt))
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(assetCategories)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    return {
      data: await Promise.all(
        result.map((assetCategory) =>
          this.mapToAssetCategoryDto(assetCategory),
        ),
      ),
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findAllOptimized(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    name?: string,
    status?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: AssetCategoryListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      eq(assetCategories.isDeleted, false),
      eq(assetCategories.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(assetCategories.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(assetCategories.createdAt, toDate));
      }
    }

    // Add name filtering if provided
    if (name) {
      whereConditions.push(ilike(assetCategories.name, `%${name}%`));
    }

    // Add status filtering if provided
    if (status) {
      // Decode URL-encoded commas and split by comma
      const decodedStatus = decodeURIComponent(status);
      const statusArray = decodedStatus
        .split(',')
        .map((s) => s.trim() as CategoryStatus);
      whereConditions.push(inArray(assetCategories.status, statusArray));
    }

    // Add advanced filters if provided
    if (filters) {
      try {
        const parsedFilters = JSON.parse(filters);
        const filterConditions = [];

        for (const filter of parsedFilters) {
          const { id: fieldId, value, operator } = filter;

          if (fieldId === 'name') {
            switch (operator) {
              case 'iLike':
                filterConditions.push(
                  ilike(assetCategories.name, `%${value}%`),
                );
                break;
              case 'notILike':
                filterConditions.push(
                  sql`NOT ${ilike(assetCategories.name, `%${value}%`)}`,
                );
                break;
              case 'eq':
                filterConditions.push(eq(assetCategories.name, value));
                break;
              case 'ne':
                filterConditions.push(sql`${assetCategories.name} != ${value}`);
                break;
              case 'isEmpty':
                filterConditions.push(
                  sql`${assetCategories.name} IS NULL OR ${assetCategories.name} = ''`,
                );
                break;
              case 'isNotEmpty':
                filterConditions.push(
                  sql`${assetCategories.name} IS NOT NULL AND ${assetCategories.name} != ''`,
                );
                break;
            }
          } else if (fieldId === 'status') {
            switch (operator) {
              case 'eq':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    inArray(assetCategories.status, value as CategoryStatus[]),
                  );
                } else {
                  filterConditions.push(eq(assetCategories.status, value));
                }
                break;
              case 'ne':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    sql`${assetCategories.status} NOT IN (${value.map((s) => `'${s}'`).join(',')})`,
                  );
                } else {
                  filterConditions.push(
                    sql`${assetCategories.status} != ${value}`,
                  );
                }
                break;
              case 'iLike': // Contains (for backward compatibility)
                if (typeof value === 'string') {
                  const decodedValue = decodeURIComponent(value);
                  const statusValues = decodedValue
                    .split(',')
                    .map((s) => s.trim() as CategoryStatus);
                  filterConditions.push(
                    inArray(assetCategories.status, statusValues),
                  );
                } else {
                  filterConditions.push(eq(assetCategories.status, value));
                }
                break;
            }
          }
        }

        if (filterConditions.length > 0) {
          if (joinOperator === 'or') {
            whereConditions.push(or(...filterConditions));
          } else {
            whereConditions.push(and(...filterConditions));
          }
        }
      } catch {
        // Invalid JSON, ignore filters
      }
    }

    // Build sort conditions with default sort
    let orderBy = [asc(assetCategories.name), asc(assetCategories.id)];

    if (sort) {
      try {
        const parsedSort = JSON.parse(sort);
        if (parsedSort.length > 0) {
          const sortField = parsedSort[0];
          const isDesc = sortField.desc === true;

          switch (sortField.id) {
            case 'name':
              orderBy = [
                isDesc ? desc(assetCategories.name) : asc(assetCategories.name),
                asc(assetCategories.id), // Secondary sort for consistency
              ];
              break;
            case 'createdAt':
              orderBy = [
                isDesc
                  ? desc(assetCategories.createdAt)
                  : asc(assetCategories.createdAt),
                asc(assetCategories.id), // Secondary sort for consistency
              ];
              break;
            case 'updatedAt':
              orderBy = [
                isDesc
                  ? desc(assetCategories.updatedAt)
                  : asc(assetCategories.updatedAt),
                asc(assetCategories.id), // Secondary sort for consistency
              ];
              break;
          }
        }
      } catch {
        // Invalid JSON, use default sort
      }
    }

    // Execute query with optimized fields
    const result = await this.db
      .select({
        id: assetCategories.id,
        name: assetCategories.name,
        description: assetCategories.description,
        status: assetCategories.status,
        parentId: assetCategories.parentId,
        imageId: assetCategories.image,
        imagePublicUrl: media.publicUrl,
        imageFileName: media.fileName,
        createdAt: assetCategories.createdAt,
        updatedAt: assetCategories.updatedAt,
      })
      .from(assetCategories)
      .leftJoin(media, eq(assetCategories.image, media.id))
      .where(and(...whereConditions))
      .orderBy(...orderBy)
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(assetCategories)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    // Get asset category IDs for additional data
    const assetCategoryIds = result.map((ac) => ac.id);

    // Get parent information
    const parentInfoQuery = await this.db
      .select({
        id: assetCategories.id,
        parentId: assetCategories.parentId,
        parentName: sql<string>`parent_ac.name`.as('parentName'),
      })
      .from(assetCategories)
      .leftJoin(
        sql`${assetCategories} parent_ac`,
        sql`${assetCategories.parentId} = parent_ac.id`,
      )
      .where(
        and(
          inArray(assetCategories.id, assetCategoryIds),
          eq(assetCategories.isDeleted, false),
        ),
      );

    // Get subcategories count for each asset category
    const subcategoriesCountQuery = await this.db
      .select({
        parentId: assetCategories.parentId,
        count: sql<number>`count(*)`.as('count'),
      })
      .from(assetCategories)
      .where(
        and(
          inArray(assetCategories.parentId, assetCategoryIds),
          eq(assetCategories.isDeleted, false),
        ),
      )
      .groupBy(assetCategories.parentId);

    // Get assets count for each category
    const assetsCountMap = await this.getAssetsCountForCategories(
      assetCategoryIds,
      businessId,
    );

    // Create lookup maps
    const parentInfoMap = new Map(
      parentInfoQuery.map((p) => [p.id, p.parentName]),
    );
    const subcategoriesCountMap = new Map(
      subcategoriesCountQuery.map((s) => [s.parentId, s.count]),
    );

    // Generate signed URLs for images and build final data
    const data = await Promise.all(
      result.map(async (assetCategory) => {
        let imageUrl: string | undefined;

        if (assetCategory.imageFileName) {
          try {
            // Generate signed URL with 60 minutes expiration for the image
            imageUrl = await this.gcsUploadService.generateSignedUrl(
              assetCategory.imageFileName,
              'asset-categories', // folder where asset category images are stored
              60, // expiration in minutes
            );
          } catch (error) {
            console.warn(
              `Failed to generate signed URL for asset category ${assetCategory.id} image:`,
              error.message,
            );
            // Fallback to public URL if signed URL generation fails
            imageUrl = assetCategory.imagePublicUrl || undefined;
          }
        }

        return {
          id: assetCategory.id.toString(),
          name: assetCategory.name,
          description: assetCategory.description,
          status: assetCategory.status,
          parentId: assetCategory.parentId?.toString(),
          parentName: parentInfoMap.get(assetCategory.id) || undefined,
          subcategoriesCount: subcategoriesCountMap.get(assetCategory.id) || 0,
          assetsCount: assetsCountMap.get(assetCategory.id) || 0,
          image: imageUrl,
        };
      }),
    );

    return {
      data,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async checkNameAvailability(
    userId: string,
    businessId: string | null,
    name: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if an asset category with the same name already exists for this business
    // Using ilike for case-insensitive comparison
    const existingAssetCategory = await this.db
      .select()
      .from(assetCategories)
      .where(
        and(
          eq(assetCategories.businessId, businessId),
          ilike(assetCategories.name, name),
          eq(assetCategories.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    return { available: !existingAssetCategory };
  }

  async findOne(userId: string, id: string): Promise<AssetCategoryDto> {
    // Get the asset category
    const assetCategory = await this.db
      .select()
      .from(assetCategories)
      .where(
        and(eq(assetCategories.id, id), eq(assetCategories.isDeleted, false)),
      )
      .then((results) => results[0]);

    if (!assetCategory) {
      throw new NotFoundException(`Asset category with ID ${id} not found`);
    }

    // Get user's activeBusinessId to verify permission
    const user = await this.db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (
      !user ||
      !user.activeBusinessId ||
      user.activeBusinessId !== assetCategory.businessId
    ) {
      throw new UnauthorizedException('Access denied to this asset category');
    }

    return await this.mapToAssetCategoryDto(assetCategory);
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateAssetCategoryDto: UpdateAssetCategoryDto,
    imageFile?: Express.Multer.File,
    ogImageFile?: Express.Multer.File,
    metadata?: ActivityMetadata,
  ): Promise<AssetCategoryDto> {
    // Get the asset category
    const existingAssetCategory = await this.db
      .select()
      .from(assetCategories)
      .where(
        and(eq(assetCategories.id, id), eq(assetCategories.isDeleted, false)),
      )
      .then((results) => results[0]);

    if (!existingAssetCategory) {
      throw new NotFoundException(`Asset category with ID ${id} not found`);
    }

    // Verify business ownership
    if (businessId !== existingAssetCategory.businessId) {
      throw new UnauthorizedException(
        'Access denied to update this asset category',
      );
    }

    // Check for name conflict if name is being updated
    if (
      updateAssetCategoryDto.name &&
      updateAssetCategoryDto.name !== existingAssetCategory.name
    ) {
      const nameConflict = await this.db
        .select()
        .from(assetCategories)
        .where(
          and(
            eq(assetCategories.businessId, businessId),
            ilike(assetCategories.name, updateAssetCategoryDto.name),
            eq(assetCategories.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (nameConflict) {
        throw new ConflictException(
          `An asset category with the name '${updateAssetCategoryDto.name}' already exists for this business`,
        );
      }
    }

    // Handle parent category validation if being updated
    if (updateAssetCategoryDto.parentId) {
      // Prevent asset category from being its own parent
      if (updateAssetCategoryDto.parentId === id) {
        throw new BadRequestException(
          'Asset category cannot be its own parent',
        );
      }

      const parentCategory = await this.db
        .select()
        .from(assetCategories)
        .where(
          and(
            eq(assetCategories.id, updateAssetCategoryDto.parentId),
            eq(assetCategories.businessId, existingAssetCategory.businessId),
            eq(assetCategories.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!parentCategory) {
        throw new BadRequestException('Parent asset category not found');
      }
    }

    let mediaId = existingAssetCategory.image;
    let ogImageId = existingAssetCategory.ogImage;

    // Handle image update if provided
    if (imageFile) {
      mediaId = await this.mediaService.updateMediaReference(
        existingAssetCategory.image,
        imageFile,
        'asset-categories',
        businessId,
        userId,
      );
    }

    // Handle OG image update if provided
    if (ogImageFile) {
      ogImageId = await this.mediaService.updateMediaReference(
        existingAssetCategory.ogImage,
        ogImageFile,
        'asset-categories/og-images',
        businessId,
        userId,
      );
    }

    try {
      // Update the asset category
      const [updatedAssetCategory] = await this.db
        .update(assetCategories)
        .set({
          ...updateAssetCategoryDto,
          image: mediaId,
          ogImage: ogImageId,
          updatedAt: new Date(),
        })
        .where(eq(assetCategories.id, id))
        .returning();

      // Log the activity
      await this.activityLogService.logUpdate(
        id,
        EntityType.ASSET_CATEGORY,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return await this.mapToAssetCategoryDto(updatedAssetCategory);
    } catch (error) {
      throw new BadRequestException(
        `Failed to update asset category: ${error.message}`,
      );
    }
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ success: boolean; message: string }> {
    // Get the asset category
    const existingAssetCategory = await this.db
      .select()
      .from(assetCategories)
      .where(
        and(eq(assetCategories.id, id), eq(assetCategories.isDeleted, false)),
      )
      .then((results) => results[0]);

    if (!existingAssetCategory) {
      throw new NotFoundException(`Asset category with ID ${id} not found`);
    }

    if (businessId !== existingAssetCategory.businessId) {
      throw new UnauthorizedException(
        'Access denied to delete this asset category',
      );
    }

    // Check for child categories
    const childCategories = await this.db
      .select()
      .from(assetCategories)
      .where(
        and(
          eq(assetCategories.parentId, id),
          eq(assetCategories.isDeleted, false),
        ),
      );

    if (childCategories.length > 0) {
      throw new BadRequestException(
        'Cannot delete asset category with child categories. Please remove or reassign child categories first.',
      );
    }

    // Soft delete the asset category
    await this.db
      .update(assetCategories)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(assetCategories.id, id));

    // Log the activity
    await this.activityLogService.logDelete(
      id,
      EntityType.ASSET_CATEGORY,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return {
      success: true,
      message: `Asset category with ID ${id} has been deleted`,
    };
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    assetCategoryIds: string[],
    metadata?: ActivityMetadata,
  ): Promise<{
    deleted: number;
    message: string;
    deletedIds: string[];
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!assetCategoryIds || assetCategoryIds.length === 0) {
        throw new BadRequestException(
          'No asset category IDs provided for deletion',
        );
      }

      // Get all asset categories that exist and belong to the business
      const existingAssetCategories = await this.db
        .select({
          id: assetCategories.id,
          name: assetCategories.name,
          businessId: assetCategories.businessId,
        })
        .from(assetCategories)
        .where(
          and(
            inArray(assetCategories.id, assetCategoryIds),
            eq(assetCategories.businessId, businessId),
            eq(assetCategories.isDeleted, false),
          ),
        );

      if (existingAssetCategories.length === 0) {
        throw new NotFoundException(
          'No valid asset categories found for deletion',
        );
      }

      // Check if any of the found asset categories have child categories
      const categoriesWithChildren = await this.db
        .select({
          parentId: assetCategories.parentId,
          childName: assetCategories.name,
        })
        .from(assetCategories)
        .where(
          and(
            inArray(
              assetCategories.parentId,
              existingAssetCategories.map((c) => c.id),
            ),
            eq(assetCategories.isDeleted, false),
          ),
        );

      if (categoriesWithChildren.length > 0) {
        const parentIds = [
          ...new Set(categoriesWithChildren.map((c) => c.parentId)),
        ];
        const parentNames = existingAssetCategories
          .filter((c) => parentIds.includes(c.id))
          .map((c) => c.name);

        throw new BadRequestException(
          `Cannot delete asset categories with child categories: ${parentNames.join(', ')}. Please remove or reassign child categories first.`,
        );
      }

      const deletedIds: string[] = [];
      const currentTime = new Date();

      // Use transaction to ensure all deletions succeed or fail together
      await this.db.transaction(async (tx) => {
        for (const assetCategory of existingAssetCategories) {
          // Soft delete the asset category
          await tx
            .update(assetCategories)
            .set({
              isDeleted: true,
              updatedBy: userId,
              updatedAt: currentTime,
            })
            .where(eq(assetCategories.id, assetCategory.id));

          deletedIds.push(assetCategory.id);
        }
      });

      // Log bulk delete operation
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_DELETE,
        EntityType.ASSET_CATEGORY,
        deletedIds,
        { isDeleted: true, updatedBy: userId },
        userId,
        businessId,
        {
          filterCriteria: { assetCategoryIds },
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        deleted: deletedIds.length,
        message: `Successfully deleted ${deletedIds.length} asset categories`,
        deletedIds,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk delete asset categories: ${error.message}`,
      );
    }
  }

  async findAllSlim(
    userId: string,
    businessId: string | null,
  ): Promise<AssetCategorySlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Find all asset categories with only essential fields
    const assetCategoryResults = await this.db
      .select({
        id: assetCategories.id,
        name: assetCategories.name,
      })
      .from(assetCategories)
      .where(
        and(
          eq(assetCategories.isDeleted, false),
          eq(assetCategories.status, CategoryStatus.ACTIVE),
          eq(assetCategories.businessId, businessId),
        ),
      )
      .orderBy(asc(assetCategories.name), asc(assetCategories.id));

    return assetCategoryResults.map((assetCategory) => ({
      id: assetCategory.id.toString(),
      name: assetCategory.name,
    }));
  }

  async findAllHierarchy(
    userId: string,
    businessId: string | null,
  ): Promise<{ id: string; name: string; parentId: string | null }[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Get all asset categories with only the essential hierarchy fields
    const assetCategoryResults = await this.db
      .select({
        id: assetCategories.id,
        name: assetCategories.name,
        parentId: assetCategories.parentId,
      })
      .from(assetCategories)
      .where(
        and(
          eq(assetCategories.isDeleted, false),
          eq(assetCategories.status, CategoryStatus.ACTIVE),
          eq(assetCategories.businessId, businessId),
        ),
      )
      .orderBy(asc(assetCategories.name), asc(assetCategories.id));

    return assetCategoryResults.map((assetCategory) => ({
      id: assetCategory.id.toString(),
      name: assetCategory.name,
      parentId: assetCategory.parentId?.toString() || null,
    }));
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createAssetCategoryDto: CreateAssetCategoryDto,
    imageFile?: Express.Multer.File,
    ogImageFile?: Express.Multer.File,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    const assetCategory = await this.create(
      userId,
      businessId,
      createAssetCategoryDto,
      imageFile,
      ogImageFile,
      metadata,
    );
    return { id: assetCategory.id };
  }

  async bulkCreate(
    userId: string,
    businessId: string | null,
    createAssetCategoriesDto: CreateAssetCategoryDto[],
    imageFiles?: Express.Multer.File[],
    metadata?: ActivityMetadata,
  ): Promise<AssetCategoryDto[]> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!createAssetCategoriesDto || createAssetCategoriesDto.length === 0) {
        throw new BadRequestException(
          'No asset categories provided for creation',
        );
      }

      // Validate that if images are provided, they don't exceed the number of categories
      if (imageFiles && imageFiles.length > createAssetCategoriesDto.length) {
        throw new BadRequestException(
          'Number of images cannot exceed number of asset categories',
        );
      }

      // Validate imageIndex values if provided
      if (imageFiles) {
        for (let i = 0; i < createAssetCategoriesDto.length; i++) {
          const assetCategory = createAssetCategoriesDto[i];
          if (assetCategory.imageIndex !== undefined) {
            if (assetCategory.imageIndex < 0) {
              throw new BadRequestException(
                `Asset category "${assetCategory.name}" has invalid imageIndex: ${assetCategory.imageIndex}. Must be 0 or greater.`,
              );
            }
            if (assetCategory.imageIndex >= imageFiles.length) {
              throw new BadRequestException(
                `Asset category "${assetCategory.name}" has imageIndex ${assetCategory.imageIndex} but only ${imageFiles.length} images provided.`,
              );
            }
          }
        }
      }

      // Check for duplicate names within the request
      const requestNames = createAssetCategoriesDto.map((dto) =>
        dto.name.toLowerCase(),
      );
      const duplicateNames = requestNames.filter(
        (name, index) => requestNames.indexOf(name) !== index,
      );
      if (duplicateNames.length > 0) {
        throw new BadRequestException(
          `Duplicate asset category names found in request: ${duplicateNames.join(', ')}`,
        );
      }

      // Check if any asset categories with the same names already exist for this business
      const existingAssetCategories = await this.db
        .select()
        .from(assetCategories)
        .where(
          and(
            eq(assetCategories.businessId, businessId),
            sql`LOWER(${assetCategories.name}) IN (${requestNames.map((name) => `'${name}'`).join(',')})`,
            eq(assetCategories.isDeleted, false),
          ),
        );

      if (existingAssetCategories.length > 0) {
        const existingNames = existingAssetCategories.map((c) => c.name);
        throw new ConflictException(
          `Asset categories with the following names already exist: ${existingNames.join(', ')}`,
        );
      }

      const createdAssetCategories: AssetCategoryDto[] = [];

      // Use a transaction to ensure all asset categories are created or none are
      await this.db.transaction(async (tx) => {
        for (let i = 0; i < createAssetCategoriesDto.length; i++) {
          const createAssetCategoryDto = createAssetCategoriesDto[i];

          // Get image file based on imageIndex if specified, otherwise use array index
          let imageFile: Express.Multer.File | undefined;
          if (createAssetCategoryDto.imageIndex !== undefined) {
            // Use specific image index if provided
            imageFile = imageFiles?.[createAssetCategoryDto.imageIndex];
          } else {
            // Fallback to array index mapping for backward compatibility
            imageFile = imageFiles?.[i];
          }

          let mediaId: string | undefined;

          // Upload image if provided for this asset category
          if (imageFile) {
            const uploadedMedia = await this.mediaService.uploadMedia(
              imageFile,
              'asset-categories',
              businessId,
              userId,
            );
            mediaId = uploadedMedia.id;
          }

          // Create the asset category
          const [newAssetCategory] = await tx
            .insert(assetCategories)
            .values({
              businessId,
              name: createAssetCategoryDto.name,
              description: createAssetCategoryDto.description,
              parentId: createAssetCategoryDto.parentId,
              image: mediaId,
              createdBy: userId,
              status: createAssetCategoryDto.status ?? CategoryStatus.ACTIVE,
            })
            .returning();

          createdAssetCategories.push(
            await this.mapToAssetCategoryDto(newAssetCategory),
          );
        }
      });

      // Log bulk create operation
      const createdIds = createdAssetCategories.map((ac) => ac.id);
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_CREATE,
        EntityType.ASSET_CATEGORY,
        createdIds,
        {
          names: createAssetCategoriesDto.map((dto) => dto.name),
          status: CategoryStatus.ACTIVE,
        },
        userId,
        businessId,
        {
          filterCriteria: { count: createAssetCategoriesDto.length },
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return createdAssetCategories;
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk create asset categories: ${error.message}`,
      );
    }
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createAssetCategoriesDto: CreateAssetCategoryDto[],
    imageFiles?: Express.Multer.File[],
    metadata?: ActivityMetadata,
  ): Promise<{ ids: string[] }> {
    const assetCategories = await this.bulkCreate(
      userId,
      businessId,
      createAssetCategoriesDto,
      imageFiles,
      metadata,
    );
    return { ids: assetCategories.map((assetCategory) => assetCategory.id) };
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateAssetCategoryDto: UpdateAssetCategoryDto,
    imageFile?: Express.Multer.File,
    ogImageFile?: Express.Multer.File,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    await this.update(
      userId,
      businessId,
      id,
      updateAssetCategoryDto,
      imageFile,
      ogImageFile,
      metadata,
    );
    return { id };
  }

  async updateAssetCategoryPositions(
    userId: string,
    businessId: string | null,
    updates: { id: string; position: number }[],
    metadata?: ActivityMetadata,
  ): Promise<{ updated: number }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!updates || updates.length === 0) {
        throw new BadRequestException('No position updates provided');
      }

      // Validate position values (must be positive integers)
      for (const update of updates) {
        if (!Number.isInteger(update.position) || update.position < 1) {
          throw new BadRequestException(
            `Invalid position ${update.position} for asset category ${update.id}. Position must be a positive integer starting from 1.`,
          );
        }
      }

      // Validate that all asset categories belong to the business
      const assetCategoryIds = updates.map((update) => update.id);
      const existingAssetCategories = await this.db
        .select({
          id: assetCategories.id,
          parentId: assetCategories.parentId,
        })
        .from(assetCategories)
        .where(
          and(
            eq(assetCategories.businessId, businessId),
            inArray(assetCategories.id, assetCategoryIds),
            eq(assetCategories.isDeleted, false),
          ),
        );

      if (existingAssetCategories.length !== assetCategoryIds.length) {
        const foundIds = existingAssetCategories.map((ac) => ac.id);
        const missingIds = assetCategoryIds.filter(
          (id) => !foundIds.includes(id),
        );
        throw new BadRequestException(
          `Asset categories not found or don't belong to this business: ${missingIds.join(', ')}`,
        );
      }

      let updatedCount = 0;

      // Use a transaction to ensure all updates succeed or fail together
      await this.db.transaction(async (tx) => {
        for (const update of updates) {
          await tx
            .update(assetCategories)
            .set({
              updatedAt: new Date(),
            })
            .where(
              and(
                eq(assetCategories.id, update.id),
                eq(assetCategories.businessId, businessId),
                eq(assetCategories.isDeleted, false),
              ),
            );
        }
        updatedCount = updates.length;
      });

      // Log bulk position update operation
      const updatedIds = updates.map((update) => update.id);
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_UPDATE,
        EntityType.ASSET_CATEGORY,
        updatedIds,
        { positionsUpdated: true },
        userId,
        businessId,
        {
          filterCriteria: { positionUpdates: updates.length },
          executionStrategy: ExecutionStrategy.PARALLEL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return { updated: updatedCount };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update asset category positions: ${error.message}`,
      );
    }
  }

  async bulkUpdateAssetCategoryHierarchy(
    userId: string,
    businessId: string | null,
    updates: { id: string; parentId: string | null }[],
    metadata?: ActivityMetadata,
  ): Promise<{
    updated: number;
    failed: Array<{ id: string; error: string }>;
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!updates || updates.length === 0) {
        throw new BadRequestException('No hierarchy updates provided');
      }

      const failed: Array<{ id: string; error: string }> = [];
      let updatedCount = 0;

      // Validate that all asset categories belong to the business
      const assetCategoryIds = updates.map((update) => update.id);
      const existingAssetCategories = await this.db
        .select({ id: assetCategories.id, parentId: assetCategories.parentId })
        .from(assetCategories)
        .where(
          and(
            eq(assetCategories.businessId, businessId),
            inArray(assetCategories.id, assetCategoryIds),
            eq(assetCategories.isDeleted, false),
          ),
        );

      const existingAssetCategoryMap = new Map(
        existingAssetCategories.map((ac) => [ac.id, ac]),
      );

      // Use a transaction to ensure data consistency
      await this.db.transaction(async (tx) => {
        for (const update of updates) {
          try {
            // Check if asset category exists
            if (!existingAssetCategoryMap.has(update.id)) {
              failed.push({
                id: update.id,
                error:
                  'Asset category not found or does not belong to this business',
              });
              continue;
            }

            // Check if parent exists (if parentId is not null)
            if (update.parentId !== null) {
              const parentExists = await tx
                .select({ id: assetCategories.id })
                .from(assetCategories)
                .where(
                  and(
                    eq(assetCategories.id, update.parentId),
                    eq(assetCategories.businessId, businessId),
                    eq(assetCategories.isDeleted, false),
                  ),
                )
                .limit(1);

              if (parentExists.length === 0) {
                failed.push({
                  id: update.id,
                  error: 'Parent asset category not found',
                });
                continue;
              }

              // Check for circular reference (asset category cannot be its own ancestor)
              if (update.parentId === update.id) {
                failed.push({
                  id: update.id,
                  error: 'Asset category cannot be its own parent',
                });
                continue;
              }

              // Check if this would create a circular reference
              const wouldCreateCircle = async (
                parentId: string,
                childId: string,
              ): Promise<boolean> => {
                const parent = await tx
                  .select({ parentId: assetCategories.parentId })
                  .from(assetCategories)
                  .where(
                    and(
                      eq(assetCategories.id, parentId),
                      eq(assetCategories.businessId, businessId),
                      eq(assetCategories.isDeleted, false),
                    ),
                  )
                  .limit(1);

                if (parent.length === 0) return false;
                if (parent[0].parentId === childId) return true;
                if (parent[0].parentId)
                  return wouldCreateCircle(parent[0].parentId, childId);
                return false;
              };

              if (await wouldCreateCircle(update.parentId, update.id)) {
                failed.push({
                  id: update.id,
                  error: 'Would create circular reference',
                });
                continue;
              }
            }

            // Update the asset category
            await tx
              .update(assetCategories)
              .set({
                parentId: update.parentId,
                updatedAt: new Date(),
              })
              .where(
                and(
                  eq(assetCategories.id, update.id),
                  eq(assetCategories.businessId, businessId),
                  eq(assetCategories.isDeleted, false),
                ),
              );

            updatedCount++;

            // Log the hierarchy update activity
            await this.activityLogService.logUpdate(
              update.id,
              EntityType.ASSET_CATEGORY,
              userId,
              businessId,
              {
                ipAddress: metadata?.ipAddress,
                userAgent: metadata?.userAgent,
                sessionId: metadata?.sessionId,
              },
            );
          } catch (error) {
            failed.push({
              id: update.id,
              error: `Failed to update: ${error.message}`,
            });
          }
        }
      });

      return { updated: updatedCount, failed };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update asset category hierarchy: ${error.message}`,
      );
    }
  }

  async bulkUpdateAssetCategoryStatus(
    userId: string,
    businessId: string | null,
    assetCategoryIds: string[],
    status: CategoryStatus,
    metadata?: ActivityMetadata,
  ): Promise<{
    updated: number;
    updatedIds: string[];
    failed: Array<{ assetCategoryId: string; error: string }>;
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!assetCategoryIds || assetCategoryIds.length === 0) {
        throw new BadRequestException(
          'No asset category IDs provided for status update',
        );
      }

      let updatedCount = 0;
      const updatedIds: string[] = [];
      const failed: Array<{ assetCategoryId: string; error: string }> = [];

      // Process each asset category ID
      await this.db.transaction(async (tx) => {
        for (const assetCategoryId of assetCategoryIds) {
          try {
            // Check if asset category exists and belongs to the business
            const existingAssetCategory = await tx
              .select()
              .from(assetCategories)
              .where(
                and(
                  eq(assetCategories.id, assetCategoryId),
                  eq(assetCategories.businessId, businessId),
                  eq(assetCategories.isDeleted, false),
                ),
              )
              .then((results) => results[0]);

            if (!existingAssetCategory) {
              failed.push({
                assetCategoryId,
                error: 'Asset category not found or access denied',
              });
              continue;
            }

            // Skip if status is already the same
            if (existingAssetCategory.status === status) {
              failed.push({
                assetCategoryId,
                error: `Asset category already has status: ${status}`,
              });
              continue;
            }

            // Update the asset category status
            await tx
              .update(assetCategories)
              .set({
                status,
                updatedAt: new Date(),
              })
              .where(
                and(
                  eq(assetCategories.id, assetCategoryId),
                  eq(assetCategories.businessId, businessId),
                  eq(assetCategories.isDeleted, false),
                ),
              );

            updatedCount++;
            updatedIds.push(assetCategoryId);
          } catch (error) {
            failed.push({
              assetCategoryId,
              error: `Failed to update: ${error.message}`,
            });
          }
        }
      });

      // Log bulk status change operation if any asset categories were updated
      if (updatedIds.length > 0) {
        await this.activityLogService.logBulkOperation(
          ActivityType.BULK_STATUS_CHANGE,
          EntityType.ASSET_CATEGORY,
          updatedIds,
          { status },
          userId,
          businessId,
          {
            filterCriteria: { assetCategoryIds, targetStatus: status },
            failures: failed.map((f) => ({
              id: f.assetCategoryId,
              error: f.error,
            })),
            executionStrategy: ExecutionStrategy.SEQUENTIAL,
            source: metadata?.source || ActivitySource.WEB,
            ipAddress: metadata?.ipAddress,
            userAgent: metadata?.userAgent,
            sessionId: metadata?.sessionId,
          },
        );
      }

      return {
        updated: updatedCount,
        updatedIds,
        failed,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk update asset category status: ${error.message}`,
      );
    }
  }

  private async getAssetsCountForCategory(
    assetCategoryId: string,
    businessId: string,
  ): Promise<number> {
    try {
      const result = await this.db
        .select({ count: sql<number>`count(*)` })
        .from(assets)
        .where(
          and(
            eq(assets.businessId, businessId),
            eq(assets.categoryId, assetCategoryId),
            eq(assets.isDeleted, false),
          ),
        );

      return Number(result[0].count);
    } catch (error) {
      console.warn(
        `Failed to get assets count for asset category ${assetCategoryId}:`,
        error.message,
      );
      return 0;
    }
  }

  private async getAssetsCountForCategories(
    assetCategoryIds: string[],
    businessId: string,
  ): Promise<Map<string, number>> {
    try {
      if (assetCategoryIds.length === 0) {
        return new Map();
      }

      // Count assets where assetCategoryId matches
      const assetResults = await this.db
        .select({
          assetCategoryId: assets.categoryId,
          count: sql<number>`count(*)`.as('count'),
        })
        .from(assets)
        .where(
          and(
            eq(assets.businessId, businessId),
            inArray(assets.categoryId, assetCategoryIds),
            eq(assets.isDeleted, false),
          ),
        )
        .groupBy(assets.categoryId);

      // Combine the counts
      const countsMap = new Map<string, number>();

      // Initialize all asset category IDs with 0
      assetCategoryIds.forEach((id) => countsMap.set(id, 0));

      // Add asset counts
      assetResults.forEach((result) => {
        if (result.assetCategoryId) {
          countsMap.set(
            result.assetCategoryId,
            (countsMap.get(result.assetCategoryId) || 0) + result.count,
          );
        }
      });

      return countsMap;
    } catch (error) {
      console.warn(
        'Failed to get assets count for asset categories:',
        error.message,
      );
      return new Map();
    }
  }

  private async mapToAssetCategoryDto(
    assetCategory: typeof assetCategories.$inferSelect,
  ): Promise<AssetCategoryDto> {
    // Get assets count for this asset category
    const assetsCount = await this.getAssetsCountForCategory(
      assetCategory.id,
      assetCategory.businessId,
    );

    // Get user names for createdBy and updatedBy
    const createdByName = await this.usersService.getUserName(
      assetCategory.createdBy.toString(),
    );
    let updatedByName: string | undefined;
    if (assetCategory.updatedBy) {
      updatedByName = await this.usersService.getUserName(
        assetCategory.updatedBy.toString(),
      );
    }

    const assetCategoryDto: AssetCategoryDto = {
      id: assetCategory.id.toString(),
      businessId: assetCategory.businessId.toString(),
      name: assetCategory.name,
      description: assetCategory.description,
      parentId: assetCategory.parentId?.toString(),
      createdBy: createdByName,
      updatedBy: updatedByName,
      status: assetCategory.status,
      assetsCount,
      createdAt: assetCategory.createdAt,
      updatedAt: assetCategory.updatedAt,
    };

    // Fetch media information and generate signed URL if image exists
    if (assetCategory.image) {
      try {
        const mediaData = await this.mediaService.findById(
          assetCategory.image,
          assetCategory.businessId,
        );

        // Generate signed URL with 60 minutes expiration for the image
        assetCategoryDto.image = await this.gcsUploadService.generateSignedUrl(
          mediaData.fileName,
          'asset-categories', // folder where asset category images are stored
          60, // expiration in minutes
        );
      } catch (error) {
        // If media is not found or signed URL generation fails, just continue without it
        console.warn(
          `Failed to generate signed URL for asset category ${assetCategory.id} image:`,
          error.message,
        );
      }
    }

    // Fetch OG image information and generate signed URL if ogImage exists
    if (assetCategory.ogImage) {
      // TODO: check if this is correct
      try {
        const ogMediaData = await this.mediaService.findById(
          assetCategory.ogImage,
          assetCategory.businessId,
        );

        // Generate signed URL with 60 minutes expiration for the OG image
        assetCategoryDto.ogImage =
          await this.gcsUploadService.generateSignedUrl(
            ogMediaData.fileName,
            'asset-categories/og-images', // folder where OG images are stored
            60, // expiration in minutes
          );
      } catch (error) {
        // If media is not found or signed URL generation fails, just continue without it
        console.warn(
          `Failed to generate signed URL for asset category ${assetCategory.id} OG image:`,
          error.message,
        );
      }
    }

    return assetCategoryDto;
  }
}
