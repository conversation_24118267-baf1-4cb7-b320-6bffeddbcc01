import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  Query,
  UseGuards,
  UseInterceptors,
  UploadedFiles,
} from '@nestjs/common';
import {
  FilesInterceptor,
  FileFieldsInterceptor,
} from '@nestjs/platform-express';
import { AssetCategoriesService } from './asset-categories.service';
import { CreateAssetCategoryDto } from './dto/create-asset-category.dto';
import { BulkCreateAssetCategoryDto } from './dto/bulk-create-asset-category.dto';
import { UpdateAssetCategoryDto } from './dto/update-asset-category.dto';
import { AssetCategoryDto } from './dto/asset-category.dto';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiParam,
  ApiBearerAuth,
  ApiQuery,
  ApiBody,
  ApiConsumes,
} from '@nestjs/swagger';
import { AssetCategoryNameAvailabilityResponseDto } from './dto/check-asset-category-name.dto';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { ActivityMetadata } from '../auth/decorators/activity-metadata.decorator';
import { Permission } from '../shared/types/permission.enum';
import type { ActivityMetadata as ActivityMetadataType } from '../shared/types/activity-metadata.type';
import { AssetCategorySlimDto } from './dto/asset-category-slim.dto';
import { PaginatedAssetCategoriesResponseDto } from './dto/paginated-asset-categories-response.dto';
import { DeleteAssetCategoryResponseDto } from './dto/delete-asset-category-response.dto';
import { AssetCategoryIdResponseDto } from './dto/asset-category-id-response.dto';
import { BulkAssetCategoryIdsResponseDto } from './dto/bulk-asset-category-ids-response.dto';
import {
  UpdateAssetCategoryPositionsDto,
  UpdateAssetCategoryPositionsResponseDto,
} from './dto/update-asset-category-positions.dto';
import {
  BulkUpdateAssetCategoryHierarchyDto,
  BulkUpdateAssetCategoryHierarchyResponseDto,
} from './dto/bulk-update-asset-category-hierarchy.dto';
import {
  BulkDeleteAssetCategoryDto,
  BulkDeleteAssetCategoryResponseDto,
} from './dto/bulk-delete-asset-category.dto';
import {
  BulkUpdateAssetCategoryStatusDto,
  BulkUpdateAssetCategoryStatusResponseDto,
} from './dto/bulk-update-asset-category-status.dto';

@ApiTags('asset-categories')
@Controller('asset-categories')
@UseGuards(PermissionsGuard)
export class AssetCategoriesController {
  constructor(
    private readonly assetCategoriesService: AssetCategoriesService,
  ) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_CATEGORY_CREATE)
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'image', maxCount: 1 },
      { name: 'ogImage', maxCount: 1 },
    ]),
  )
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Create a new asset category with optional images' })
  @ApiBody({
    description:
      'Asset category creation with optional image and ogImage upload',
    schema: {
      type: 'object',
      properties: {
        name: {
          type: 'string',
          example: 'Computers',
          description: 'Asset category name',
        },
        description: {
          type: 'string',
          example: 'Computer equipment and accessories',
          description: 'Asset category description',
        },
        parentId: {
          type: 'string',
          format: 'uuid',
          description: 'Parent asset category ID',
        },
        status: {
          type: 'string',
          enum: ['active', 'inactive'],
          description: 'Asset category status',
          default: 'active',
        },
        image: {
          type: 'string',
          format: 'binary',
          description: 'Asset category image file',
        },
        ogImage: {
          type: 'string',
          format: 'binary',
          description: 'Open Graph image file for social media',
        },
      },
      required: ['name'],
    },
  })
  @ApiResponse({
    status: 201,
    description: 'The asset category has been successfully created',
    type: AssetCategoryIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or file',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Asset category name already exists',
  })
  create(
    @Request() req: any,
    @Body() createAssetCategoryDto: CreateAssetCategoryDto,
    @UploadedFiles()
    files: {
      image?: Express.Multer.File[];
      ogImage?: Express.Multer.File[];
    } = {},
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<AssetCategoryIdResponseDto> {
    // Extract image and ogImage from the files object
    const image = files?.image?.[0];
    const ogImage = files?.ogImage?.[0];

    return this.assetCategoriesService.createAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      createAssetCategoryDto,
      image,
      ogImage,
      metadata,
    );
  }

  @Post('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_CATEGORY_CREATE)
  @UseInterceptors(FilesInterceptor('images'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Bulk create asset categories with optional images',
  })
  @ApiBody({
    description:
      'Bulk asset category creation with optional image uploads. Images can be mapped to categories using field names like "image_0", "image_1", etc., or by using the assetCategories array with imageIndex property.',
    schema: {
      type: 'object',
      properties: {
        assetCategories: {
          type: 'string',
          description:
            'JSON string containing array of asset category objects. Each category can optionally include an "imageIndex" property to specify which image file to use.',
          example:
            '[{"name":"Computers","status":"active","imageIndex":0},{"name":"Furniture","status":"active"},{"name":"Vehicles","status":"active","imageIndex":1}]',
        },
        images: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
          description:
            'Array of asset category image files (optional). Images are mapped to categories using the imageIndex property in the assetCategories array.',
        },
      },
      required: ['assetCategories'],
    },
  })
  @ApiResponse({
    status: 201,
    description: 'The asset categories have been successfully created',
    type: BulkAssetCategoryIdsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data, duplicate names, or files',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Asset category names already exist',
  })
  bulkCreate(
    @Request() req: any,
    @Body() bulkCreateAssetCategoryDto: BulkCreateAssetCategoryDto,
    @UploadedFiles() images: Express.Multer.File[] = [],
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkAssetCategoryIdsResponseDto> {
    return this.assetCategoriesService.bulkCreateAndReturnIds(
      req.user.id,
      req.user.activeBusinessId,
      bulkCreateAssetCategoryDto.assetCategories,
      images,
      metadata,
    );
  }

  @Delete('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_CATEGORY_DELETE)
  @ApiOperation({ summary: 'Bulk delete asset categories' })
  @ApiBody({
    description: 'Array of asset category IDs to delete',
    type: BulkDeleteAssetCategoryDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Asset categories have been successfully deleted',
    type: BulkDeleteAssetCategoryResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or asset categories not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'One or more asset categories not found',
  })
  async bulkDelete(
    @Request() req: any,
    @Body() bulkDeleteAssetCategoryDto: BulkDeleteAssetCategoryDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkDeleteAssetCategoryResponseDto> {
    return this.assetCategoriesService.bulkDelete(
      req.user.id,
      req.user.activeBusinessId,
      bulkDeleteAssetCategoryDto.assetCategoryIds,
      metadata,
    );
  }

  @Patch('bulk-status')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_CATEGORY_UPDATE)
  @ApiOperation({ summary: 'Bulk update asset category status' })
  @ApiBody({
    description: 'Array of asset category IDs and status to update',
    type: BulkUpdateAssetCategoryStatusDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Asset category status has been successfully updated',
    type: BulkUpdateAssetCategoryStatusResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or asset categories not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async bulkUpdateStatus(
    @Request() req: any,
    @Body() bulkUpdateStatusDto: BulkUpdateAssetCategoryStatusDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkUpdateAssetCategoryStatusResponseDto> {
    const result =
      await this.assetCategoriesService.bulkUpdateAssetCategoryStatus(
        req.user.id,
        req.user.activeBusinessId,
        bulkUpdateStatusDto.assetCategoryIds,
        bulkUpdateStatusDto.status,
        metadata,
      );

    return {
      updated: result.updated,
      message: `Successfully updated status for ${result.updated} asset categories`,
      updatedIds: result.updatedIds,
      failed: result.failed.length > 0 ? result.failed : undefined,
    };
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_CATEGORY_READ)
  @ApiOperation({
    summary: 'Get all asset categories for the active business',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'from',
    description: 'Filter from date (YYYY-MM-DD format)',
    required: false,
    type: String,
    example: '2025-05-24',
  })
  @ApiQuery({
    name: 'to',
    description: 'Filter to date (YYYY-MM-DD format)',
    required: false,
    type: String,
    example: '2025-05-24',
  })
  @ApiQuery({
    name: 'name',
    description: 'Filter by asset category name',
    required: false,
    type: String,
    example: 'Computers',
  })
  @ApiQuery({
    name: 'status',
    description:
      'Filter by status (comma-separated for multiple values). Supports URL encoding: status=active%2Cinactive',
    required: false,
    type: String,
    example: 'active,inactive',
  })
  @ApiQuery({
    name: 'filters',
    description:
      'Advanced filters as JSON string with operator support. Supported operators: iLike (contains), notILike (does not contain), eq (is), ne (is not), isEmpty (is empty), isNotEmpty (is not empty)',
    required: false,
    type: String,
    example:
      '[{"id":"name","value":"Computer","operator":"iLike","type":"text","rowId":"1"},{"id":"status","value":"active","operator":"eq","type":"select","rowId":"2"}]',
  })
  @ApiQuery({
    name: 'joinOperator',
    description: 'Join operator for advanced filters',
    required: false,
    type: String,
    enum: ['and', 'or'],
    example: 'and',
  })
  @ApiQuery({
    name: 'sort',
    description:
      'Sort configuration as JSON string. Supported fields: name, createdAt, updatedAt',
    required: false,
    type: String,
    example: '[{"id":"updatedAt","desc":true}]',
  })
  @ApiResponse({
    status: 200,
    description:
      "Returns all active asset categories for the user's active business with pagination (optimized with only essential fields)",
    type: PaginatedAssetCategoriesResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Request() req: any,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('name') name?: string,
    @Query('status') status?: string,
    @Query('filters') filters?: string,
    @Query('joinOperator') joinOperator?: 'and' | 'or',
    @Query('sort') sort?: string,
  ): Promise<PaginatedAssetCategoriesResponseDto> {
    return this.assetCategoriesService.findAllOptimized(
      req.user.id,
      req.user.activeBusinessId,
      page,
      limit,
      from,
      to,
      name,
      status,
      filters,
      joinOperator,
      sort,
    );
  }

  @Get('check-name-availability')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_CATEGORY_READ)
  @ApiOperation({ summary: 'Check if an asset category name is available' })
  @ApiQuery({
    name: 'name',
    description: 'Asset category name to check',
    required: true,
    type: String,
    example: 'Computers',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns availability status of the asset category name',
    type: AssetCategoryNameAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkNameAvailability(
    @Request() req: any,
    @Query('name') name: string,
  ): Promise<AssetCategoryNameAvailabilityResponseDto> {
    return this.assetCategoriesService.checkNameAvailability(
      req.user.id,
      req.user.activeBusinessId,
      name,
    );
  }

  @Get('slim')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_CATEGORY_READ)
  @ApiOperation({
    summary: 'Get all active asset categories in slim format for dropdowns',
  })
  @ApiResponse({
    status: 200,
    description:
      "Returns all active asset categories for the user's active business in slim format",
    type: [AssetCategorySlimDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllSlim(@Request() req: any): Promise<AssetCategorySlimDto[]> {
    return this.assetCategoriesService.findAllSlim(
      req.user.id,
      req.user.activeBusinessId,
    );
  }

  @Get('hierarchy')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_CATEGORY_READ)
  @ApiOperation({ summary: 'Get all asset categories in hierarchy format' })
  @ApiResponse({
    status: 200,
    description: 'Asset categories hierarchy returned successfully',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' },
          name: { type: 'string' },
          parentId: { type: 'string', format: 'uuid', nullable: true },
          position: { type: 'number' },
        },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllHierarchy(
    @Request() req: any,
  ): Promise<{ id: string; name: string; parentId: string | null }[]> {
    return this.assetCategoriesService.findAllHierarchy(
      req.user.id,
      req.user.activeBusinessId,
    );
  }

  @Patch('batch/positions')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_CATEGORY_UPDATE)
  @ApiOperation({ summary: 'Batch update asset category positions' })
  @ApiBody({
    description: 'Array of asset category position updates',
    type: UpdateAssetCategoryPositionsDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Asset category positions have been successfully updated',
    type: UpdateAssetCategoryPositionsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or asset categories not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async updatePositions(
    @Request() req: any,
    @Body() updateAssetCategoryPositionsDto: UpdateAssetCategoryPositionsDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<UpdateAssetCategoryPositionsResponseDto> {
    const result =
      await this.assetCategoriesService.updateAssetCategoryPositions(
        req.user.id,
        req.user.activeBusinessId,
        updateAssetCategoryPositionsDto.updates,
        metadata,
      );

    return {
      updated: result.updated,
      message: `Successfully updated ${result.updated} asset category positions`,
    };
  }

  @Patch('batch/hierarchy')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_CATEGORY_UPDATE)
  @ApiOperation({ summary: 'Batch update asset category hierarchy' })
  @ApiBody({
    description: 'Array of asset category hierarchy updates',
    type: BulkUpdateAssetCategoryHierarchyDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Asset category hierarchy has been successfully updated',
    type: BulkUpdateAssetCategoryHierarchyResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or asset categories not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async updateHierarchy(
    @Request() req: any,
    @Body() bulkUpdateHierarchyDto: BulkUpdateAssetCategoryHierarchyDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkUpdateAssetCategoryHierarchyResponseDto> {
    return this.assetCategoriesService.bulkUpdateAssetCategoryHierarchy(
      req.user.id,
      req.user.activeBusinessId,
      bulkUpdateHierarchyDto.updates,
      metadata,
    );
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_CATEGORY_READ)
  @ApiOperation({ summary: 'Get a specific asset category by ID' })
  @ApiParam({
    name: 'id',
    description: 'Asset category ID',
    type: String,
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the asset category with the specified ID',
    type: AssetCategoryDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Asset category not found',
  })
  findOne(
    @Request() req: any,
    @Param('id') id: string,
  ): Promise<AssetCategoryDto> {
    return this.assetCategoriesService.findOne(req.user.id, id);
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_CATEGORY_UPDATE)
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'image', maxCount: 1 },
      { name: 'ogImage', maxCount: 1 },
    ]),
  )
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Update an asset category with optional images' })
  @ApiParam({
    name: 'id',
    description: 'Asset category ID',
    type: String,
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiBody({
    description: 'Asset category update with optional image and ogImage upload',
    schema: {
      type: 'object',
      properties: {
        name: {
          type: 'string',
          example: 'Computers',
          description: 'Asset category name',
        },
        description: {
          type: 'string',
          example: 'Computer equipment and accessories',
          description: 'Asset category description',
        },
        parentId: {
          type: 'string',
          format: 'uuid',
          description: 'Parent asset category ID',
        },
        status: {
          type: 'string',
          enum: ['active', 'inactive'],
          description: 'Asset category status',
        },
        image: {
          type: 'string',
          format: 'binary',
          description: 'Asset category image file',
        },
        ogImage: {
          type: 'string',
          format: 'binary',
          description: 'Open Graph image file for social media',
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'The asset category has been successfully updated',
    type: AssetCategoryIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or file',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Asset category not found',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Asset category name already exists',
  })
  update(
    @Request() req: any,
    @Param('id') id: string,
    @Body() updateAssetCategoryDto: UpdateAssetCategoryDto,
    @UploadedFiles()
    files: {
      image?: Express.Multer.File[];
      ogImage?: Express.Multer.File[];
    } = {},
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<AssetCategoryIdResponseDto> {
    // Extract image and ogImage from the files object
    const image = files?.image?.[0];
    const ogImage = files?.ogImage?.[0];

    return this.assetCategoriesService.updateAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateAssetCategoryDto,
      image,
      ogImage,
      metadata,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_CATEGORY_DELETE)
  @ApiOperation({ summary: 'Delete an asset category (soft delete)' })
  @ApiParam({
    name: 'id',
    description: 'Asset category ID',
    type: String,
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiResponse({
    status: 200,
    description: 'The asset category has been successfully deleted',
    type: DeleteAssetCategoryResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Asset category not found',
  })
  remove(
    @Request() req: any,
    @Param('id') id: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<DeleteAssetCategoryResponseDto> {
    return this.assetCategoriesService.remove(
      req.user.id,
      req.user.activeBusinessId,
      id,
      metadata,
    );
  }
}
