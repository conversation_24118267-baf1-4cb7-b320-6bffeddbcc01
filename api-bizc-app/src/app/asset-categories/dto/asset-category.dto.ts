import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { CategoryStatus } from '../../shared/types';

export class AssetCategoryDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'Asset Category ID',
  })
  id: string;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'Business ID',
  })
  businessId: string;

  @ApiProperty({
    example: 'Computers',
    description: 'Asset category name',
  })
  name: string;

  @ApiPropertyOptional({
    example: 'Electronic computing devices and accessories',
    description: 'Asset category description',
  })
  description?: string;

  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'Parent asset category ID',
  })
  parentId?: string;

  @ApiPropertyOptional({
    example: 'https://storage.googleapis.com/bucket/asset-categories/image.jpg',
    description: 'Asset category image URL',
  })
  image?: string;

  @ApiPropertyOptional({
    example:
      'https://storage.googleapis.com/bucket/asset-categories/og-image.jpg',
    description: 'Open Graph image URL for social media',
  })
  ogImage?: string;

  @ApiProperty({
    example: 'John Doe',
    description: 'Name of user who created this asset category',
  })
  createdBy: string;

  @ApiPropertyOptional({
    example: 'Jane Smith',
    description: 'Name of user who last updated this asset category',
  })
  updatedBy?: string;

  @ApiProperty({
    example: CategoryStatus.ACTIVE,
    enum: CategoryStatus,
    enumName: 'CategoryStatus',
    description: 'Asset category status',
  })
  status: CategoryStatus;

  @ApiProperty({
    example: '2023-01-01T00:00:00Z',
    description: 'Creation timestamp',
  })
  createdAt: Date;

  @ApiProperty({
    example: '2023-01-01T00:00:00Z',
    description: 'Last update timestamp',
  })
  updatedAt: Date;

  @ApiProperty({
    example: 5,
    description: 'Number of assets in this category',
  })
  assetsCount: number;
}
