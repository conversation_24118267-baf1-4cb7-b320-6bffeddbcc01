import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  MaxLength,
  IsUUID,
  IsInt,
  Min,
} from 'class-validator';
import { CategoryStatus } from '../../shared/types';

export class CreateAssetCategoryDto {
  @ApiProperty({
    example: 'Computers',
    description: 'Asset category name',
    maxLength: 100,
  })
  @IsString({ message: 'Asset category name must be a string' })
  @IsNotEmpty({ message: 'Asset category name is required' })
  @MaxLength(100, {
    message: 'Asset category name must be 100 characters or less',
  })
  name: string;

  @ApiProperty({
    example: 'Electronic computing devices and accessories',
    description: 'Asset category description',
    required: false,
    maxLength: 500,
  })
  @IsString({ message: 'Description must be a string' })
  @IsOptional()
  @MaxLength(500, { message: 'Description must be 500 characters or less' })
  description?: string;

  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Parent asset category ID',
  })
  @IsOptional()
  @IsUUID(4, { message: 'Parent ID must be a valid UUID' })
  parentId?: string;

  @ApiPropertyOptional({
    example: 0,
    description:
      'Index of the image file to use from uploaded images array (for bulk operations)',
    minimum: 0,
  })
  @IsOptional()
  @IsInt({ message: 'Image index must be an integer' })
  @Min(0, { message: 'Image index must be 0 or greater' })
  imageIndex?: number;

  @ApiProperty({
    enum: CategoryStatus,
    enumName: 'CategoryStatus',
    description: 'Asset category status',
    default: CategoryStatus.ACTIVE,
  })
  @IsEnum(CategoryStatus, {
    message: 'Status must be a valid category status',
  })
  @IsOptional()
  status?: CategoryStatus;
}
