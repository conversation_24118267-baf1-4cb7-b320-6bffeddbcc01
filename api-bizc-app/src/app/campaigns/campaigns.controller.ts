import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  Query,
  UseGuards,
} from '@nestjs/common';
import { CampaignsService } from './campaigns.service';
import { CreateCampaignDto } from './dto/create-campaign.dto';
import { UpdateCampaignDto } from './dto/update-campaign.dto';
import { CampaignDto } from './dto/campaign.dto';
import { CampaignSlimDto } from './dto/campaign-slim.dto';
import { CampaignIdResponseDto } from './dto/campaign-id-response.dto';
import { DeleteCampaignResponseDto } from './dto/delete-campaign-response.dto';
import { PaginatedCampaignsResponseDto } from './dto/paginated-campaigns-response.dto';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiParam,
  ApiBearerAuth,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { Permission } from '../shared/types/permission.enum';
import { ActivityMetadata } from '../auth/decorators/activity-metadata.decorator';
import type { ActivityMetadata as ActivityMetadataType } from '../shared/types/activity-metadata.type';

@ApiTags('campaigns')
@Controller('campaigns')
@UseGuards(PermissionsGuard)
export class CampaignsController {
  constructor(private readonly campaignsService: CampaignsService) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.CAMPAIGN_CREATE)
  @ApiOperation({ summary: 'Create a new campaign' })
  @ApiBody({
    description: 'Campaign creation data',
    type: CreateCampaignDto,
  })
  @ApiResponse({
    status: 201,
    description: 'The campaign has been successfully created',
    type: CampaignIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or validation errors',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Campaign name or code already exists',
  })
  create(
    @Request() req,
    @Body() createCampaignDto: CreateCampaignDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<CampaignIdResponseDto> {
    return this.campaignsService.create(
      req.user.id,
      req.user.activeBusinessId,
      createCampaignDto,
      metadata,
    );
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.CAMPAIGN_READ)
  @ApiOperation({
    summary: 'Get all campaigns for the active business',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'from',
    description: 'Start date for filtering (ISO 8601 format)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'to',
    description: 'End date for filtering (ISO 8601 format)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'name',
    description: 'Filter by campaign name (partial match)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'code',
    description: 'Filter by campaign code (partial match)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'status',
    description: 'Filter by campaign status',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'campaignType',
    description: 'Filter by campaign type',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'targetAudience',
    description: 'Filter by target audience',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'filters',
    description: 'Additional filters (JSON string)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'joinOperator',
    description: 'Join operator for multiple filters',
    required: false,
    enum: ['and', 'or'],
  })
  @ApiQuery({
    name: 'sort',
    description:
      'Sort field and direction (e.g., "name:asc", "createdAt:desc")',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'All campaigns returned successfully',
    type: PaginatedCampaignsResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Request() req,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('name') name?: string,
    @Query('code') code?: string,
    @Query('status') status?: string,
    @Query('campaignType') campaignType?: string,
    @Query('targetAudience') targetAudience?: string,
    @Query('filters') filters?: string,
    @Query('joinOperator') joinOperator?: 'and' | 'or',
    @Query('sort') sort?: string,
  ): Promise<PaginatedCampaignsResponseDto> {
    return this.campaignsService.findAllOptimized(
      req.user.id,
      req.user.activeBusinessId,
      page ? parseInt(page.toString()) : undefined,
      limit ? parseInt(limit.toString()) : undefined,
      from,
      to,
      name,
      code,
      status,
      campaignType,
      targetAudience,
      filters,
      joinOperator,
      sort,
    );
  }

  @Get('check-name-availability')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CAMPAIGN_READ)
  @ApiOperation({ summary: 'Check if a campaign name is available' })
  @ApiQuery({
    name: 'name',
    required: true,
    description: 'The name to check for availability',
    example: 'Summer Sale 2023',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns name availability',
    schema: {
      type: 'object',
      properties: {
        available: { type: 'boolean' },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkNameAvailability(
    @Request() req,
    @Query('name') name: string,
  ): Promise<{ available: boolean }> {
    return this.campaignsService.checkNameAvailability(
      req.user.id,
      req.user.activeBusinessId,
      name,
    );
  }

  @Get('slim')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CAMPAIGN_READ)
  @ApiOperation({ summary: 'Get all campaigns in slim format' })
  @ApiResponse({
    status: 200,
    description: 'All campaigns returned successfully',
    type: [CampaignSlimDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllSlim(@Request() req): Promise<CampaignSlimDto[]> {
    return this.campaignsService.findAllSlim(
      req.user.id,
      req.user.activeBusinessId,
    );
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CAMPAIGN_READ)
  @ApiOperation({ summary: 'Get a campaign by ID' })
  @ApiParam({
    name: 'id',
    description: 'Campaign ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the campaign details',
    type: CampaignDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Campaign not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to this campaign',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findOne(@Request() req, @Param('id') id: string): Promise<CampaignDto> {
    return this.campaignsService.findOne(
      req.user.id,
      req.user.activeBusinessId,
      id,
    );
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CAMPAIGN_UPDATE)
  @ApiOperation({ summary: 'Update a campaign' })
  @ApiParam({
    name: 'id',
    description: 'Campaign ID',
  })
  @ApiBody({
    description: 'Campaign update data',
    type: UpdateCampaignDto,
  })
  @ApiResponse({
    status: 200,
    description: 'The campaign has been successfully updated',
    type: CampaignDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or validation errors',
  })
  @ApiResponse({
    status: 404,
    description: 'Campaign not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to update this campaign',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Campaign name or code already exists',
  })
  update(
    @Request() req,
    @Param('id') id: string,
    @Body() updateCampaignDto: UpdateCampaignDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<CampaignDto> {
    return this.campaignsService.update(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateCampaignDto,
      metadata,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CAMPAIGN_DELETE)
  @ApiOperation({ summary: 'Delete a campaign' })
  @ApiParam({
    name: 'id',
    description: 'Campaign ID',
  })
  @ApiResponse({
    status: 200,
    description: 'The campaign has been successfully deleted',
    type: DeleteCampaignResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Campaign not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to delete this campaign',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  remove(
    @Request() req,
    @Param('id') id: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<DeleteCampaignResponseDto> {
    return this.campaignsService.remove(
      req.user.id,
      req.user.activeBusinessId,
      id,
      metadata,
    );
  }
}
