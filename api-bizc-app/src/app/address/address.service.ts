import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateAddressDto } from './dto/create-address.dto';
import { UpdateAddressDto } from './dto/update-address.dto';
import { AddressDto } from './dto/address.dto';
import { addresses } from '../drizzle/schema/address.schema';
import { eq, and } from 'drizzle-orm';
import { AddressType } from '../shared/types';

@Injectable()
export class AddressService {
  constructor(@Inject(DRIZZLE) private db: DrizzleDB) {}

  async create(
    userId: string,
    createAddressDto: CreateAddressDto,
    tx?: any, // Optional transaction parameter
  ): Promise<{ id: string }> {
    try {
      const dbInstance = tx || this.db;

      // Insert new address
      const [address] = await dbInstance
        .insert(addresses)
        .values({
          street: createAddressDto.street,
          city: createAddressDto.city,
          state: createAddressDto.state,
          zipCode: createAddressDto.zipCode,
          country: createAddressDto.country,
          addressType: createAddressDto.addressType ?? AddressType.BUSINESS,
          businessId: createAddressDto.businessId,
          userId: createAddressDto.userId,
          isDefault: createAddressDto.isDefault ?? false,
        })
        .returning();

      return {
        id: address.id,
      };
    } catch (error) {
      throw new BadRequestException(
        `Failed to create address: ${error.message}`,
      );
    }
  }

  async findById(
    userId: string,
    id: string,
    businessId?: string,
  ): Promise<AddressDto> {
    const whereConditions = [eq(addresses.id, id)];

    // Add business scope if provided
    if (businessId) {
      whereConditions.push(eq(addresses.businessId, businessId));
    }

    const address = await this.db
      .select()
      .from(addresses)
      .where(and(...whereConditions))
      .then((results) => results[0]);

    if (!address) {
      throw new NotFoundException('Address not found');
    }

    return this.mapToAddressDto(address);
  }

  async findByBusinessId(
    userId: string,
    businessId: string,
    addressType?: AddressType,
  ): Promise<AddressDto[]> {
    const whereConditions = [eq(addresses.businessId, businessId)];

    if (addressType) {
      whereConditions.push(eq(addresses.addressType, addressType));
    }

    const result = await this.db
      .select()
      .from(addresses)
      .where(and(...whereConditions))
      .orderBy(addresses.createdAt);

    return result.map((address) => this.mapToAddressDto(address));
  }

  async findByUserId(
    userId: string,
    targetUserId: string,
    addressType?: AddressType,
  ): Promise<AddressDto[]> {
    const whereConditions = [eq(addresses.userId, targetUserId)];

    if (addressType) {
      whereConditions.push(eq(addresses.addressType, addressType));
    }

    const result = await this.db
      .select()
      .from(addresses)
      .where(and(...whereConditions))
      .orderBy(addresses.createdAt);

    return result.map((address) => this.mapToAddressDto(address));
  }

  async update(
    userId: string,
    id: string,
    updateAddressDto: UpdateAddressDto,
    businessId?: string,
    tx?: any, // Optional transaction parameter
  ): Promise<{ id: string }> {
    try {
      const dbInstance = tx || this.db;

      const whereConditions = [eq(addresses.id, id)];

      // Add business scope if provided
      if (businessId) {
        whereConditions.push(eq(addresses.businessId, businessId));
      }

      // Check if address exists
      const existingAddress = await dbInstance
        .select()
        .from(addresses)
        .where(and(...whereConditions))
        .then((results) => results[0]);

      if (!existingAddress) {
        throw new NotFoundException('Address not found');
      }

      // Update the address
      const [updatedAddress] = await dbInstance
        .update(addresses)
        .set({
          street: updateAddressDto.street,
          city: updateAddressDto.city,
          state: updateAddressDto.state,
          zipCode: updateAddressDto.zipCode,
          country: updateAddressDto.country,
          addressType: updateAddressDto.addressType,
          businessId: updateAddressDto.businessId,
          userId: updateAddressDto.userId,
          isDefault: updateAddressDto.isDefault,
          updatedAt: new Date(),
        })
        .where(and(...whereConditions))
        .returning();

      if (!updatedAddress) {
        throw new NotFoundException('Address not found');
      }

      return {
        id: updatedAddress.id,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update address: ${error.message}`,
      );
    }
  }

  async delete(
    userId: string,
    id: string,
    businessId?: string,
    tx?: any, // Optional transaction parameter
  ): Promise<{ id: string; message: string }> {
    const dbInstance = tx || this.db;

    const whereConditions = [eq(addresses.id, id)];

    // Add business scope if provided
    if (businessId) {
      whereConditions.push(eq(addresses.businessId, businessId));
    }

    const address = await dbInstance
      .select()
      .from(addresses)
      .where(and(...whereConditions))
      .then((results) => results[0]);

    if (!address) {
      throw new NotFoundException('Address not found');
    }

    // Soft delete the address (if the schema supports it) or hard delete
    await dbInstance.delete(addresses).where(and(...whereConditions));

    return {
      id: address.id,
      message: 'Address deleted successfully',
    };
  }

  /**
   * Map database address record to AddressDto
   */
  private mapToAddressDto(address: any): AddressDto {
    return {
      id: address.id,
      street: address.street,
      city: address.city,
      state: address.state,
      zipCode: address.zipCode,
      country: address.country,
      addressType: address.addressType,
      businessId: address.businessId,
      userId: address.userId,
      isDefault: address.isDefault,
      createdAt: address.createdAt,
      updatedAt: address.updatedAt,
    };
  }
}
