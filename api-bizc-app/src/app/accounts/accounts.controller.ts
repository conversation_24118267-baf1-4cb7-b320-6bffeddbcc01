import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  Query,
  UseGuards,
} from '@nestjs/common';
import { AccountsService } from './accounts.service';
import { CreateAccountDto } from './dto/create-account.dto';
import { BulkCreateAccountDto } from './dto/bulk-create-account.dto';
import { UpdateAccountDto } from './dto/update-account.dto';
import { AccountDto } from './dto/account.dto';
import { AccountSlimDto } from './dto/account-slim.dto';
import { AccountIdResponseDto } from './dto/account-id-response.dto';
import { PaginatedAccountsResponseDto } from './dto/paginated-accounts-response.dto';
import { BulkAccountIdsResponseDto } from './dto/bulk-account-ids-response.dto';
import { BulkDeleteAccountDto } from './dto/bulk-delete-account.dto';
import { BulkDeleteAccountResponseDto } from './dto/bulk-delete-account-response.dto';
import { DeleteAccountResponseDto } from './dto/delete-account-response.dto';
import { FilteredAccountsResponseDto } from './dto/filtered-accounts-response.dto';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiParam,
  ApiBearerAuth,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { AccountNameAvailabilityResponseDto } from './dto/check-account-name.dto';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { Permission } from '../shared/types/permission.enum';
import { ActivityMetadata } from '../auth/decorators/activity-metadata.decorator';
import type { ActivityMetadata as ActivityMetadataType } from '../shared/types/activity-metadata.type';

@ApiTags('accounts')
@Controller('accounts')
@UseGuards(PermissionsGuard)
export class AccountsController {
  constructor(private readonly accountsService: AccountsService) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOUNT_CREATE)
  @ApiOperation({ summary: 'Create a new account' })
  @ApiResponse({
    status: 201,
    description: 'The account has been successfully created',
    type: AccountIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Account name or number already exists',
  })
  create(
    @Request() req,
    @Body() createAccountDto: CreateAccountDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<AccountIdResponseDto> {
    return this.accountsService.createAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      createAccountDto,
      metadata,
    );
  }

  @Post('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOUNT_CREATE)
  @ApiOperation({ summary: 'Bulk create accounts' })
  @ApiResponse({
    status: 201,
    description: 'The accounts have been successfully created',
    type: BulkAccountIdsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or duplicate names',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Account names or numbers already exist',
  })
  bulkCreate(
    @Request() req,
    @Body() bulkCreateAccountDto: BulkCreateAccountDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkAccountIdsResponseDto> {
    return this.accountsService.bulkCreateAndReturnIds(
      req.user.id,
      req.user.activeBusinessId,
      bulkCreateAccountDto.accounts,
      metadata,
    );
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOUNT_READ)
  @ApiOperation({
    summary: 'Get all accounts for the active business',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'from',
    description: 'Start date for filtering (YYYY-MM-DD)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'to',
    description: 'End date for filtering (YYYY-MM-DD)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'accountName',
    description: 'Filter by account name',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'accountNumber',
    description: 'Filter by account number',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'accountCategory',
    description: 'Filter by account category',
    required: false,
    enum: ['ASSETS', 'LIABILITIES', 'EQUITY', 'REVENUE', 'EXPENSES'],
  })
  @ApiQuery({
    name: 'accountType',
    description: 'Filter by account type',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'accountDetailType',
    description: 'Filter by account detail type',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'status',
    description: 'Filter by status',
    required: false,
    enum: ['active', 'inactive'],
  })
  @ApiQuery({
    name: 'filters',
    description:
      'Advanced filters as JSON string with operator support. Supported operators: iLike (contains), notILike (does not contain), eq (is), ne (is not), isEmpty (is empty), isNotEmpty (is not empty)',
    required: false,
    type: String,
    example:
      '[{"id":"accountName","value":"Cash","operator":"iLike","type":"text","rowId":"1"},{"id":"status","value":"active","operator":"eq","type":"select","rowId":"2"},{"id":"accountCategory","value":"ASSETS","operator":"eq","type":"select","rowId":"3"}]',
  })
  @ApiQuery({
    name: 'joinOperator',
    description: 'Join operator for advanced filters',
    required: false,
    type: String,
    enum: ['and', 'or'],
    example: 'and',
  })
  @ApiQuery({
    name: 'sort',
    description:
      'Sort configuration as JSON string. Supported fields: accountName, accountNumber, accountCategory, accountType, openingBalance, createdAt, updatedAt',
    required: false,
    type: String,
    example: '[{"id":"accountName","desc":false}]',
  })
  @ApiResponse({
    status: 200,
    description: 'Accounts returned successfully',
    type: PaginatedAccountsResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Request() req,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('accountName') accountName?: string,
    @Query('accountNumber') accountNumber?: string,
    @Query('accountCategory') accountCategory?: string,
    @Query('accountType') accountType?: string,
    @Query('accountDetailType') accountDetailType?: string,
    @Query('status') status?: string,
    @Query('filters') filters?: string,
    @Query('joinOperator') joinOperator?: 'and' | 'or',
    @Query('sort') sort?: string,
  ): Promise<PaginatedAccountsResponseDto> {
    return this.accountsService.findAllOptimized(
      req.user.id,
      req.user.activeBusinessId,
      page ? parseInt(page.toString()) : undefined,
      limit ? parseInt(limit.toString()) : undefined,
      from,
      to,
      accountName,
      accountNumber,
      accountCategory,
      accountType,
      accountDetailType,
      status,
      filters,
      joinOperator,
      sort,
    );
  }

  @Get('filter')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOUNT_READ)
  @ApiOperation({
    summary: 'Filter accounts by category, type, and/or detail type',
    description:
      'Get accounts filtered by accountCategory, accountType, and/or accountDetailType. All parameters are optional and can be used in any combination.',
  })
  @ApiQuery({
    name: 'accountCategory',
    description: 'Filter by account category',
    required: false,
    enum: ['ASSETS', 'LIABILITIES', 'EQUITY', 'REVENUE', 'EXPENSES'],
  })
  @ApiQuery({
    name: 'accountType',
    description: 'Filter by account type',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'accountDetailType',
    description: 'Filter by account detail type',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Filtered accounts returned successfully',
    type: FilteredAccountsResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findFilteredAccounts(
    @Request() req,
    @Query('accountCategory') accountCategory?: string,
    @Query('accountType') accountType?: string,
    @Query('accountDetailType') accountDetailType?: string,
  ): Promise<FilteredAccountsResponseDto> {
    return this.accountsService
      .findFilteredAccounts(
        req.user.id,
        req.user.activeBusinessId,
        accountCategory,
        accountType,
        accountDetailType,
      )
      .then((data) => ({
        data,
        message: 'Accounts filtered successfully',
      }));
  }

  @Get('check-name-availability')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOUNT_READ)
  @ApiOperation({
    summary: 'Check if an account name is available for the business',
  })
  @ApiQuery({
    name: 'accountName',
    description: 'Account name to check',
    required: true,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Account name availability checked successfully',
    type: AccountNameAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkAccountNameAvailability(
    @Request() req,
    @Query('accountName') accountName: string,
  ): Promise<{ available: boolean }> {
    return this.accountsService.checkAccountNameAvailability(
      req.user.id,
      req.user.activeBusinessId,
      accountName,
    );
  }

  @Get('check-number-availability')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOUNT_READ)
  @ApiOperation({
    summary: 'Check if an account number is available for the business',
  })
  @ApiQuery({
    name: 'accountNumber',
    description: 'Account number to check',
    required: true,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Account number availability checked successfully',
    type: AccountNameAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkAccountNumberAvailability(
    @Request() req,
    @Query('accountNumber') accountNumber: string,
  ): Promise<{ available: boolean }> {
    return this.accountsService.checkAccountNumberAvailability(
      req.user.id,
      req.user.activeBusinessId,
      accountNumber,
    );
  }

  @Get('slim')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOUNT_READ)
  @ApiOperation({ summary: 'Get all accounts in slim format' })
  @ApiResponse({
    status: 200,
    description: 'All accounts returned successfully',
    type: [AccountSlimDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllSlim(@Request() req): Promise<AccountSlimDto[]> {
    return this.accountsService.findAllSlim(
      req.user.id,
      req.user.activeBusinessId,
    );
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOUNT_READ)
  @ApiOperation({ summary: 'Get account by ID' })
  @ApiParam({
    name: 'id',
    description: 'Account ID',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Account returned successfully',
    type: AccountDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Account not found',
  })
  findOne(@Request() req, @Param('id') id: string): Promise<AccountDto> {
    return this.accountsService.findOne(
      req.user.id,
      req.user.activeBusinessId,
      id,
    );
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOUNT_UPDATE)
  @ApiOperation({ summary: 'Update account by ID' })
  @ApiParam({
    name: 'id',
    description: 'Account ID',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Account updated successfully',
    type: AccountIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Account not found',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Account name or number already exists',
  })
  update(
    @Request() req,
    @Param('id') id: string,
    @Body() updateAccountDto: UpdateAccountDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<AccountIdResponseDto> {
    return this.accountsService.updateAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateAccountDto,
      metadata,
    );
  }

  @Delete('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOUNT_DELETE)
  @ApiOperation({ summary: 'Bulk delete accounts' })
  @ApiBody({
    description: 'Array of account IDs to delete',
    type: BulkDeleteAccountDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Accounts deleted successfully',
    type: BulkDeleteAccountResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  bulkDelete(
    @Request() req,
    @Body() bulkDeleteAccountDto: BulkDeleteAccountDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkDeleteAccountResponseDto> {
    return this.accountsService.bulkDelete(
      req.user.id,
      req.user.activeBusinessId,
      bulkDeleteAccountDto.ids,
      metadata,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOUNT_DELETE)
  @ApiOperation({ summary: 'Delete account by ID' })
  @ApiParam({
    name: 'id',
    description: 'Account ID',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Account deleted successfully',
    type: DeleteAccountResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Account not found',
  })
  remove(
    @Request() req,
    @Param('id') id: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<DeleteAccountResponseDto> {
    return this.accountsService.remove(
      req.user.id,
      req.user.activeBusinessId,
      id,
      metadata,
    );
  }
}
