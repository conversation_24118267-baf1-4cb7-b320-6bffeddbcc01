import {
  Controller,
  Delete,
  Post,
  Put,
  Body,
  Param,
  Request,
} from '@nestjs/common';
import {
  SessionId,
  Source,
  RequestContext,
  IpAddress,
  UserAgent,
  type RequestContextType,
} from '../shared/decorators';
import { ActivitySource } from '../shared/types/activity.enum';

/**
 * Example controller showing different ways to use the activity logging decorators
 */
@Controller('accommodation-units')
export class AccommodationUnitsController {
  /**
   * Method 1: Individual decorators (most explicit)
   */
  @Delete(':id')
  removeWithIndividualDecorators(
    @Request() req: AuthenticatedRequest,
    @Param('id') id: string,
    @SessionId() sessionId: string, // Auto-extracted session ID
    @Source() source: ActivitySource, // Auto-detected source
    @IpAddress() ipAddress: string, // Auto-extracted IP
    @UserAgent() userAgent: string, // Auto-extracted User-Agent
  ): Promise<DeleteAccommodationUnitResponseDto> {
    return this.accommodationUnitsService.remove(
      req.user.id,
      req.user.activeBusinessId,
      id,
      {
        sessionId,
        source,
        ipAddress,
        userAgent,
      },
    );
  }

  /**
   * Method 2: RequestContext decorator (recommended - most convenient)
   */
  @Delete(':id')
  removeWithRequestContext(
    @Request() req: AuthenticatedRequest,
    @Param('id') id: string,
    @RequestContext() context: RequestContextType, // All context in one object
  ): Promise<DeleteAccommodationUnitResponseDto> {
    return this.accommodationUnitsService.remove(
      req.user.id,
      req.user.activeBusinessId,
      id,
      context, // Contains: { sessionId, source, ipAddress, userAgent, timestamp }
    );
  }

  /**
   * Method 3: Mixed approach (when you need some manual control)
   */
  @Post()
  createWithMixedApproach(
    @Request() req: AuthenticatedRequest,
    @Body() dto: CreateAccommodationUnitDto,
    @SessionId() sessionId: string,
    @Source() source: ActivitySource,
  ): Promise<AccommodationUnitResponseDto> {
    // Manual extraction for specific needs
    const ipAddress = req.ip || req.connection?.remoteAddress;
    const userAgent = req.headers['user-agent'];

    return this.accommodationUnitsService.create(
      req.user.id,
      req.user.activeBusinessId,
      dto,
      {
        sessionId,
        source,
        ipAddress,
        userAgent,
        customMetadata: {
          createdFromUI: true,
          featureFlag: req.headers['x-feature-flag'],
        },
      },
    );
  }

  /**
   * Method 4: With confidence scoring (for debugging/monitoring)
   */
  @Put(':id')
  updateWithConfidence(
    @Request() req: AuthenticatedRequest,
    @Param('id') id: string,
    @Body() dto: UpdateAccommodationUnitDto,
    @SourceWithConfidence()
    sourceInfo: {
      source: ActivitySource;
      confidence: number;
      method: string;
    },
    @SessionId() sessionId: string,
  ): Promise<AccommodationUnitResponseDto> {
    // Log source detection confidence for monitoring
    if (sourceInfo.confidence < 0.8) {
      this.logger.warn('Low confidence source detection', {
        source: sourceInfo.source,
        confidence: sourceInfo.confidence,
        method: sourceInfo.method,
        userAgent: req.headers['user-agent'],
      });
    }

    return this.accommodationUnitsService.update(
      req.user.id,
      req.user.activeBusinessId,
      id,
      dto,
      {
        sessionId,
        source: sourceInfo.source,
        ipAddress: req.ip,
        userAgent: req.headers['user-agent'],
      },
    );
  }
}

/**
 * Example service showing how to use the context in activity logging
 */
export class AccommodationUnitsService {
  async remove(
    userId: string,
    businessId: string,
    id: string,
    context: RequestContextType, // Use the context from decorator
  ): Promise<DeleteAccommodationUnitResponseDto> {
    // Your business logic here...
    const deletedUnit = await this.findAndDelete(id);

    // Activity logging with full context
    await this.activityLogService.logDelete(
      id,
      EntityType.ACCOMMODATION_UNIT,
      userId,
      businessId,
      {
        source: context.source, // Automatically detected
        sessionId: context.sessionId, // Automatically extracted
        ipAddress: context.ipAddress, // Automatically extracted
        userAgent: context.userAgent, // Automatically extracted
        reason: 'User initiated deletion',
        deleteType: DeleteType.SOFT,
      },
    );

    return { success: true, deletedId: id };
  }

  async create(
    userId: string,
    businessId: string,
    dto: CreateAccommodationUnitDto,
    context: {
      sessionId: string;
      source: ActivitySource;
      ipAddress?: string;
      userAgent?: string;
      customMetadata?: any;
    },
  ): Promise<AccommodationUnitResponseDto> {
    // Your business logic here...
    const createdUnit = await this.createUnit(dto);

    // Activity logging
    await this.activityLogService.logCreate(
      createdUnit.id,
      EntityType.ACCOMMODATION_UNIT,
      Object.keys(dto), // Created fields
      userId,
      businessId,
      {
        source: context.source,
        sessionId: context.sessionId,
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        reason: 'New accommodation unit created',
      },
    );

    return createdUnit;
  }
}
