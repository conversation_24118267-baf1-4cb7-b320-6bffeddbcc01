/**
 * Examples of how clients should send requests to properly set sessionId and source
 */

// ========================================
// 1. WEB CLIENT (React/Vue/Angular)
// ========================================

/**
 * Web application - automatic session management
 */
class WebApiClient {
  private sessionId: string;

  constructor() {
    // Generate session ID when app starts or user logs in
    this.sessionId = this.generateSessionId();
    // Store in localStorage for persistence across page reloads
    localStorage.setItem('sessionId', this.sessionId);
  }

  private generateSessionId(): string {
    const existing = localStorage.getItem('sessionId');
    if (existing) return existing;

    // Generate new session ID
    return `web_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
  }

  async deleteAccommodationUnit(id: string) {
    return fetch(`/api/accommodation-units/${id}`, {
      method: 'DELETE',
      headers: {
        Authorization: `Bearer ${this.getAuthToken()}`,
        'Content-Type': 'application/json',
        'X-Session-Id': this.sessionId, // Session tracking
        'X-Client-Source': 'WEB', // Explicit source
        'User-Agent': navigator.userAgent, // Browser info
      },
    });
  }

  async createAccommodationUnit(data: any) {
    return fetch('/api/accommodation-units', {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${this.getAuthToken()}`,
        'Content-Type': 'application/json',
        'X-Session-Id': this.sessionId,
        'X-Client-Source': 'WEB',
      },
      body: JSON.stringify({
        ...data,
        sessionId: this.sessionId, // Also in body as fallback
      }),
    });
  }
}

// ========================================
// 2. MOBILE CLIENT (React Native/Flutter)
// ========================================

/**
 * Mobile application - session with app lifecycle
 */
class MobileApiClient {
  private sessionId: string;
  private appVersion: string;

  constructor(appVersion: string) {
    this.appVersion = appVersion;
    this.sessionId = this.generateMobileSessionId();
  }

  private generateMobileSessionId(): string {
    // Mobile sessions might be longer-lived
    return `mobile_${Date.now()}_${this.getDeviceId()}`;
  }

  private getDeviceId(): string {
    // In React Native, you might use react-native-device-info
    // return DeviceInfo.getUniqueId();
    return 'device_123'; // Placeholder
  }

  async deleteAccommodationUnit(id: string) {
    return fetch(`/api/accommodation-units/${id}`, {
      method: 'DELETE',
      headers: {
        Authorization: `Bearer ${this.getAuthToken()}`,
        'X-Session-Id': this.sessionId,
        'X-Client-Source': 'MOBILE', // Mobile source
        'X-App-Version': this.appVersion, // App version tracking
        'X-Device-Id': this.getDeviceId(), // Device identification
        'User-Agent': this.getMobileUserAgent(),
      },
    });
  }

  private getMobileUserAgent(): string {
    // Custom mobile user agent
    return `BizC-Mobile/${this.appVersion} (${this.getPlatform()})`;
  }
}

// ========================================
// 3. API CLIENT (Postman/External Integration)
// ========================================

/**
 * API client - programmatic access
 */
class ApiClient {
  private apiKey: string;
  private clientId: string;

  constructor(apiKey: string, clientId: string) {
    this.apiKey = apiKey;
    this.clientId = clientId;
  }

  async deleteAccommodationUnit(id: string) {
    // API clients might not have traditional sessions
    // but can use correlation IDs for request tracking
    const correlationId = `api_${Date.now()}_${this.clientId}`;

    return fetch(`/api/accommodation-units/${id}`, {
      method: 'DELETE',
      headers: {
        'X-API-Key': this.apiKey, // API authentication
        'X-Client-Source': 'API', // Explicit API source
        'X-Session-Id': correlationId, // Correlation ID as session
        'X-Client-Id': this.clientId, // Client identification
        'User-Agent': 'BizC-API-Client/1.0', // API client identifier
      },
    });
  }

  async bulkCreateAccommodationUnits(units: any[]) {
    const correlationId = `api_bulk_${Date.now()}_${this.clientId}`;

    return fetch('/api/accommodation-units/bulk', {
      method: 'POST',
      headers: {
        'X-API-Key': this.apiKey,
        'X-Client-Source': 'API',
        'X-Session-Id': correlationId, // Same session for bulk operation
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        units,
        sessionId: correlationId, // Also in body
        metadata: {
          clientId: this.clientId,
          batchId: `batch_${Date.now()}`,
        },
      }),
    });
  }
}

// ========================================
// 4. ADMIN PANEL (Different Web App)
// ========================================

/**
 * Admin panel - separate application with enhanced tracking
 */
class AdminApiClient {
  private sessionId: string;
  private adminUserId: string;

  constructor(adminUserId: string) {
    this.adminUserId = adminUserId;
    this.sessionId = `admin_${Date.now()}_${adminUserId}`;
  }

  async deleteAccommodationUnit(id: string, reason: string) {
    return fetch(`/api/accommodation-units/${id}`, {
      method: 'DELETE',
      headers: {
        Authorization: `Bearer ${this.getAuthToken()}`,
        'X-Session-Id': this.sessionId,
        'X-Client-Source': 'WEB', // Still web, but admin context
        'X-Admin-Action': 'true', // Flag for admin actions
        'X-Admin-Reason': reason, // Admin-specific metadata
        'User-Agent': this.getAdminUserAgent(),
      },
    });
  }

  private getAdminUserAgent(): string {
    return `${navigator.userAgent} BizC-Admin/1.0`;
  }
}

// ========================================
// 5. CURL/TESTING EXAMPLES
// ========================================

/**
 * Command line examples for testing
 */

// Web simulation
/*
curl -X DELETE "http://localhost:3000/api/accommodation-units/123" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "X-Session-Id: web_1640995200000_abc123" \
  -H "X-Client-Source: WEB" \
  -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
*/

// Mobile simulation
/*
curl -X DELETE "http://localhost:3000/api/accommodation-units/123" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "X-Session-Id: mobile_1640995200000_device123" \
  -H "X-Client-Source: MOBILE" \
  -H "X-App-Version: 1.2.3" \
  -H "User-Agent: BizC-Mobile/1.2.3 (iOS 15.0)"
*/

// API simulation
/*
curl -X DELETE "http://localhost:3000/api/accommodation-units/123" \
  -H "X-API-Key: your-api-key" \
  -H "X-Session-Id: api_1640995200000_client123" \
  -H "X-Client-Source: API" \
  -H "X-Client-Id: integration-client-123" \
  -H "User-Agent: BizC-API-Client/1.0"
*/

// Testing without explicit headers (should auto-detect)
/*
curl -X DELETE "http://localhost:3000/api/accommodation-units/123" \
  -H "Authorization: Bearer your-jwt-token"
  # Will auto-detect as API source from curl user-agent
  # Will generate fallback session ID
*/
