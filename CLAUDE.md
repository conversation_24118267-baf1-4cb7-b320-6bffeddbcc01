# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview
BizC is a cloud-based Enterprise Resource Planning (ERP) system built as a modular, flexible platform for universal business adaptability. The system supports businesses from small startups to enterprise-level operations.

## Development Commands

### Frontend (app.BizC.lk)
```bash
yarn dev          # Development server
yarn build        # Production build
yarn start        # Start production server
yarn lint         # ESLint code checking
```

### Backend (api-bizc-app)
```bash
yarn start:dev    # Development with watch mode
yarn start:debug  # Debug mode with watch
yarn start:prod   # Production mode
yarn build        # Build application
yarn test         # Unit tests
yarn test:e2e     # End-to-end tests
yarn test:cov     # Test coverage
yarn seed         # Database seeding
yarn db:generate  # Generate database migrations
yarn db:migrate   # Run database migrations
yarn db:studio    # Drizzle Studio database GUI
yarn db:push     # Push schema changes
yarn lint         # ESLint with auto-fix
yarn format      # Prettier code formatting
```

## High-Level Architecture

### Technology Stack
- **Frontend**: Next.js 15.1.0 with React 19.0.0, TypeScript, Shadcn UI, Tailwind CSS
- **Backend**: NestJS with TypeScript, PostgreSQL, Drizzle ORM
- **Authentication**: JWT with Passport.js (Google OAuth, Facebook, Local strategies)
- **State Management**: TanStack Query for server state, Zustand for client state
- **Internationalization**: Next-intl (English, Sinhala, Tamil support)

### System Architecture
- **Monorepo Structure**: Contains both frontend (`app.BizC.lk`) and backend (`api-bizc-app`) applications
- **Dual-Mode System**: Admin mode (real-time API data) and Demo mode (mock data) with `isDemo` flag
- **Modular Design**: Business functions separated into distinct modules
- **RESTful API**: Standard REST communication between frontend and backend

### Key Directories

#### Frontend Structure (`/apps/app.BizC.lk/src/`)
- `app/` - Next.js app router pages and API routes
- `components/` - Reusable UI components organized by feature
- `contexts/` - React context providers for shared state
- `hooks/` - Custom React hooks
- `lib/` - Utility libraries and configurations
- `types/` - TypeScript type definitions
- `memory-bank/` - Project documentation and context

#### Backend Structure (`/apps/api-bizc-app/src/`)
- `app/` - Feature modules (accounts, assets, categories, customers, etc.)
- `core/` - Core functionality (guards, filters, interceptors)
- `shared/` - Shared services and utilities
- `common/` - Common enums and filters

## Development Guidelines

### Naming Conventions
- **Components**: PascalCase (e.g., `CategoryForm`, `ProductTable`)
- **Variables/Functions**: camelCase (e.g., `getUserData`, `isLoading`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `API_BASE_URL`)

### Component Architecture
- All components support dual-mode operation using `isDemo` prop
- UI components are separated from business logic
- API calls are abstracted in service layers
- Reusable components follow composition pattern

### Database Operations
- Use Drizzle ORM for type-safe database operations
- Run `yarn db:generate` after schema changes
- Use `yarn db:studio` for visual database management
- Apply migrations with `yarn db:migrate`

### Testing
- Unit tests with Jest: `yarn test`
- End-to-end tests: `yarn test:e2e`
- Test coverage: `yarn test:cov`
- Always run tests before committing changes

### Code Quality
- ESLint configuration with TypeScript support
- Prettier integration for consistent formatting
- Use `yarn lint` to check and auto-fix code issues
- Use `yarn format` for consistent code formatting

## Key Features and Modules

### Business Modules
The system includes comprehensive business modules:
- **Core**: Accounts, Assets, Brands, Categories, Customers
- **Operations**: Inventory, Sales, Purchases, Suppliers
- **Management**: Staff, Projects, Tasks, Vehicles, Equipment
- **Marketing**: Campaigns, SMS, Email services
- **Financial**: Transactions, reporting, analytics
- **AI Integration**: Chat functionality with AI assistance

### Special Features
- **File Upload**: Image processing and Google Cloud Storage integration
- **Bulk Import**: CSV/Excel import functionality
- **Rich Text Editing**: TipTap integration for content creation
- **Data Visualization**: Recharts for analytics and reporting
- **PDF Generation**: jsPDF for document generation
- **Drag and Drop**: @dnd-kit for interactive UI elements

## Memory Bank System
The project uses a sophisticated documentation system in `/memory-bank/`:
- **Project Brief**: Core requirements and business goals
- **Technical Context**: Technology decisions and setup rationale
- **System Patterns**: Architectural decisions and design patterns
- **Progress Tracking**: Current development status and completed features
- **Active Context**: Current work focus and immediate next steps

