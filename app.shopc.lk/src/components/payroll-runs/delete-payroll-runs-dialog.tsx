"use client";

import * as React from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { TrashIcon } from "@radix-ui/react-icons";
import { PayrollRunTableData } from "@/types/payroll-run";
import { ApiStatus } from "@/types/common";
import { useBulkDeletePayrollRuns } from "@/lib/payroll-runs/hooks";
import { toast } from "sonner";

interface DeletePayrollRunsDialogProps {
  payrollRuns: PayrollRunTableData[];
  open: boolean;
  onOpenChange: (open: boolean) => void;
  showTrigger?: boolean;
  onSuccess?: () => void;
  isDemo?: boolean;
}

export function DeletePayrollRunsDialog({
  payrollRuns,
  open,
  onOpenChange,
  showTrigger = true,
  onSuccess,
  isDemo = false,
}: DeletePayrollRunsDialogProps) {
  const [isDeleting, setIsDeleting] = React.useState(false);
  const bulkDeleteMutation = useBulkDeletePayrollRuns(isDemo);

  const handleDelete = async () => {
    if (payrollRuns.length === 0) return;

    setIsDeleting(true);

    try {
      const payrollRunIds = payrollRuns.map((payrollRun) => payrollRun.id);
      const result = await bulkDeleteMutation.mutateAsync(payrollRunIds);

      if (result.status === ApiStatus.SUCCESS) {
        toast.success(
          `Successfully deleted ${payrollRuns.length} payroll run${
            payrollRuns.length > 1 ? "s" : ""
          }`
        );
        onSuccess?.();
        onOpenChange(false);
      } else {
        toast.error(result.message || "Failed to delete payroll runs");
      }
    } catch (error) {
      toast.error("Failed to delete payroll runs");
    } finally {
      setIsDeleting(false);
    }
  };

  const payrollRunCount = payrollRuns.length;
  const isMultiple = payrollRunCount > 1;

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      {showTrigger && (
        <AlertDialogTrigger asChild>
          <Button variant="outline" size="sm">
            <TrashIcon className="mr-2 h-4 w-4" />
            Delete {isMultiple ? `${payrollRunCount} payroll runs` : "payroll run"}
          </Button>
        </AlertDialogTrigger>
      )}
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            Delete {isMultiple ? "Payroll Runs" : "Payroll Run"}
          </AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to delete{" "}
            {isMultiple ? (
              <>
                these <strong>{payrollRunCount} payroll runs</strong>
              </>
            ) : (
              <>
                the payroll run <strong>"{payrollRuns[0]?.runCode}"</strong>
              </>
            )}
            ? This action cannot be undone.
            {isMultiple && (
              <div className="mt-3 p-3 bg-muted rounded-md">
                <p className="text-sm font-medium mb-2">
                  Payroll runs to be deleted:
                </p>
                <ul className="text-sm space-y-1 max-h-32 overflow-y-auto">
                  {payrollRuns.map((payrollRun) => (
                    <li key={payrollRun.id} className="flex items-center gap-2">
                      <span className="w-2 h-2 bg-destructive rounded-full flex-shrink-0" />
                      <span className="truncate">
                        {payrollRun.runCode} - {payrollRun.payrollMonth} {payrollRun.payrollYear}
                      </span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={isDeleting}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {isDeleting ? (
              <>
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                Deleting...
              </>
            ) : (
              <>
                <TrashIcon className="mr-2 h-4 w-4" />
                Delete {isMultiple ? `${payrollRunCount} payroll runs` : "payroll run"}
              </>
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
